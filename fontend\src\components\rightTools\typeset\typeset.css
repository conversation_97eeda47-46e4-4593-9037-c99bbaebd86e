/* 排版工具组件样式 */
.typography-tools {
  width: 100%;
  height: 100%;
  padding: 1px !important;
  box-sizing: border-box;
  overflow-y: auto;
  background: #ffffff;
}

/* 排版区域 */
.typography-section {
  margin-bottom: 1px;
}

.typography-section:last-child {
  margin-bottom: 0;
}

/* 区域标题 */
.typography-section-title {
  font-size: 12px;
}

/* 样式选择容器 */
.typography-style-container {
  display: flex;
  align-items: center;
  gap: 1px;
}

/* 样式选择下拉框 */
.typography-style-select {
  font-size: 12px;
  width: 160px;
  height: 26px;
}

/* 样式选择下拉框选项 */
.typography-style-select option {
  font-size: 12px;
}

/* 样式应用按钮 */
.style-apply-button {
  min-width: 46px !important;
  height: 26px !important;
  font-size: 12px;
  margin-left: 5px;
}

/* 按钮容器 */
.typography-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

/* 通用按钮样式 */
.typography-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  height: 36px;
  border: 1px solid #d4d7de;
  border-radius: 6px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  padding: 0 2px;
  box-sizing: border-box;
  user-select: none;
}

/* 按钮悬停效果 */
.typography-button:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 按钮激活状态 */
.typography-button.active {
  background: #e8f4fd;
  border-color: #4991f2;
  color: #4991f2;
}

/* 按钮文字 */
.typography-button-text {
  font-size: 12px !important;
  color: #333;
  text-align: center;
  line-height: 1;
  font-weight: 500;
  white-space: nowrap;
}

/* 激活状态下的文字颜色 */
.typography-button.active .typography-button-text {
  color: #4991f2;
}

/* ==================== 特定按钮样式 ==================== */

/* 样式按钮 */
/* .style-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: rgb(0, 0, 0);
  border-color: #667eea;
} */


/* .style1-button:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
} */



/* .style2-button:hover {
  background: linear-gradient(135deg, #ee81e9 0%, #f3455a 100%);
  transform: translateY(-1px);
} */

.style1-button,
.style2-button,
.style3-button,
.style4-button {
  color: white !important;
  border-color: #4facfe !important;
  padding: 0px !important;
  width: 60px !important;
  height: 28px !important;
}



.style1-button:hover,
.style2-button:hover,
.style3-button:hover,
.style4-button :hover {
  background: linear-gradient(135deg, #3d9aec 0%, #00e0ec 100%) !important;
  transform: translateY(-1px);
}
.style4-button:hover {
  background: linear-gradient(135deg, #3d9aec 0%, #00e0ec 100%) !important;
  transform: translateY(-1px);

}
.style1-button:hover {
  background: linear-gradient(135deg, #31d769 0%, #26e7c5 100%);
  transform: translateY(-1px);
}


.style1-button .typography-button-text,
.style2-button .typography-button-text,
.style3-button .typography-button-text,
.style4-button .typography-button-text {
  color: rgb(0, 0, 0);
}



/* 字体格式按钮 */
.bold-button.active {
  font-weight: bold;
}

.italic-button.active .typography-button-text {
  font-style: italic;
}

.underline-button.active .typography-button-text {
  text-decoration: underline;
}

.strikeout-button.active .typography-button-text {
  text-decoration: line-through;
}

/* 对齐按钮特殊样式 */
.align-left-button,
.align-center-button,
.align-right-button,
.justify-button {
  min-width: 50px;
}

/* 列表按钮特殊样式 */
.unordered-list-button,
.ordered-list-button {
  min-width: 70px;
}

/* ==================== 图片工具样式 ==================== */

/* 图片工具区域 */
.image-tools-section {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #e2e6ed;
}

/* 图片工具按钮容器 */
.image-tools-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

/* 图片工具按钮 */
.image-tool-button {
  min-width: 60px !important;
  height: 27px !important;
  font-size: 11px !important;
  padding: 0 4px !important;
  border: 1px solid #d4d7de;
  border-radius: 4px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  user-select: none;
}

/* 图片工具按钮悬停效果 */
.image-tool-button:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 图片工具按钮激活状态 */
.image-tool-button.active {
  background: #e8f4fd;
  border-color: #4991f2;
  color: #4991f2;
}

/* 图片工具按钮禁用状态 */
.image-tool-button:disabled {
  background: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.image-tool-button:disabled:hover {
  background: #f5f7fa;
  border-color: #e4e7ed;
  transform: none;
  box-shadow: none;
}

/* 图片工具按钮文字 */
.image-tool-button .typography-button-text {
  font-size: 11px !important;
  color: #333;
  text-align: center;
  line-height: 1;
  font-weight: 500;
  white-space: nowrap;
}

/* 激活状态下的文字颜色 */
.image-tool-button.active .typography-button-text {
  color: #4991f2;
}

/* 禁用状态下的文字颜色 */
.image-tool-button:disabled .typography-button-text {
  color: #c0c4cc;
}

/* ==================== 表格边框工具样式 ==================== */

/* 表格边框工具区域 */
.table-border-tools-section {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #e2e6ed;
}

/* 表格边框工具按钮容器 */
.table-border-tools-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

/* 表格边框工具按钮 */
.table-border-tool-button {
  min-width: 60px !important;
  height: 27px !important;
  font-size: 11px !important;
  padding: 0 4px !important;
  border: 1px solid #d4d7de;
  border-radius: 4px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  user-select: none;
}

/* 表格边框工具按钮悬停效果 */
.table-border-tool-button:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 表格边框工具按钮激活状态 */
.table-border-tool-button.active {
  background: #e8f4fd;
  border-color: #4991f2;
  color: #4991f2;
}

/* 表格边框工具按钮禁用状态 */
.table-border-tool-button:disabled {
  background: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.table-border-tool-button:disabled:hover {
  background: #f5f7fa;
  border-color: #e4e7ed;
  transform: none;
  box-shadow: none;
}

/* 表格边框工具按钮文字 */
.table-border-tool-button .typography-button-text {
  font-size: 11px !important;
  color: #333;
  text-align: center;
  line-height: 1;
  font-weight: 500;
  white-space: nowrap;
}

/* 激活状态下的文字颜色 */
.table-border-tool-button.active .typography-button-text {
  color: #4991f2;
}

/* 禁用状态下的文字颜色 */
.table-border-tool-button:disabled .typography-button-text {
  color: #c0c4cc;
}

/* 响应式设计 */

  .typography-tools {
    padding: 1px;
  }


  .typography-button-text {
    font-size: 12px !important;
  }
