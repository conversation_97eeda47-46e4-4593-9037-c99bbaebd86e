/**
 * 选择API工具函数
 * 用于安全地处理浏览器Selection API，避免getRangeAt错误
 */

/**
 * 安全地获取选择范围
 * @returns Range对象或null
 */
export function getSafeRange(): Range | null {
  try {
    const selection = window.getSelection()
    
    // 检查selection是否存在
    if (!selection) {
      return null
    }
    
    // 检查是否有选择范围
    if (selection.rangeCount === 0) {
      return null
    }
    
    // 安全地获取第一个范围
    return selection.getRangeAt(0)
  } catch (error) {
    console.warn('获取选择范围时发生错误:', error)
    return null
  }
}

/**
 * 检查是否有有效的选择
 * @returns boolean
 */
export function hasValidSelection(): boolean {
  try {
    const selection = window.getSelection()
    return !!(selection && selection.rangeCount > 0 && !selection.isCollapsed)
  } catch (error) {
    console.warn('检查选择状态时发生错误:', error)
    return false
  }
}

/**
 * 安全地获取选择的文本内容
 * @returns 选择的文本或空字符串
 */
export function getSafeSelectionText(): string {
  try {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) {
      return ''
    }
    return selection.toString()
  } catch (error) {
    console.warn('获取选择文本时发生错误:', error)
    return ''
  }
}

/**
 * 安全地清除所有选择
 */
export function safeClearSelection(): void {
  try {
    const selection = window.getSelection()
    if (selection) {
      selection.removeAllRanges()
    }
  } catch (error) {
    console.warn('清除选择时发生错误:', error)
  }
}

/**
 * 安全地设置选择范围
 * @param range 要设置的范围
 */
export function setSafeSelection(range: Range): void {
  try {
    const selection = window.getSelection()
    if (!selection) {
      return
    }
    
    // 先清除现有选择
    selection.removeAllRanges()
    
    // 添加新的范围
    selection.addRange(range)
  } catch (error) {
    console.warn('设置选择范围时发生错误:', error)
  }
}

/**
 * 创建一个安全的Selection处理器类
 * 用于封装所有Selection相关操作
 */
export class SafeSelectionHandler {
  /**
   * 检查当前是否有选择
   */
  public isSelection(): boolean {
    return hasValidSelection()
  }
  
  /**
   * 处理选择变化事件
   * @param callback 回调函数
   */
  public handleSelection(callback: (range: Range | null) => void): void {
    try {
      const range = getSafeRange()
      callback(range)
    } catch (error) {
      console.warn('处理选择事件时发生错误:', error)
      callback(null)
    }
  }
  
  /**
   * 获取选择的边界矩形
   * @returns DOMRect或null
   */
  public getSelectionBounds(): DOMRect | null {
    try {
      const range = getSafeRange()
      if (!range) {
        return null
      }
      return range.getBoundingClientRect()
    } catch (error) {
      console.warn('获取选择边界时发生错误:', error)
      return null
    }
  }
  
  /**
   * 监听选择变化事件
   * @param callback 回调函数
   */
  public onSelectionChange(callback: () => void): () => void {
    const handler = () => {
      // 使用setTimeout确保DOM更新完成
      setTimeout(() => {
        try {
          callback()
        } catch (error) {
          console.warn('选择变化回调执行错误:', error)
        }
      }, 0)
    }
    
    document.addEventListener('selectionchange', handler)
    
    // 返回清理函数
    return () => {
      document.removeEventListener('selectionchange', handler)
    }
  }
}
