import './FormatButton.css'

export class FormatButton {
  private element: HTMLDivElement;
  private command: any;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.format-button') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="format-button" title="清除格式">
      <i></i>
    </div>`
  }

  private bindEvents(): void {
    this.element.onclick = () => {
      console.log('format')
      this.command.executeFormat()
    }
  }
} 