# Canvas Editor 图片工具测试指南

## 📋 测试概述

本文档提供了Canvas Editor右侧工具栏图片工具功能的完整测试指南，确保所有功能正常工作。

## 🎯 测试环境准备

### 1. 启动项目
```bash
# 启动后端服务
cd backend
python start.py 8000

# 启动前端服务
cd fontend
npm run dev
```

### 2. 访问地址
- 前端应用: http://localhost:3000
- 编辑器页面: 直接访问主页面

### 3. 准备测试图片
准备几张不同格式的测试图片：
- PNG格式图片
- JPG格式图片
- GIF格式图片
- WEBP格式图片

## 🧪 功能测试步骤

### 测试1: 图片工具区域显示
**目标**: 验证图片工具区域是否正确显示

**步骤**:
1. 打开Canvas Editor
2. 点击右侧工具栏的"图书编排"tab
3. 点击"排版"子tab
4. 查看是否显示"图片工具"区域

**预期结果**:
- ✅ 显示"图片工具"标题
- ✅ 显示7个图片工具按钮
- ✅ 所有按钮初始状态为禁用（灰色）

### 测试2: 图片插入和选中检测
**目标**: 验证图片选中状态检测功能

**步骤**:
1. 在文档中插入一张图片（通过主菜单的插入→图片）
2. 点击选中刚插入的图片
3. 观察右侧图片工具按钮状态

**预期结果**:
- ✅ 选中图片后，所有图片工具按钮变为可用状态
- ✅ 当前图片布局模式对应的按钮显示为激活状态（蓝色）
- ✅ 点击文档其他位置后，按钮重新变为禁用状态

### 测试3: 更改图片功能
**目标**: 验证图片替换功能

**步骤**:
1. 选中文档中的图片
2. 点击"更改图片"按钮
3. 在弹出的文件选择器中选择新图片
4. 确认选择

**预期结果**:
- ✅ 弹出文件选择对话框
- ✅ 只能选择图片格式文件（.png, .jpg, .jpeg, .gif, .webp）
- ✅ 选择新图片后，文档中的图片被替换
- ✅ 显示"图片已更换"提示消息

### 测试4: 另存为图片功能
**目标**: 验证图片保存功能

**步骤**:
1. 选中文档中的图片
2. 点击"另存为图片"按钮
3. 在浏览器下载提示中确认保存

**预期结果**:
- ✅ 触发浏览器下载
- ✅ 下载的文件名格式正确（图片ID.png）
- ✅ 下载的图片内容与文档中的图片一致
- ✅ 显示"图片已保存"提示消息

### 测试5: 图片布局模式切换
**目标**: 验证所有图片布局模式功能

#### 5.1 嵌入型布局
**步骤**:
1. 选中图片
2. 点击"嵌入型"按钮

**预期结果**:
- ✅ 图片显示为嵌入式布局
- ✅ "嵌入型"按钮显示为激活状态
- ✅ 显示"已设置为嵌入型"提示消息

#### 5.2 上下型环绕
**步骤**:
1. 选中图片
2. 点击"上下型环绕"按钮

**预期结果**:
- ✅ 文字在图片上下方显示
- ✅ "上下型环绕"按钮显示为激活状态
- ✅ 显示"已设置为上下型环绕"提示消息

#### 5.3 四周型环绕
**步骤**:
1. 选中图片
2. 点击"四周型环绕"按钮

**预期结果**:
- ✅ 文字环绕图片四周
- ✅ "四周型环绕"按钮显示为激活状态
- ✅ 显示"已设置为四周型环绕"提示消息

#### 5.4 置文字上方
**步骤**:
1. 选中图片
2. 点击"置文字上方"按钮

**预期结果**:
- ✅ 图片浮动在文字上方
- ✅ "置文字上方"按钮显示为激活状态
- ✅ 显示"已置于文字上方"提示消息

#### 5.5 置文字下方
**步骤**:
1. 选中图片
2. 点击"置文字下方"按钮

**预期结果**:
- ✅ 图片浮动在文字下方
- ✅ "置文字下方"按钮显示为激活状态
- ✅ 显示"已置于文字下方"提示消息

### 测试6: 状态同步测试
**目标**: 验证按钮状态与图片状态的同步

**步骤**:
1. 选中图片
2. 通过右键菜单改变图片布局
3. 观察右侧工具栏按钮状态

**预期结果**:
- ✅ 右键菜单操作后，工具栏按钮状态自动更新
- ✅ 对应的布局按钮显示为激活状态
- ✅ 其他布局按钮恢复为非激活状态

### 测试7: 错误处理测试
**目标**: 验证错误情况的处理

#### 7.1 未选中图片时点击按钮
**步骤**:
1. 确保没有选中任何图片
2. 点击任意图片工具按钮

**预期结果**:
- ✅ 显示"请先选中一张图片"提示消息
- ✅ 不执行任何操作

#### 7.2 选中非图片元素时
**步骤**:
1. 选中文本内容
2. 观察图片工具按钮状态

**预期结果**:
- ✅ 所有图片工具按钮保持禁用状态
- ✅ 按钮显示为灰色不可点击状态

## 🎨 界面测试

### 测试8: 样式和布局测试
**目标**: 验证界面样式和布局

**步骤**:
1. 检查图片工具区域的整体布局
2. 测试按钮的悬停效果
3. 测试按钮的激活状态显示
4. 测试按钮的禁用状态显示

**预期结果**:
- ✅ 区域标题"图片工具"显示正确
- ✅ 按钮排列整齐，间距合适
- ✅ 悬停时按钮有视觉反馈（颜色变化、阴影效果）
- ✅ 激活状态按钮显示为蓝色
- ✅ 禁用状态按钮显示为灰色

### 测试9: 响应式测试
**目标**: 验证不同窗口大小下的显示效果

**步骤**:
1. 调整浏览器窗口大小
2. 观察图片工具区域的布局变化

**预期结果**:
- ✅ 按钮能够自适应容器宽度
- ✅ 小窗口下按钮换行显示正常
- ✅ 文字不会被截断

## 🔧 兼容性测试

### 测试10: 浏览器兼容性
**目标**: 验证不同浏览器下的功能

**测试浏览器**:
- Chrome (推荐)
- Firefox
- Safari
- Edge

**预期结果**:
- ✅ 所有功能在主流浏览器中正常工作
- ✅ 样式显示一致
- ✅ 文件选择器正常工作

### 测试11: 图片格式兼容性
**目标**: 验证不同图片格式的支持

**测试格式**:
- PNG
- JPG/JPEG
- GIF
- WEBP

**预期结果**:
- ✅ 所有支持的格式都能正常更换
- ✅ 不支持的格式被正确过滤
- ✅ 图片显示质量良好

## 📊 性能测试

### 测试12: 大图片处理
**目标**: 验证大尺寸图片的处理能力

**步骤**:
1. 插入大尺寸图片（如4K图片）
2. 测试各种布局模式切换
3. 测试图片保存功能

**预期结果**:
- ✅ 大图片能够正常显示
- ✅ 布局切换响应及时
- ✅ 保存功能正常工作

### 测试13: 多图片操作
**目标**: 验证文档中有多张图片时的功能

**步骤**:
1. 在文档中插入多张图片
2. 依次选中不同图片
3. 测试工具按钮状态更新

**预期结果**:
- ✅ 选中不同图片时按钮状态正确更新
- ✅ 每张图片的布局状态独立管理
- ✅ 操作不会影响其他图片

## ✅ 测试检查清单

### 基础功能
- [ ] 图片工具区域正确显示
- [ ] 图片选中状态检测正常
- [ ] 更改图片功能正常
- [ ] 另存为图片功能正常
- [ ] 所有布局模式切换正常

### 状态管理
- [ ] 按钮禁用/启用状态正确
- [ ] 激活状态显示正确
- [ ] 状态同步正常

### 用户体验
- [ ] 操作提示消息正常显示
- [ ] 界面样式美观
- [ ] 交互反馈及时

### 错误处理
- [ ] 未选中图片时的错误提示
- [ ] 文件选择错误处理
- [ ] 异常情况处理

### 兼容性
- [ ] 浏览器兼容性良好
- [ ] 图片格式支持完整
- [ ] 响应式布局正常

## 🐛 常见问题排查

### 问题1: 按钮一直显示为禁用状态
**可能原因**:
- 图片选中检测逻辑错误
- 事件监听未正确绑定

**排查方法**:
1. 检查浏览器控制台是否有JavaScript错误
2. 确认图片确实被选中（光标在图片上）
3. 检查`isImageSelected()`方法的返回值

### 问题2: 布局切换无效果
**可能原因**:
- Canvas Editor命令执行失败
- 图片元素获取错误

**排查方法**:
1. 检查控制台错误信息
2. 确认图片元素类型正确
3. 验证Canvas Editor版本兼容性

### 问题3: 文件选择器无法打开
**可能原因**:
- 浏览器安全策略限制
- 文件输入元素创建失败

**排查方法**:
1. 检查浏览器是否阻止了弹出窗口
2. 确认在用户交互事件中调用
3. 检查文件类型限制设置

---

*测试指南版本: v1.0*  
*适用于: Canvas Editor v0.9.110+*  
*最后更新: 2025年6月25日*
