/**
 * 认证服务类 - 处理用户认证相关功能
 */

// 用户信息接口
export interface User {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
}

// 会话信息接口
export interface UserSession {
  user: User
  session_token: string
  expires_at: number
  remember_me: boolean
}

/**
 * 认证服务类
 */
export class AuthService {
  private static readonly STORAGE_KEY = 'book_editor_session'
  private static readonly REMEMBER_KEY = 'book_editor_remember'

  /**
   * 保存用户会话信息
   */
  static saveUserSession(user: User, sessionToken: string, expiresIn: number): void {
    const expiresAt = Date.now() + (expiresIn * 1000)

    const session: UserSession = {
      user,
      session_token: sessionToken,
      expires_at: expiresAt,
      remember_me: false
    }

    // 保存到sessionStorage（浏览器关闭后清除）
    sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(session))

    console.log('用户会话已保存:', user.username)
  }

  /**
   * 设置记住我状态
   */
  static setRememberMe(remember: boolean): void {
    const session = this.getCurrentSession()
    if (session) {
      session.remember_me = remember

      if (remember) {
        // 如果选择记住我，同时保存到localStorage
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(session))
        localStorage.setItem(this.REMEMBER_KEY, 'true')
      } else {
        // 清除localStorage中的记住我信息
        localStorage.removeItem(this.STORAGE_KEY)
        localStorage.removeItem(this.REMEMBER_KEY)
      }

      // 更新sessionStorage
      sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(session))
    }
  }

  /**
   * 获取当前用户会话
   */
  static getCurrentSession(): UserSession | null {
    try {
      // 首先尝试从sessionStorage获取
      let sessionData = sessionStorage.getItem(this.STORAGE_KEY)

      // 如果sessionStorage中没有，尝试从localStorage获取（记住我功能）
      if (!sessionData && localStorage.getItem(this.REMEMBER_KEY) === 'true') {
        sessionData = localStorage.getItem(this.STORAGE_KEY)

        // 如果从localStorage恢复了会话，同时保存到sessionStorage
        if (sessionData) {
          sessionStorage.setItem(this.STORAGE_KEY, sessionData)
        }
      }

      if (!sessionData) {
        return null
      }

      const session: UserSession = JSON.parse(sessionData)

      // 检查会话是否过期
      if (Date.now() > session.expires_at) {
        this.clearSession()
        return null
      }

      return session
    } catch (error) {
      console.error('获取用户会话失败:', error)
      this.clearSession()
      return null
    }
  }

  /**
   * 获取当前用户信息
   */
  static getCurrentUser(): User | null {
    const session = this.getCurrentSession()
    return session ? session.user : null
  }

  /**
   * 获取会话令牌
   */
  static getSessionToken(): string | null {
    const session = this.getCurrentSession()
    return session ? session.session_token : null
  }

  /**
   * 检查用户是否已登录
   */
  static isLoggedIn(): boolean {
    return this.getCurrentSession() !== null
  }

  /**
   * 清除用户会话
   */
  static clearSession(): void {
    sessionStorage.removeItem(this.STORAGE_KEY)
    localStorage.removeItem(this.STORAGE_KEY)
    localStorage.removeItem(this.REMEMBER_KEY)
    console.log('用户会话已清除')
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    const sessionToken = this.getSessionToken()

    try {
      // 调用后端登出API
      if (sessionToken) {
        await fetch('/api/auth/logout/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            session_token: sessionToken
          })
        })
      }
    } catch (error) {
      console.error('后端登出失败:', error)
    } finally {
      // 无论后端是否成功，都清除本地会话
      this.clearSession()
    }
  }

  /**
   * 刷新会话（延长过期时间）
   */
  static refreshSession(expiresIn = 86400): void {
    const session = this.getCurrentSession()
    if (session) {
      session.expires_at = Date.now() + (expiresIn * 1000)
      sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(session))

      if (session.remember_me) {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(session))
      }
    }
  }

  /**
   * 检查会话是否即将过期（30分钟内）
   */
  static isSessionExpiringSoon(): boolean {
    const session = this.getCurrentSession()
    if (!session) return false

    const thirtyMinutes = 30 * 60 * 1000
    return (session.expires_at - Date.now()) < thirtyMinutes
  }

  /**
   * 获取用户显示名称
   */
  static getUserDisplayName(): string {
    const user = this.getCurrentUser()
    if (!user) return '未登录'

    if (user.first_name || user.last_name) {
      return `${user.first_name} ${user.last_name}`.trim()
    }

    return user.username
  }

  /**
   * 更新用户信息
   */
  static updateUserInfo(updatedUser: Partial<User>): void {
    const session = this.getCurrentSession()
    if (session) {
      // 合并更新的用户信息
      session.user = { ...session.user, ...updatedUser }

      // 保存更新后的会话
      sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(session))

      if (session.remember_me) {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(session))
      }
    }
  }

  /**
   * 添加认证头到请求
   */
  static addAuthHeader(headers: Record<string, string> = {}): Record<string, string> {
    const sessionToken = this.getSessionToken()
    if (sessionToken) {
      headers['Authorization'] = `Bearer ${sessionToken}`
    }
    return headers
  }

  /**
   * 创建带认证的fetch请求
   */
  static async authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
    const headers = this.addAuthHeader(options.headers as Record<string, string> || {})

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    })

    // 如果返回401，说明会话已过期
    if (response.status === 401) {
      this.clearSession()
      // 重定向到登录页面
      window.location.href = '/login/'
    }

    return response
  }

  /**
   * 初始化认证检查（页面加载时调用）
   */
  static initAuthCheck(): void {
    // 检查当前页面是否需要认证
    const currentPath = window.location.pathname
    const publicPaths = ['/login/', '/register/', '/forgot-password/']

    const isPublicPage = publicPaths.some(path => currentPath.includes(path))

    if (!isPublicPage && !this.isLoggedIn()) {
      // 需要认证但未登录，重定向到登录页面
      console.log('用户未登录，重定向到登录页面')
      window.location.href = '/login/'
      return
    }

    // 如果会话即将过期，显示提醒
    if (this.isSessionExpiringSoon()) {
      console.warn('用户会话即将过期')
      // 这里可以显示续期提醒
    }
  }
}

// 页面加载时自动进行认证检查
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    AuthService.initAuthCheck()
  })
}
