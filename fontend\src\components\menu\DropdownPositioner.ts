/**
 * 下拉框定位器
 * 负责将下拉框定位到页面上的正确位置，使其浮于整个页面之上
 */
export class DropdownPositioner {
  private static instance: DropdownPositioner | null = null

  /**
   * 获取单例实例
   */
  public static getInstance(): DropdownPositioner {
    if (!DropdownPositioner.instance) {
      DropdownPositioner.instance = new DropdownPositioner()
    }
    return DropdownPositioner.instance
  }

  /**
   * 初始化下拉框定位器
   */
  public init(): void {
    this.bindEvents()
    this.setupDropdowns()
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      this.updateAllDropdownPositions()
    })

    // 监听滚动事件
    window.addEventListener('scroll', () => {
      this.updateAllDropdownPositions()
    })
  }

  /**
   * 设置所有下拉框
   */
  private setupDropdowns(): void {
    // 查找所有下拉框触发器
    const dropdownTriggers = document.querySelectorAll('.menu-item__font, .menu-item__size, .menu-item__table, .menu-item__search')
    
    dropdownTriggers.forEach(trigger => {
      this.setupDropdown(trigger as HTMLElement)
    })
  }

  /**
   * 设置单个下拉框
   */
  private setupDropdown(trigger: HTMLElement): void {
    const dropdown = this.findDropdown(trigger)
    if (!dropdown) return

    // 添加定位类
    dropdown.classList.add('dropdown-positioned')

    // 绑定显示事件
    trigger.addEventListener('click', (e) => {
      e.stopPropagation()
      this.showDropdown(trigger, dropdown)
    })

    // 绑定隐藏事件
    document.addEventListener('click', (e) => {
      if (!trigger.contains(e.target as Node) && !dropdown.contains(e.target as Node)) {
        this.hideDropdown(dropdown)
      }
    })
  }

  /**
   * 查找下拉框元素
   */
  private findDropdown(trigger: HTMLElement): HTMLElement | null {
    // 根据触发器类型查找对应的下拉框
    if (trigger.classList.contains('menu-item__font')) {
      return trigger.querySelector('.options')
    }
    if (trigger.classList.contains('menu-item__size')) {
      return trigger.querySelector('.options')
    }
    if (trigger.classList.contains('menu-item__table')) {
      return trigger.querySelector('.menu-item__table__collapse')
    }
    if (trigger.classList.contains('menu-item__search')) {
      return trigger.querySelector('.menu-item__search__collapse')
    }
    return trigger.querySelector('.options')
  }

  /**
   * 显示下拉框
   */
  private showDropdown(trigger: HTMLElement, dropdown: HTMLElement): void {
    // 计算位置
    const position = this.calculatePosition(trigger, dropdown)
    
    // 设置位置
    dropdown.style.position = 'fixed'
    dropdown.style.top = `${position.top}px`
    dropdown.style.left = `${position.left}px`
    dropdown.style.zIndex = '999999'
    
    // 显示下拉框
    dropdown.style.display = 'block'
    
    // 隐藏其他下拉框
    this.hideOtherDropdowns(dropdown)
  }

  /**
   * 隐藏下拉框
   */
  private hideDropdown(dropdown: HTMLElement): void {
    dropdown.style.display = 'none'
  }

  /**
   * 隐藏其他下拉框
   */
  private hideOtherDropdowns(currentDropdown: HTMLElement): void {
    const allDropdowns = document.querySelectorAll('.options, .menu-item__table__collapse, .menu-item__search__collapse')
    allDropdowns.forEach(dropdown => {
      if (dropdown !== currentDropdown) {
        (dropdown as HTMLElement).style.display = 'none'
      }
    })
  }

  /**
   * 计算下拉框位置
   */
  private calculatePosition(trigger: HTMLElement, dropdown: HTMLElement): { top: number; left: number } {
    const triggerRect = trigger.getBoundingClientRect()
    const dropdownRect = dropdown.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    let top = triggerRect.bottom + 2 // 在触发器下方2px，减少距离
    let left = triggerRect.left

    // 检查是否超出右边界
    if (left + dropdownRect.width > viewportWidth) {
      left = viewportWidth - dropdownRect.width - 10
    }

    // 检查是否超出左边界
    if (left < 10) {
      left = 10
    }

    // 检查是否超出下边界
    if (top + dropdownRect.height > viewportHeight) {
      top = triggerRect.top - dropdownRect.height - 5 // 在触发器上方
    }

    // 检查是否超出上边界
    if (top < 10) {
      top = 10
    }

    return { top, left }
  }

  /**
   * 更新所有下拉框位置
   */
  private updateAllDropdownPositions(): void {
    const visibleDropdowns = document.querySelectorAll('.options[style*="display: block"], .menu-item__table__collapse[style*="display: block"], .menu-item__search__collapse[style*="display: block"]')
    
    visibleDropdowns.forEach(dropdown => {
      const trigger = this.findTriggerForDropdown(dropdown as HTMLElement)
      if (trigger) {
        const position = this.calculatePosition(trigger, dropdown as HTMLElement)
        const dropdownEl = dropdown as HTMLElement
        dropdownEl.style.top = `${position.top}px`
        dropdownEl.style.left = `${position.left}px`
      }
    })
  }

  /**
   * 查找下拉框对应的触发器
   */
  private findTriggerForDropdown(dropdown: HTMLElement): HTMLElement | null {
    // 向上查找包含触发器的父元素
    let parent = dropdown.parentElement
    while (parent) {
      if (parent.classList.contains('menu-item__font') || 
          parent.classList.contains('menu-item__size') ||
          parent.classList.contains('menu-item__table') ||
          parent.classList.contains('menu-item__search')) {
        return parent
      }
      parent = parent.parentElement
    }
    return null
  }

  /**
   * 隐藏所有下拉框
   */
  public hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options, .menu-item__table__collapse, .menu-item__search__collapse')
    allDropdowns.forEach(dropdown => {
      (dropdown as HTMLElement).style.display = 'none'
    })
  }

  /**
   * 销毁定位器
   */
  public destroy(): void {
    window.removeEventListener('resize', this.updateAllDropdownPositions)
    window.removeEventListener('scroll', this.updateAllDropdownPositions)
    this.hideAllDropdowns()
  }

  /**
   * 重新初始化所有下拉框
   */
  public reinitialize(): void {
    this.hideAllDropdowns()
    this.setupDropdowns()
  }

  /**
   * 检查下拉框是否在视口内
   */
  private isInViewport(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect()
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= window.innerHeight &&
      rect.right <= window.innerWidth
    )
  }

  /**
   * 获取最佳显示位置
   */
  private getBestPosition(trigger: HTMLElement, dropdown: HTMLElement): 'bottom' | 'top' | 'left' | 'right' {
    const triggerRect = trigger.getBoundingClientRect()
    const dropdownRect = dropdown.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 计算各个方向的可用空间
    const spaceBelow = viewportHeight - triggerRect.bottom
    const spaceAbove = triggerRect.top
    const spaceRight = viewportWidth - triggerRect.right
    const spaceLeft = triggerRect.left

    // 优先选择下方，其次上方，再次右方，最后左方
    if (spaceBelow >= dropdownRect.height) return 'bottom'
    if (spaceAbove >= dropdownRect.height) return 'top'
    if (spaceRight >= dropdownRect.width) return 'right'
    if (spaceLeft >= dropdownRect.width) return 'left'

    // 如果都不够，选择空间最大的方向
    const maxSpace = Math.max(spaceBelow, spaceAbove, spaceRight, spaceLeft)
    if (maxSpace === spaceBelow) return 'bottom'
    if (maxSpace === spaceAbove) return 'top'
    if (maxSpace === spaceRight) return 'right'
    return 'left'
  }
}
