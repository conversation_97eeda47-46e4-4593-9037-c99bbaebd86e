import { Editor, ElementType } from '../../editor'
import { IBlock } from '../../editor/interface/Block'
import { BlockType } from '../../editor/dataset/enum/Block'
import { IElement } from '../../editor/interface/Element'
import { Dialog } from '../dialog/Dialog'
import html from './BlockButton.html'
import './BlockButton.css'

export class BlockButton {
  private dom: HTMLDivElement
  private instance: Editor

  constructor(instance: Editor) {
    this.instance = instance

    // 创建DOM元素
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html
    this.dom = tempDiv.firstElementChild as HTMLDivElement

    // 添加样式确保按钮可见
    this.dom.style.cursor = 'pointer'
    this.dom.setAttribute('title', '内容块')

    // 确保绑定事件
    this.bindEvents()


    // 为现有按钮添加增强点击事件支持
    this.enhanceExistingButton()
  }

  private bindEvents(): void {
    // 使用箭头函数绑定上下文，并改用addEventListener
    this.dom.addEventListener('click', (e) => {
      console.log('内容块按钮被点击')
      e.preventDefault()
      e.stopPropagation()
      this.openBlockDialog()
    })
  }

  /**
   * 增强现有DOM中的内容块按钮，确保可点击性
   * 从debug-helper.ts移植而来的功能
   */
  private enhanceExistingButton(): void {
    // 延迟执行，确保DOM已完全加载
    setTimeout(() => {
      // 查找内容块按钮
      const blockBtn = document.querySelector('.menu-item__block')


      if (blockBtn) {
        // 确保按钮可交互，设置高z-index
        blockBtn.setAttribute('style', 'z-index: 9999 !important;')

        // 直接绑定点击事件
        blockBtn.addEventListener('click', (e) => {
          console.log('内容块按钮被直接点击')
          e.preventDefault()
          e.stopPropagation()

          // 尝试通过API调用插入示例内容块
          try {
            const editorInstance = (window as any).editor
            if (editorInstance && editorInstance.command) {
              editorInstance.command.executeInsertElementList([
                {
                  type: 8, // ElementType.BLOCK
                  value: '',
                  height: 300,
                  block: {
                    type: 'iframe',
                    iframeBlock: {
                      src: 'https://example.com',
                      srcdoc: '<html><body><h1>示例内容块</h1><p>这是一个示例内容</p></body></html>'
                    }
                  }
                }
              ])
              console.log('成功插入内容块')
            } else {
              console.error('找不到编辑器实例或command对象')
            }
          } catch (error) {
            console.error('插入内容块失败:', error)
          }
        })


      }
    }, 2000)
  }

  private openBlockDialog(): void {
    console.log('打开内容块对话框')
    new Dialog({
      title: '内容块',
      data: [
        {
          type: 'select',
          label: '类型',
          name: 'type',
          value: 'iframe',
          required: true,
          options: [
            {
              label: '网址',
              value: 'iframe'
            },
            {
              label: '视频',
              value: 'video'
            }
          ]
        },
        {
          type: 'number',
          label: '宽度',
          name: 'width',
          placeholder: '请输入宽度（默认页面内宽度）'
        },
        {
          type: 'number',
          label: '高度',
          name: 'height',
          required: true,
          placeholder: '请输入高度'
        },
        {
          type: 'input',
          label: '地址',
          name: 'src',
          required: false,
          placeholder: '请输入地址'
        },
        {
          type: 'textarea',
          label: 'HTML',
          height: 100,
          name: 'srcdoc',
          required: false,
          placeholder: '请输入HTML代码（仅网址类型有效）'
        }
      ],
      onConfirm: payload => {
        const type = payload.find(p => p.name === 'type')?.value
        if (!type) return

        const width = payload.find(p => p.name === 'width')?.value
        const height = payload.find(p => p.name === 'height')?.value
        if (!height) return

        // 地址或HTML代码至少存在一项
        const src = payload.find(p => p.name === 'src')?.value
        const srcdoc = payload.find(p => p.name === 'srcdoc')?.value

        const block: IBlock = {
          type: <BlockType>type
        }

        if (block.type === BlockType.IFRAME) {
          if (!src && !srcdoc) return
          block.iframeBlock = {
            src,
            srcdoc
          }
        } else if (block.type === BlockType.VIDEO) {
          if (!src) return
          block.videoBlock = {
            src
          }
        }

        const blockElement: IElement = {
          type: ElementType.BLOCK,
          value: '',
          height: Number(height),
          block
        }

        if (width) {
          blockElement.width = Number(width)
        }

        console.log('插入内容块元素:', blockElement)
        this.instance.command.executeInsertElementList([blockElement])
      }
    })
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}