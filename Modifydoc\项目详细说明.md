# Canvas Editor 项目详细说明

## 🚀 项目概述

Canvas Editor 是一个基于 HTML5 Canvas/SVG 的现代化富文本编辑器，由 Hufe921 开发并开源。该项目提供了类似 Microsoft Word 的所见即所得编辑体验，支持分页显示、丰富的文本格式化、表格操作、图片处理等功能。

### 🌟 核心特性

#### 📝 富文本编辑功能
- **文本格式化**: 字体、字号、颜色、加粗、斜体、下划线、删除线、上标、下标
- **段落样式**: 标题级别、对齐方式、行间距、段落间距
- **列表支持**: 有序列表、无序列表、多级嵌套
- **文本装饰**: 高亮、背景色、文本装饰样式

#### 📊 表格功能
- **表格创建**: 自定义行列数的表格插入
- **表格编辑**: 单元格合并、拆分、行列增删
- **表格样式**: 边框样式、背景色、对齐方式
- **表格工具**: 快速选择行列、表格操作工具栏

#### 🖼️ 多媒体支持
- **图片插入**: 支持多种图片格式，可调整大小和位置
- **图片浮动**: 文字环绕、左右浮动布局
- **LaTeX 公式**: 数学公式编辑和渲染
- **代码块**: 语法高亮的代码编辑

#### 🔍 搜索与导航
- **文本搜索**: 关键词搜索、正则表达式支持
- **搜索替换**: 批量替换功能
- **目录生成**: 自动生成文档目录
- **书签定位**: 快速跳转到指定位置

#### 📄 页面与布局
- **分页模式**: 类 Word 的分页显示
- **连续模式**: 无分页的连续滚动
- **页面设置**: 纸张大小、方向、边距调整
- **页眉页脚**: 自定义页眉页脚内容
- **水印功能**: 文字或图片水印

#### 🎛️ 控件系统
- **表单控件**: 文本框、下拉选择、单选框、复选框
- **日期控件**: 日期选择器
- **数字控件**: 数字输入验证
- **控件验证**: 表单验证规则

#### 🔧 高级功能
- **撤销重做**: 完整的历史记录管理
- **格式刷**: 快速复制格式
- **打印功能**: 基于 Canvas 的高质量打印
- **导入导出**: 支持多种文档格式
- **插件系统**: 可扩展的插件架构
- **国际化**: 多语言支持

## 🛠️ 技术架构

### 核心技术栈
- **TypeScript 4.9+**: 主要开发语言，提供完整的类型安全
- **HTML5 Canvas**: 核心渲染引擎，实现高性能文档渲染
- **SVG**: 矢量图形支持（开发中）
- **Vite 2.4+**: 现代化构建工具，快速开发体验
- **CSS3**: 现代样式系统，支持变量和动画
- **Web APIs**: 充分利用现代浏览器 API

### 依赖库
- **KaTeX**: LaTeX 数学公式渲染
- **Prism.js**: 代码语法高亮
- **Cypress**: 端到端测试框架
- **VitePress**: 文档生成工具

### 开发工具链
- **Node.js 16+**: 开发环境要求
- **npm/yarn**: 包管理器
- **ESLint**: 代码质量检查
- **TypeScript Compiler**: 类型检查和编译
- **Git Hooks**: 代码提交规范

## 📁 项目结构

### 根目录结构
```
canvas-editor/
├── src/                    # 源代码目录
│   ├── editor/            # 编辑器核心
│   ├── components/        # UI 组件
│   ├── assets/           # 静态资源
│   ├── utils/            # 工具函数
│   ├── types/            # 类型定义
│   ├── plugins/          # 插件系统
│   ├── init/             # 初始化模块
│   ├── main.ts           # 应用入口
│   └── style.css         # 全局样式
├── docs/                  # 文档目录
├── cypress/              # 测试文件
├── public/               # 公共资源
├── scripts/              # 构建脚本
├── package.json          # 项目配置
├── tsconfig.json         # TypeScript 配置
├── vite.config.ts        # Vite 配置
└── README.md             # 项目说明
```

### 核心模块详解

#### 1. 编辑器核心 (`src/editor/`)
```
editor/
├── core/                 # 核心功能模块
│   ├── draw/            # 渲染引擎
│   ├── command/         # 命令系统
│   ├── event/           # 事件系统
│   ├── cursor/          # 光标管理
│   ├── range/           # 选区管理
│   ├── history/         # 历史记录
│   ├── position/        # 位置计算
│   ├── listener/        # 事件监听
│   ├── contextmenu/     # 右键菜单
│   ├── shortcut/        # 快捷键
│   ├── i18n/           # 国际化
│   ├── plugin/         # 插件系统
│   ├── register/       # 注册中心
│   ├── observer/       # 观察者模式
│   ├── override/       # 重写机制
│   ├── worker/         # Web Worker
│   ├── zone/           # 区域管理
│   └── actuator/       # 执行器
├── interface/           # 接口定义
├── dataset/            # 数据模型
├── utils/              # 工具函数
├── assets/             # 编辑器资源
└── index.ts            # 编辑器入口
```

#### 2. UI 组件 (`src/components/`)
```
components/
├── menu/               # 顶部菜单栏
├── footer/             # 底部状态栏
├── dialog/             # 对话框组件
├── comment/            # 批注组件
├── signature/          # 签名组件
└── tools/              # 工具组件
```

#### 3. 初始化系统 (`src/init/`)
```
init/
├── index.ts            # 初始化入口
└── initialize.ts       # 初始化逻辑
```

## 🏗️ 核心架构设计

### 1. 编辑器主类 (Editor)
编辑器的主入口类，负责整个编辑器的初始化和生命周期管理。

**主要职责:**
- 数据处理和格式化
- 核心模块初始化
- 事件系统建立
- 插件系统管理
- 销毁和清理

**核心属性:**
- `command`: 命令系统，提供所有编辑操作的 API
- `listener`: 事件监听器，处理编辑器状态变化
- `eventBus`: 事件总线，实现模块间通信
- `override`: 重写系统，允许自定义行为
- `register`: 注册中心，管理右键菜单和快捷键
- `destroy`: 销毁方法，清理资源
- `use`: 插件使用方法

### 2. 渲染引擎 (Draw)
基于 Canvas 的高性能渲染引擎，负责文档的可视化呈现。

**核心功能:**
- Canvas 页面管理
- 元素渲染和布局
- 交互事件处理
- 性能优化

**主要组件:**
- `Position`: 位置计算和管理
- `Range`: 选区管理
- `Cursor`: 光标控制
- `Margin`: 页边距处理
- `Background`: 背景渲染
- `Watermark`: 水印功能
- `Search`: 搜索高亮
- `Highlight`: 文本高亮

### 3. 命令系统 (Command)
提供统一的编辑操作接口，实现撤销重做功能。

**设计模式:**
- 命令模式：每个操作都是一个命令对象
- 适配器模式：CommandAdapt 作为适配器层
- 代理模式：Command 类代理 CommandAdapt 的方法

**主要命令分类:**
- 文本操作：输入、删除、格式化
- 选区操作：选择、复制、粘贴
- 样式操作：字体、颜色、对齐
- 插入操作：表格、图片、公式
- 页面操作：缩放、模式切换

### 4. 事件系统 (EventBus)
基于发布-订阅模式的事件系统，实现模块间的松耦合通信。

**事件类型:**
- `contentChange`: 内容变化
- `rangeStyleChange`: 选区样式变化
- `pageScaleChange`: 页面缩放变化
- `pageSizeChange`: 页面大小变化
- `controlChange`: 控件状态变化

### 5. 数据模型 (Element)
基于 JSON 的轻量级数据结构，描述文档的所有内容。

**元素类型:**
- `TEXT`: 文本元素
- `IMAGE`: 图片元素
- `TABLE`: 表格元素
- `LATEX`: 公式元素
- `CONTROL`: 控件元素
- `BLOCK`: 块级元素
- `PAGE_BREAK`: 分页符

**元素属性:**
- 基础属性：`value`, `type`, `id`
- 样式属性：`font`, `size`, `color`, `bold`, `italic`
- 布局属性：`width`, `height`, `rowFlex`
- 扩展属性：`conceptId`, `extension`

## 🔧 核心功能实现

### 1. 文本渲染
基于 Canvas 2D API 实现高性能文本渲染：
- 字体度量和缓存
- 文本换行算法
- 双向文本支持
- 字符间距调整

### 2. 表格系统
完整的表格编辑功能：
- 动态行列调整
- 单元格合并拆分
- 表格样式设置
- 表格分页处理

### 3. 图片处理
支持多种图片操作：
- 图片加载和缓存
- 尺寸调整和裁剪
- 浮动布局算法
- 图片工具栏

### 4. 搜索功能
强大的文本搜索能力：
- 正则表达式支持
- 搜索结果高亮
- 搜索导航
- 批量替换

### 5. 打印系统
基于 Canvas 的高质量打印：
- 打印预览
- 页面设置
- 打印优化
- PDF 导出

## 📊 性能优化

### 1. 渲染优化
- **虚拟滚动**: 只渲染可见区域
- **增量渲染**: 只重绘变化部分
- **Canvas 缓存**: 缓存复杂元素
- **RAF 调度**: 使用 requestAnimationFrame

### 2. 内存管理
- **对象池**: 复用频繁创建的对象
- **弱引用**: 避免内存泄漏
- **及时清理**: 销毁不需要的资源
- **图片缓存**: 智能图片缓存策略

### 3. 事件优化
- **事件委托**: 减少事件监听器数量
- **防抖节流**: 优化高频事件
- **异步处理**: 避免阻塞主线程
- **Web Worker**: 后台处理复杂计算

## 🔌 插件系统

### 插件架构
Canvas Editor 提供了灵活的插件系统，支持功能扩展：

```typescript
// 插件接口
interface IPlugin {
  name: string
  install(editor: Editor): void
  uninstall?(editor: Editor): void
}

// 使用插件
editor.use(myPlugin)
```

### 官方插件
- **canvas-editor-plugin**: 官方插件集合
- **PDF 导出**: PDF 文档导出功能
- **AI 处理**: AI 驱动的文本处理
- **协同编辑**: 实时协作功能（开发中）

## 🌐 国际化支持

### 多语言配置
```typescript
// 语言配置
const i18nConfig = {
  'zh-CN': {
    'menu.undo': '撤销',
    'menu.redo': '重做'
  },
  'en-US': {
    'menu.undo': 'Undo',
    'menu.redo': 'Redo'
  }
}
```

### 支持语言
- 简体中文 (zh-CN)
- 英语 (en-US)
- 更多语言持续添加中...

## 📱 浏览器兼容性

### 支持的浏览器
- **Chrome**: 80+
- **Firefox**: 75+
- **Safari**: 13+
- **Edge**: 80+

### 移动端支持
- **iOS Safari**: 13+
- **Android Chrome**: 80+
- **移动端优化**: 触摸事件、虚拟键盘适配

## 🚀 快速开始

### 安装
```bash
npm install @hufe921/canvas-editor
```

### 基础使用
```typescript
import Editor from '@hufe921/canvas-editor'

// 创建编辑器实例
const editor = new Editor(
  document.querySelector('.editor-container'),
  {
    main: [{ value: 'Hello Canvas Editor!' }]
  },
  {
    mode: EditorMode.EDIT,
    pageMode: PageMode.PAGING
  }
)
```

### 高级配置
```typescript
const editor = new Editor(container, data, {
  // 编辑模式
  mode: EditorMode.EDIT,
  
  // 页面模式
  pageMode: PageMode.PAGING,
  
  // 页面设置
  width: 794,
  height: 1123,
  margins: [100, 120, 100, 120],
  
  // 样式配置
  defaultFont: 'Microsoft YaHei',
  defaultSize: 14,
  defaultColor: '#000000',
  
  // 功能开关
  watermark: {
    data: 'Canvas Editor',
    opacity: 0.3
  }
})
```

## 📚 API 文档

### 核心 API

#### 内容操作
```typescript
// 获取内容
const content = editor.command.getValue()

// 设置内容
editor.command.setValue(data)

// 插入文本
editor.command.executeInsertElementList([
  { value: 'Hello World' }
])
```

#### 格式化操作
```typescript
// 设置字体
editor.command.executeFont('Arial')

// 设置字号
editor.command.executeSize(16)

// 设置颜色
editor.command.executeColor('#ff0000')

// 加粗
editor.command.executeBold()
```

#### 表格操作
```typescript
// 插入表格
editor.command.executeInsertTable(3, 4)

// 合并单元格
editor.command.executeMergeTableCell()

// 删除表格
editor.command.executeDeleteTable()
```

#### 搜索操作
```typescript
// 搜索文本
editor.command.executeSearch('keyword')

// 替换文本
editor.command.executeReplace('old', 'new')

// 获取搜索信息
const searchInfo = editor.command.getSearchNavigateInfo()
```

### 事件监听
```typescript
// 内容变化
editor.listener.contentChange = () => {
  console.log('Content changed')
}

// 选区样式变化
editor.listener.rangeStyleChange = (style) => {
  console.log('Range style:', style)
}

// 页面变化
editor.listener.intersectionPageNoChange = (pageNo) => {
  console.log('Current page:', pageNo)
}
```

## 🧪 测试

### 测试框架
- **Cypress**: 端到端测试
- **Jest**: 单元测试（计划中）

### 运行测试
```bash
# 打开 Cypress 测试界面
npm run cypress:open

# 运行 Cypress 测试
npm run cypress:run
```

### 测试覆盖
- 基础编辑功能
- 表格操作
- 图片处理
- 搜索替换
- 打印功能

## 🔄 版本历史

### 最新版本 v0.9.110 (2025-05-23)
**新功能:**
- 添加区域隐藏选项
- 添加表单模式规则选项
- 添加组删除选项
- 支持日期控件格式化设置

**Bug 修复:**
- 修复分页表格删除问题
- 修复缩放时浮动图片位置问题

### 主要里程碑
- **v0.9.100**: 表格工具优化，水印渲染层调整
- **v0.9.95**: 区域元素支持，控件状态属性
- **v0.9.90**: 页面边框，单选框/复选框优化
- **v0.9.85**: 设计模式，位置上下文变化执行器
- **v0.9.80**: 图片环绕显示，表格操作 API 分离

## 🛣️ 发展路线图

### 短期目标 (2025 Q2-Q3)
- [ ] **移动端适配**: 完善触摸操作和响应式布局
- [ ] **SVG 渲染**: 完成 SVG 渲染层开发
- [ ] **PDF 导出**: 增强 PDF 导出功能
- [ ] **性能优化**: 大文档渲染性能提升

### 中期目标 (2025 Q4-2026 Q1)
- [ ] **协同编辑**: 实时多人协作功能
- [ ] **CRDT 支持**: 冲突解决算法
- [ ] **云存储**: 云端文档存储和同步
- [ ] **AI 集成**: AI 辅助写作和编辑

### 长期目标 (2026+)
- [ ] **表格分页**: 大表格跨页显示
- [ ] **控件规则**: 表单验证规则引擎
- [ ] **插件市场**: 第三方插件生态
- [ ] **企业版**: 企业级功能和服务

## 🤝 贡献指南

### 参与贡献
我们欢迎所有形式的贡献，包括但不限于：
- 🐛 Bug 报告
- 💡 功能建议
- 📝 文档改进
- 🔧 代码贡献
- 🌐 翻译工作

### 开发流程
1. **Fork 项目**: 从主仓库 fork 到个人账户
2. **创建分支**: 基于 `develop` 分支创建功能分支
3. **开发功能**: 编写代码并添加测试
4. **提交代码**: 遵循提交规范
5. **创建 PR**: 提交 Pull Request
6. **代码审查**: 等待维护者审查
7. **合并代码**: 审查通过后合并

### 提交规范
```bash
# 格式
<type>(<scope>): <subject>

# 示例
feat(editor): 添加表格合并功能
fix(search): 修复搜索高亮显示问题
docs(api): 更新 API 文档
```

### 代码规范
- **TypeScript**: 严格的类型检查
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **注释**: 完整的 JSDoc 注释

## 📞 社区与支持

### 官方资源
- **GitHub**: https://github.com/Hufe921/canvas-editor
- **文档**: https://hufe.club/canvas-editor-docs/
- **演示**: https://hufe.club/canvas-editor/
- **NPM**: https://www.npmjs.com/package/@hufe921/canvas-editor

### 获取帮助
- **Issues**: GitHub Issues 提交问题
- **Discussions**: GitHub Discussions 讨论交流
- **QQ 群**: 加入官方 QQ 交流群
- **微信群**: 扫码加入微信交流群

### 赞助支持
如果这个项目对您有帮助，请考虑赞助支持项目发展：
- **支付宝**: 扫描二维码赞助
- **微信**: 扫描二维码赞助
- **GitHub Sponsors**: GitHub 赞助页面

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

---

**Canvas Editor** - 让文档编辑更简单、更强大！

*Made with ❤️ by Hufe921 and contributors*
