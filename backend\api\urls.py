"""
API 应用的 URL 配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# 创建路由器
router = DefaultRouter()

# 注册视图集
router.register(r'documents', views.DocumentViewSet)

app_name = 'api'

urlpatterns = [
    # API 根路径
    path('', include(router.urls)),

    # 健康检查端点
    path('health/', views.health_check, name='health_check'),

    # 认证相关端点
    path('auth/captcha/', views.CaptchaView.as_view(), name='captcha'),
    path('auth/login/', views.LoginView.as_view(), name='login'),
    path('auth/logout/', views.LogoutView.as_view(), name='logout'),
    path('auth/register/', views.RegisterView.as_view(), name='register'),
    path('auth/profile/', views.ProfileView.as_view(), name='profile'),

    # 其他自定义端点
    # path('custom/', views.custom_view, name='custom'),
]
