::-webkit-scrollbar {
  height: 16px;
  width: 16px;
  overflow: visible
}

::-webkit-scrollbar-button {
  width: 0;
  height: 0
}

::-webkit-scrollbar-corner {
  background: transparent
}

::-webkit-scrollbar-thumb {
  background-color: #ddd;
  background-clip: padding-box;
  border: 4px solid #f2f4f7;
  border-radius: 8px;
  min-height: 24px
}

::-webkit-scrollbar-thumb:hover {
  background-color: #c9c9c9
}

::-webkit-scrollbar-track {
  background: #f2f4f7;
  background-clip: padding-box
}

* {
  margin: 0;
  padding: 0;
}

html, body {
  background-color: #F2F4F7;
  position: relative; /* 确保正常文档流 */
  z-index: auto; /* 默认层级 */
}

ul {
  list-style: none;
}

.menu {
  width: 100%;
  height: 70px; /* 减小菜单高度 */
  top: 0;
  left: 0; /* 确保从左边开始 */
  z-index: 3000; /* 提高菜单基础层级 */
  position: fixed; /* 固定定位，始终在顶部 */
  display: flex;
  flex-direction: column; /* 改为列布局以支持Ribbon */
  background: #F2F4F7;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  pointer-events: auto; /* 确保菜单可以接收鼠标事件 */
}

.menu-divider {
  width: 1px;
  height: 16px;
  margin: 0 8px;
  display: inline-block;
  background-color: #cfd2d8;
}

.menu-item {
  height: 30px; /* 调整高度与面板一致 */
  display: flex;
  align-items: center;
  justify-content: center; /* 添加水平居中 */
  position: relative;
  gap: 0px; /* 添加子元素间距 */
}

.menu-item>div {
  width: 32px; /* 增加按钮宽度 */
  height: 32px; /* 增加按钮高度 */
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0px;
  border-radius: 4px; /* 添加圆角 */
  transition: all 0.2s ease; /* 添加过渡效果 */
}

.menu-item>div:hover {
  background: rgba(25, 55, 88, .04);
}

.menu-item>div.active {
  background: rgba(25, 55, 88, .08);
}

.menu-item i {
  width: 20px; /* 增加图标宽度 */
  height: 20px; /* 增加图标高度 */
  display: inline-block;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.menu-item>div>span {
  width: 16px;
  height: 3px;
  display: inline-block;
  border: 1px solid #e2e6ed;
}

.menu-item .select {
  border: none;
  font-size: 12px;
  line-height: 24px;
  user-select: none;
}

.menu-item .select::after {
  position: absolute;
  content: "";
  top: 11px;
  width: 0;
  height: 0;
  right: 2px;
  border-color: #767c85 transparent transparent;
  border-style: solid solid none;
  border-width: 3px 3px 0;
}

.menu-item .options {
  width: 70px;
  position: absolute;
  left: 0;
  top: 35px; /* 调整位置以适应大图标 */
  padding: 10px;
  background: #fff;
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  border-radius: 2px;
  display: none;
  z-index: 9999; /* 设置为最高层级 */
}

.menu-item .options.visible {
  display: block;
}

.menu-item .options li {
  padding: 5px;
  margin: 5px 0;
  user-select: none;
  transition: all .3s;
}

.menu-item .options li:hover {
  background-color: #ebecef;
}

.menu-item .options li.active {
  background-color: #e2e6ed;
}

.menu-item .menu-item__font {
  width: 65px;
  position: relative;
}

.menu-item .menu-item__size {
  width: 50px;
  text-align: center;
  position: relative;
}

.menu-item__font .select,
.menu-item__size .select {
  width: 100%;
  height: 100%;
}

.menu-item__undo.no-allow,
.menu-item__redo.no-allow,
.menu-item>div.disable {
  color: #c0c4cc;
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}

/* 开始菜单中的四个基础操作按钮 - 使用更大的尺寸 */
.ribbon-panel[data-panel="home"] .menu-item__undo,
.ribbon-panel[data-panel="home"] .menu-item__redo,
.ribbon-panel[data-panel="home"] .menu-item__painter,
.ribbon-panel[data-panel="home"] .menu-item__format {
  width: 40px !important;
  height: 40px !important;
}

.ribbon-panel[data-panel="home"] .menu-item__undo i,
.ribbon-panel[data-panel="home"] .menu-item__redo i,
.ribbon-panel[data-panel="home"] .menu-item__painter i,
.ribbon-panel[data-panel="home"] .menu-item__format i {
  width: 28px !important;
  height: 28px !important;
}

/* 开始菜单基础按钮图标样式 - 修复图标路径 */
.menu-item__undo i {
  background-image: url('./assets/images/undo.svg');
}

.menu-item__redo i {
  background-image: url('./assets/images/redo.svg');
}

.menu-item__painter i {
  background-image: url('./assets/images/painter.svg');
}

.menu-item__format i {
  background-image: url('./assets/images/format.svg');
}

.menu-item__size-add i {
  background-image: url('./assets/images/size-add.svg');
}

.menu-item__size-minus i {
  background-image: url('./assets/images/size-minus.svg');
}

.menu-item__bold i {
  background-image: url('./assets/images/bold.svg');
}

.menu-item__italic i {
  background-image: url('./assets/images/italic.svg');
}

.menu-item .menu-item__underline {
  width: 30px;
  position: relative;
}

.menu-item__underline>i {
  flex-shrink: 0;
  background-image: url('./assets/images/underline.svg');
}

.menu-item__underline .select {
  width: 100%;
  height: 100%;
}

.menu-item .menu-item__underline .options {
  width: 128px;
}

.menu-item .menu-item__underline li {
  padding: 1px 5px;
}

.menu-item__underline li i {
  pointer-events: none;
}

.menu-item__underline li[data-decoration-style="solid"] {
  background-image: url('./assets/images/line-single.svg');
}

.menu-item__underline li[data-decoration-style="double"] {
  background-image: url('./assets/images/line-double.svg')
}

.menu-item__underline li[data-decoration-style="dashed"] {
  background-image: url('./assets/images/line-dash-small-gap.svg');
}

.menu-item__underline li[data-decoration-style="dotted"] {
  background-image: url('./assets/images/line-dot.svg');
}

.menu-item__underline li[data-decoration-style="wavy"] {
  background-image: url('./assets/images/line-wavy.svg');
}

.menu-item__strikeout i {
  background-image: url('./assets/images/strikeout.svg');
}

.menu-item__superscript i {
  background-image: url('./assets/images/superscript.svg');
}

.menu-item__subscript i {
  background-image: url('./assets/images/subscript.svg');
}

.menu-item__color,
.menu-item__highlight {
  display: flex;
  flex-direction: column;
}

.menu-item__color #color,
.menu-item__highlight #highlight {
  width: 1px;
  height: 1px;
  visibility: hidden;
  outline: none;
  appearance: none;
}

.menu-item__color i {
  background-image: url('./assets/images/color.svg');
}

.menu-item__color span {
  background-color: #000000;
}

.menu-item__highlight i {
  background-image: url('./assets/images/highlight.svg');
}

.menu-item__highlight span {
  background-color: #ffff00;
}

.menu-item .menu-item__title {
  width: 60px;
  position: relative;
}

.menu-item__title .select {
  width: calc(100% - 20px);
  height: 100%;
}

.menu-item__title i {
  transform: translateX(-5px);
  background-image: url('./assets/images/title.svg');
}

.menu-item__title .options {
  width: 100px;
}

.menu-item__left i {
  background-image: url('./assets/images/left.svg');
}

.menu-item__center i {
  background-image: url('./assets/images/center.svg');
}

.menu-item__right i {
  background-image: url('./assets/images/right.svg');
}

.menu-item__alignment i {
  background-image: url('./assets/images/alignment.svg');
}

.menu-item__justify i {
  background-image: url('./assets/images/justify.svg');
}

.menu-item__row-margin {
  position: relative;
}

.menu-item__row-margin i {
  background-image: url('./assets/images/row-margin.svg');
}

.menu-item__list {
  position: relative;
}

.menu-item__list i {
  background-image: url('./assets/images/list.svg');
}

.menu-item__list .options {
  width: 110px;
}

.menu-item__list .options>ul>li * {
  pointer-events: none;
}

.menu-item__list .options>ul>li li {
  margin-left: 18px;
}

.menu-item__list .options>ul>li[data-list-style='checkbox'] li::marker {
  font-size: 11px;
}

.menu-item__image i {
  background-image: url('./assets/images/image.svg');
}

.menu-item__image input {
  display: none;
}

.menu-item__table {
  position: relative;
}

/* 表格按钮图标样式 - 修复图标路径 */
.menu-item__table i {
  background-image: url('./assets/images/table.svg');
}

.menu-item .menu-item__table__collapse {
  width: 270px;
  height: 310px;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  box-sizing: border-box;
  border-radius: 2px;
  position: fixed !important; /* 改为fixed定位，浮于整个页面 */
  display: none;
  z-index: 999999 !important; /* 超超高层级，浮于整个页面 */
  top: 85px !important; /* 适应减小的菜单高度 */
  left: auto;
  padding: 14px 27px;
  cursor: auto;
}

.menu-item .menu-item__table__collapse .table-close {
  position: absolute;
  right: 10px;
  top: 5px;
  cursor: pointer;
}

.menu-item .menu-item__table__collapse .table-close:hover {
  color: #7d7e80;
}

.menu-item .menu-item__table__collapse:hover {
  background: #fff;
}

.menu-item .menu-item__table__collapse .table-title {
  display: flex;
  justify-content: flex-start;
  padding-bottom: 5px;
  border-bottom: 1px solid #e2e6ed;
}

.table-title span {
  font-size: 12px;
  color: #3d4757;
  display: inline;
  margin: 0;
}

.table-panel {
  cursor: pointer;
}

.table-panel .table-row {
  display: flex;
  flex-wrap: nowrap;
  margin-top: 10px;
  pointer-events: none;
}

.table-panel .table-cel {
  width: 16px;
  height: 16px;
  box-sizing: border-box;
  border: 1px solid #e2e6ed;
  background: #fff;
  position: relative;
  margin-right: 6px;
  pointer-events: none;
}

.table-panel .table-cel.active {
  border: 1px solid rgba(73, 145, 242, .2);
  background: rgba(73, 145, 242, .15);
}

.table-panel .table-row .table-cel:last-child {
  margin-right: 0;
}

.menu-item__hyperlink i {
  background-image: url('./assets/images/hyperlink.svg');
}

.menu-item__separator {
  position: relative;
}

.menu-item__separator>i {
  background-image: url('./assets/images/separator.svg');
}

.menu-item .menu-item__separator .options {
  width: 128px;
}

.menu-item .menu-item__separator li {
  padding: 1px 5px;
}

.menu-item__separator li i {
  pointer-events: none;
}

.menu-item__separator li[data-separator="0,0"] {
  background-image: url('./assets/images/line-single.svg');
}

.menu-item__separator li[data-separator="1,1"] {
  background-image: url('./assets/images/line-dot.svg');
}

.menu-item__separator li[data-separator="3,1"] {
  background-image: url('./assets/images/line-dash-small-gap.svg');
}

.menu-item__separator li[data-separator="4,4"] {
  background-image: url('./assets/images/line-dash-large-gap.svg');
}

.menu-item__separator li[data-separator="7,3,3,3"] {
  background-image: url('./assets/images/line-dash-dot.svg');
}

.menu-item__separator li[data-separator="6,2,2,2,2,2"] {
  background-image: url('./assets/images/line-dash-dot-dot.svg');
}

.menu-item__watermark>i {
  background-image: url('./assets/images/watermark.svg');
}

.menu-item__watermark {
  position: relative;
}

.menu-item__codeblock i {
  background-image: url('./assets/images/codeblock.svg');
}

.menu-item__page-break i {
  background-image: url('./assets/images/page-break.svg');
}

.menu-item__control {
  position: relative;
}

.menu-item__control i {
  background-image: url('./assets/images/control.svg');
}

.menu-item__checkbox i {
  background-image: url('./assets/images/checkbox.svg');
}

.menu-item__radio i {
  background-image: url('./assets/images/radio.svg');
}

.menu-item__latex i {
  background-image: url('./assets/images/latex.svg');
}

.menu-item__date {
  position: relative;
}

.menu-item__date i {
  background-image: url('./assets/images/date.svg');
}

.menu-item__date .options {
  width: 160px;
}

.menu-item__block i {
  background-image: url('./assets/images/block.svg');
}

.menu-item .menu-item__control .options {
  width: 55px;
}

.menu-item__search {
  position: relative;
}

.menu-item__search i {
  background-image: url('./assets/images/search.svg');
}

.menu-item .menu-item__search__collapse {
  width: 290px;
  height: 122px;
  box-sizing: border-box;
  position: fixed !important; /* 改为fixed定位，浮于整个页面 */
  display: none;
  z-index: 999999 !important; /* 超超高层级，浮于整个页面 */
  top: 85px !important; /* 适应减小的菜单高度 */
  left: auto;
  background: #ffffff;
  box-shadow: 0px 5px 5px #e3dfdf;
  border: 1px solid #e2e6ed; /* 添加边框 */
  border-radius: 2px; /* 添加圆角 */
}

.menu-item .menu-item__search__collapse:hover {
  background: #ffffff;
}

.menu-item .menu-item__search__collapse>div {
  width: 250px;
  height: 36px;
  padding: 0 5px;
  line-height: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px;
}

.menu-item .menu-item__search__collapse>div input {
  width: 205px;
  height: 27px;
  appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #ebebeb;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  line-height: 27px;
  outline: none;
  padding: 0 5px;
}

.menu-item .menu-item__search__collapse>div span {
  height: 100%;
  color: #dcdfe6;
  font-size: 25px;
  display: inline-block;
  border: 0;
  padding: 0 10px;
}

.menu-item .menu-item__search__collapse__replace button {
  display: inline-block;
  border: 1px solid #e2e6ed;
  border-radius: 2px;
  background: #fff;
  line-height: 22px;
  padding: 0 6px;
  white-space: nowrap;
  margin-left: 4px;
  cursor: pointer;
  font-size: 12px;
}

.menu-item .menu-item__search__collapse__replace button:hover {
  background: rgba(25, 55, 88, .04);
}

.menu-item .menu-item__search__collapse__search {
  position: relative;
}

.menu-item .menu-item__search__collapse__search label {
  right: 110px;
  font-size: 12px;
  color: #3d4757;
  position: absolute;
}

.menu-item .menu-item__search__collapse__search>input {
  padding: 5px 90px 5px 5px !important;
}

.menu-item .menu-item__search__collapse__search>div {
  width: 28px;
  height: 27px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  border-left: 1px solid #e2e6ed;
  transition: all .5s;
}

.menu-item .menu-item__search__collapse__search>div:hover {
  background-color: rgba(25, 55, 88, .04);
}

.menu-item .menu-item__search__collapse__search i {
  width: 6px;
  height: 8px;
  transform: translateY(1px);
}

.menu-item .menu-item__search__collapse__search .arrow-left {
  right: 76px;
}

.menu-item .menu-item__search__collapse__search .arrow-left i {
  background: url(./assets/images/arrow-left.svg) no-repeat;
}

.menu-item .menu-item__search__collapse__search .arrow-right {
  right: 48px;
}

.menu-item .menu-item__search__collapse__search .arrow-right i {
  background: url(./assets/images/arrow-right.svg) no-repeat;
}

.menu-item__print i {
  background-image: url('./assets/images/print.svg');
}

.editor {
  margin-top: 80px; /* 调整顶部边距以适应减小的菜单高度 */
  position: relative; /* 确保编辑器在正常文档流中 */
  z-index: 1; /* 设置较低的z-index，确保菜单在上面 */
}

.editor>div {
  margin: auto;
  width: calc(100% - 380px);
  box-sizing: border-box;
}

.ce-page-container canvas {
  box-shadow: rgb(158 161 165 / 40%) 0px 2px 12px 0px;
}

/* 评论相关样式已移至src/components/menu/CommentButton.css */

.ce-contextmenu-signature {
  background-image: url('./assets/images/signature.svg');
}

.ce-contextmenu-word-tool {
  background-image: url('./assets/images/word-tool.svg');
}

.menu[data-initialized="true"] {
  opacity: 1;
  transition: opacity 0.2s ease-in-out;
}

.menu:not([data-initialized="true"]) {
  opacity: 0;
}

/* ==================== 下拉框层级修复 ==================== */

/* 确保所有菜单下拉框都浮于整个页面之上 */
.menu-item .options,
.menu-item .menu-item__search__collapse,
.menu-item .menu-item__underline .options {
  z-index: 999999 !important; /* 超超高层级，浮于整个页面 */
  position: fixed !important; /* 改为fixed定位，相对于整个页面 */
  top: 85px !important; /* 适应减小的菜单高度 */
}

/* 使用智能定位的按钮下拉框，不使用固定top值 */
.menu-item .menu-item__title .options,
.menu-item .menu-item__new-title .options,
.menu-item .menu-item__home-font .options,
.menu-item .menu-item__home-font-size .options,
.menu-item .menu-item__home-table .table-panel,
.menu-item .menu-item__home-margin .options,
.menu-item .menu-item__home-orientation .options,
.menu-item .menu-item__row-margin .options,
.menu-item .menu-item__list .options,
.menu-item .menu-item__table__collapse,
.menu-item .menu-item__control .options,
.menu-item .menu-item__date .options,
.menu-item .menu-item__separator .options,
.menu-item .menu-item__watermark .options {
  z-index: 999999 !important; /* 超超高层级，浮于整个页面 */
  position: fixed !important; /* 改为fixed定位，相对于整个页面 */
  /* 不设置固定的top值，由JavaScript动态计算 */
}

/* 字体和字号下拉框特殊强化 - 确保绝对最高层级 */
.menu-item .menu-item__font .options,
.menu-item .menu-item__size .options {
  z-index: 999999 !important; /* 超超高层级，浮于整个页面 */
  position: fixed !important; /* 改为fixed定位，相对于整个页面 */
  top: 85px !important; /* 适应减小的菜单高度 */
  left: auto !important;
  background: #fff !important;
  border: 1px solid #e2e6ed !important;
  border-radius: 2px !important;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15) !important;
  display: none;
  min-width: 70px;
  max-height: 200px;
  overflow-y: auto;
  pointer-events: auto !important; /* 确保可以点击 */
}

/* 字体和字号下拉框显示状态 */
.menu-item .menu-item__font.active .options,
.menu-item .menu-item__size.active .options,
.menu-item .menu-item__font:hover .options,
.menu-item .menu-item__size:hover .options {
  display: block !important;
  z-index: 999999 !important;
}

/* 下拉框动态定位辅助类 */
.dropdown-positioned {
  position: fixed !important;
  z-index: 999999 !important;
}

/* ==================== Ribbon菜单样式 ==================== */

/* Ribbon菜单容器 */
.ribbon-menu {
  width: 100%;
  height: 40px;
  display: flex;
  flex-direction: column;
  z-index: 99999; /* 置于最顶层 */
  position: relative; /* 确保z-index生效 */
}

/* 选项卡标题栏 */
.ribbon-tabs {
  display: flex;
  background: #E1E5E9;
  border-bottom: 1px solid #C7CDD3;
  height: 30px;
  align-items: center;
  justify-content: center; /* 添加水平居中 */
  padding: 0 20px; /* 改为左右对称内边距 */
  gap: 4px; /* 添加标签间距 */
  flex-shrink: 0;
  z-index: 99999; /* 置于最顶层 */
  position: relative; /* 确保z-index生效 */
}

/* 选项卡标题 */
.ribbon-tab {
  padding: 6px 16px;
  cursor: pointer;
  font-size: 12px;
  color: #3D4757;
  border-radius: 3px 3px 0 0;
  margin-right: 2px;
  transition: all 0.2s ease;
  user-select: none;
  white-space: nowrap;
}

.ribbon-tab:hover {
  background: rgba(255, 255, 255, 0.5);
}

.ribbon-tab.active {
  background: #F2F4F7;
  color: #1F2937;
  font-weight: 500;
  border: 1px solid #C7CDD3;
  border-bottom: 1px solid #F2F4F7;
}

/* 选项卡内容区域 */
.ribbon-content {
  flex: 1;
  background: #F2F4F7;
  position: relative;
  overflow: visible; /* 改为visible以显示下拉框 */
  max-height: 40px; /* 调整最小高度 */
  z-index: 99999; /* 置于最顶层 */
}

/* 选项卡面板 */
.ribbon-panel {
  display: none;
  height: 40px;
  padding: 0px 0px; /* 增加左右内边距 */
  flex-wrap: nowrap;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  overflow-x: auto;
  overflow-y: visible; /* 改为visible以显示下拉框 */
  z-index: 99999; /* 置于最顶层 */
  position: relative; /* 确保z-index生效 */
}

.ribbon-panel.active {
  display: flex;
}

/* 功能组 */
.ribbon-group {
  display: flex;
  flex-direction: column;
  margin: 0 2px; /* 改为左右对称边距 */
  min-width: auto;
  position: relative;
  height: 30px;
  flex-shrink: 0;
  align-items: center; /* 内容居中对齐 */
  justify-content: center; /* 垂直居中 */
}

.ribbon-group:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 5px;
  bottom: 5px;
  width: 1px;
  background: #C7CDD3;
}

/* 功能组标题 - 隐藏 */
.ribbon-group-header {
  display: none;
}

/* 功能组内容 */
.ribbon-group-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  padding: 1px 0;
  width: 100%; /* 确保占满宽度 */
}

/* 单排布局 - 统一的按钮排列方式 */
.ribbon-single-row {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  height: 30px; /* 调整高度与面板一致 */
  width: 100%; /* 确保占满宽度 */
}

/* 保留原有布局类以防兼容性问题 */
.ribbon-large-buttons,
.ribbon-small-buttons,
.ribbon-font-row,
.ribbon-font-style,
.ribbon-paragraph-row,
.ribbon-align-row,
.ribbon-insert-row,
.ribbon-page-row,
.ribbon-control-row {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  height: 50px;
}

/* 布局信息 */
.ribbon-layout-info {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #6B7280;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .ribbon-group {
    margin-right: 10px;
  }

  .ribbon-single-row {
    gap: 3px;
  }
}

@media (max-width: 1200px) {
  .ribbon-group {
    margin-right: 8px;
  }

  .ribbon-single-row {
    gap: 2px;
    flex-wrap: wrap;
    height: auto;
    min-height: 50px;
  }

  /* 在中等屏幕上稍微缩小按钮 */
  .menu-item>div {
    width: 28px;
    height: 28px;
  }

  .menu-item i {
    width: 18px;
    height: 18px;
  }

  /* 开始菜单的四个按钮在中等屏幕上保持较大尺寸 */
  .ribbon-panel[data-panel="home"] .menu-item__undo,
  .ribbon-panel[data-panel="home"] .menu-item__redo,
  .ribbon-panel[data-panel="home"] .menu-item__painter,
  .ribbon-panel[data-panel="home"] .menu-item__format {
    width: 36px !important;
    height: 36px !important;
  }

  .ribbon-panel[data-panel="home"] .menu-item__undo i,
  .ribbon-panel[data-panel="home"] .menu-item__redo i,
  .ribbon-panel[data-panel="home"] .menu-item__painter i,
  .ribbon-panel[data-panel="home"] .menu-item__format i {
    width: 24px !important;
    height: 24px !important;
  }
}

@media (max-width: 900px) {
  .ribbon-panel {
    overflow-x: auto;
    padding: 8px 5px;
  }

  .ribbon-group {
    flex-shrink: 0;
    margin-right: 5px;
  }

  .ribbon-single-row {
    flex-wrap: nowrap;
    height: 50px;
    gap: 2px;
  }

  /* 在小屏幕上进一步缩小按钮 */
  .menu-item>div {
    width: 26px;
    height: 26px;
  }

  .menu-item i {
    width: 16px;
    height: 16px;
  }

  /* 开始菜单的四个按钮在小屏幕上保持较大尺寸 */
  .ribbon-panel[data-panel="home"] .menu-item__undo,
  .ribbon-panel[data-panel="home"] .menu-item__redo,
  .ribbon-panel[data-panel="home"] .menu-item__painter,
  .ribbon-panel[data-panel="home"] .menu-item__format {
    width: 32px !important;
    height: 32px !important;
  }

  .ribbon-panel[data-panel="home"] .menu-item__undo i,
  .ribbon-panel[data-panel="home"] .menu-item__redo i,
  .ribbon-panel[data-panel="home"] .menu-item__painter i,
  .ribbon-panel[data-panel="home"] .menu-item__format i {
    width: 20px !important;
    height: 20px !important;
  }

  /* 调整下拉框在小屏幕的大小 */
  .menu-item .menu-item__font {
    width: 70px;
    height: 26px;
  }

  .menu-item .menu-item__size {
    width: 50px;
    height: 26px;
  }
}

/* 新字体按钮全局样式覆盖 - 确保浮于最上层 */
.new-font-button {
  position: relative !important;
  z-index: 1000 !important;
}

.new-font-button .options {
  position: fixed !important;
  z-index: 999999 !important;
  pointer-events: none !important;
  transform: translateY(-10px) !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: all 0.2s ease !important;
}

.new-font-button .options.visible {
  pointer-events: auto !important;
  transform: translateY(0) !important;
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 999999 !important;
}

/* 确保新字体按钮不被其他元素遮盖 */
.menu-item .menu-item__new-font {
  width: 120px !important;
  position: relative !important;
  z-index: 1000 !important;
}

/* 防止其他下拉框干扰 */
.menu-item .options:not(.new-font-button .options):not(.new-font-size-button .options) {
  z-index: 9999 !important;
}

/* 新字号按钮全局样式覆盖 - 确保浮于最上层 */
.new-font-size-button {
  position: relative !important;
  z-index: 1000 !important;
}

.new-font-size-button .options {
  position: fixed !important;
  z-index: 999999 !important;
  pointer-events: none !important;
  transform: translateY(-10px) !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: all 0.2s ease !important;
}

.new-font-size-button .options.visible {
  pointer-events: auto !important;
  transform: translateY(0) !important;
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 999999 !important;
}

/* 确保新字号按钮不被其他元素遮盖 */
.menu-item .menu-item__new-font-size {
  width: 80px !important;
  position: relative !important;
  z-index: 1000 !important;
}

/* ==================== 段落菜单独立标题按钮样式 ==================== */

/* 正文按钮样式 */
.menu-item .menu-item__normal-text {
  width: 50px !important;
  height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  border: none !important;
  outline: none !important;
  border-radius: 4px !important;
  transition: background-color 0.2s ease !important;
  font-size: 12px !important;
  color: #333 !important;
  user-select: none !important;
  /* padding: 0 2px !important; */
  box-sizing: border-box !important;
  padding-top: 10px !important;
}

/* 标题1-6按钮样式 */
.menu-item .menu-item__title1,
.menu-item .menu-item__title2,
.menu-item .menu-item__title3,
.menu-item .menu-item__title4,
.menu-item .menu-item__title5,
.menu-item .menu-item__title6 {
  height: 30px !important;
  width: 70px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  border-radius: 4px !important;
  transition: background-color 0.2s ease !important;
  color: #333 !important;
  user-select: none !important;
  font-weight: bold !important;
  padding: 0 2px !important;
  box-sizing: border-box !important;
  padding-top: 6px  !important;

}

/* 标题按钮悬停效果 */
.menu-item__normal-text:hover,
.menu-item__title1:hover,
.menu-item__title2:hover,
.menu-item__title3:hover,
.menu-item__title4:hover,
.menu-item__title5:hover,
.menu-item__title6:hover {
  background-color: #e8f4fd !important;
  border: none !important;
  outline: none !important;
}

/* 标题按钮激活状态 */
.menu-item__normal-text.active,
.menu-item__title1.active,
.menu-item__title2.active,
.menu-item__title3.active,
.menu-item__title4.active,
.menu-item__title5.active,
.menu-item__title6.active {
  background-color: #d1e7dd !important;
  border: none !important;
  outline: none !important;
}

/* 标题按钮文字样式 */
.menu-item__normal-text .button-text {
  font-size: 12px !important;
  width: 40px;
  height: 30px;
  padding: 20px,0px,0px,0px;
  font-weight: normal !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  line-height: 1 !important;
}

.menu-item__title1 .button-text,
.menu-item__title2 .button-text,
.menu-item__title3 .button-text,
.menu-item__title4 .button-text,
.menu-item__title5 .button-text,
.menu-item__title6 .button-text {
  font-size: 12px !important;
  width: 40px;
  height: 30px;
  border: none !important;
  font-weight: bold !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  line-height: 1 !important;
}
.menu-item .menu-item__title1,
.menu-item__title1 .button-text{
  font-size: 22px !important;
  width: 70px;
  padding-top: 6px  !important;
  padding-left: 5px;

}
.menu-item .menu-item__title2,
.menu-item__title2 .button-text{
  font-size: 20px !important;
  width: 70px;
  padding-top: 7px  !important;
  padding-left: 8px;

}
.menu-item .menu-item__title3,
.menu-item__title3 .button-text{
  font-size: 18px !important;
  width: 70px;
  padding-top: 9px  !important;
  padding-left: 11px;

}
.menu-item .menu-item__title4,
.menu-item__title4 .button-text{
  font-size: 16px !important;
  width: 70px;
  padding-top: 11px  !important;
  padding-left: 13px;

}
.menu-item .menu-item__title5,
.menu-item__title5 .button-text{
  font-size: 14px !important;
  width: 70px;
  padding-top: 12px  !important;
  padding-left: 15px;

}
.menu-item .menu-item__title6,
.menu-item__title6 .button-text{
  font-size: 12px !important;
  width: 70px;
  padding-top: 13px  !important;
  padding-left: 17px;

}

.menu-item .menu-item__normal-text,
.menu-item__normal-text .button-text{
  font-size: 18px !important;
  width: 70px;
  font-weight: bold !important;
  padding-top: 8px  !important;
  padding-left: 2px;
  border: none !important;
  outline: none !important;
}

/* 段落菜单正文按钮边框强制移除 - 最高优先级 */
.ribbon-group .menu-item .menu-item__normal-text,
.ribbon-group .menu-item .menu-item__normal-text:hover,
.ribbon-group .menu-item .menu-item__normal-text.active,
.ribbon-group .menu-item .menu-item__normal-text:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 布局菜单页面缩放组件样式 */
.ribbon-group .menu-item .page-scale-minus,
.ribbon-group .menu-item .page-scale-add {
  width: 24px;
  height: 30px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  border: none !important;
  outline: none !important;
  box-sizing: border-box;
  vertical-align: middle;
}

/* 页面缩放按钮悬停效果 */
.ribbon-group .menu-item .page-scale-minus:hover,
.ribbon-group .menu-item .page-scale-add:hover {
  background-color: #e8f4fd !important;
  border: none !important;
  outline: none !important;
}

/* 页面缩放按钮激活状态 */
.ribbon-group .menu-item .page-scale-minus.active,
.ribbon-group .menu-item .page-scale-add.active {
  background-color: #d1e7dd !important;
  border: none !important;
  outline: none !important;
}

/* 页面缩放按钮图标样式 - 修复图标路径 */
.ribbon-group .menu-item .page-scale-minus i {
  width: 16px;
  height: 16px;
  display: inline-block;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-image: url('./assets/images/page-scale-minus.svg');
}

.ribbon-group .menu-item .page-scale-add i {
  width: 16px;
  height: 16px;
  display: inline-block;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-image: url('./assets/images/page-scale-add.svg');
}

/* 页面缩放百分比显示 */
.ribbon-group .menu-item .page-scale-percentage {
  width: 50px;
  height: 30px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: 1px solid #d4d7de !important;
  outline: none !important;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  font-size: 12px;
  color: #333;
  font-weight: normal;
  user-select: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1;
  text-align: center;
  vertical-align: middle;
}

/* 页面缩放百分比悬停效果 */
.ribbon-group .menu-item .page-scale-percentage:hover {
  background-color: #e8f4fd !important;
  border-color: #c0c4cc !important;
  transform: scale(1.02);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 页面缩放百分比激活状态 */
.ribbon-group .menu-item .page-scale-percentage.active {
  background-color: #d1e7dd !important;
  border-color: #409eff !important;
}

/* 页面缩放百分比点击效果 */
.ribbon-group .menu-item .page-scale-percentage:active {
  background-color: #d1e7dd !important;
  border-color: #409eff !important;
  transform: scale(0.98);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 禁用状态 */
.ribbon-group .menu-item .page-scale-minus.disabled,
.ribbon-group .menu-item .page-scale-add.disabled {
  color: #c0c4cc;
  cursor: not-allowed;
  opacity: 0.4;
  pointer-events: none;
}

.ribbon-group .menu-item .page-scale-minus.disabled i,
.ribbon-group .menu-item .page-scale-add.disabled i {
  opacity: 0.4;
}

/* ==================== 开始菜单标题按钮样式 ==================== */

/* 开始菜单正文按钮样式 */
.menu-item .menu-item__home-normal-text {
  height: 30px !important;
  width: 50px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  cursor: pointer !important;
  border-radius: 0 !important;
  transition: background-color 0.2s ease !important;
  color: #333 !important;
  user-select: none !important;
  font-weight: bold !important;
  padding: 0 2px !important;
  box-sizing: border-box !important;
  padding-top: 6px !important;
}

/* 开始菜单标题1-6按钮样式 */
.menu-item .menu-item__home-title1,
.menu-item .menu-item__home-title2,
.menu-item .menu-item__home-title3,
.menu-item .menu-item__home-title4,
.menu-item .menu-item__home-title5,
.menu-item .menu-item__home-title6 {
  height: 30px !important;

  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  border-radius: 4px !important;
  transition: background-color 0.2s ease !important;
  color: #333 !important;
  user-select: none !important;
  font-weight: bold !important;
  padding: 0 2px !important;
  box-sizing: border-box !important;
  padding-top: 6px  !important;
}

* 正文按钮样式 */
.menu-item .normal-item__normal-text {
  width: 50px !important;
  height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  border-radius: 4px !important;
  transition: background-color 0.2s ease !important;
  font-size: 12px !important;
  color: #333 !important;
  user-select: none !important;
  /* padding: 0 2px !important; */
  box-sizing: border-box !important;
  padding-top: 10px !important;
}


/* 开始菜单标题按钮悬停效果 */
.menu-item .menu-item__home-normal-text:hover,
.menu-item .menu-item__home-title1:hover,
.menu-item .menu-item__home-title2:hover,
.menu-item .menu-item__home-title3:hover,
.menu-item .menu-item__home-title4:hover,
.menu-item .menu-item__home-title5:hover,
.menu-item .menu-item__home-title6:hover {
  background-color: #e8f4fd !important;
  border: none !important;
  outline: none !important;
}

/* 开始菜单标题按钮激活状态 */
.menu-item .menu-item__home-normal-text.active,
.menu-item .menu-item__home-title1.active,
.menu-item .menu-item__home-title2.active,
.menu-item .menu-item__home-title3.active,
.menu-item .menu-item__home-title4.active,
.menu-item .menu-item__home-title5.active,
.menu-item .menu-item__home-title6.active {
  background-color: #d1e7dd !important;
  border: none !important;
  outline: none !important;
}

/* 开始菜单标题按钮文字样式 */
.menu-item__home-normal-text .button-text {
  font-size: 12px !important;
  width: 40px;
  height: 30px;
  font-weight: normal !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  line-height: 1 !important;
}

.menu-item__home-title1 .button-text,
.menu-item__home-title2 .button-text,
.menu-item__home-title3 .button-text,
.menu-item__home-title4 .button-text,
.menu-item__home-title5 .button-text,
.menu-item__home-title6 .button-text {
  font-size: 12px !important;
  width: 40px;
  height: 30px;
  font-weight: bold !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  line-height: 1 !important;
}

/* 开始菜单按钮覆盖通用样式 */
.menu-item .menu-item__home-normal-text,
.menu-item .menu-item__home-title1,
.menu-item .menu-item__home-title2,
.menu-item .menu-item__home-title3,
.menu-item .menu-item__home-title4,
.menu-item .menu-item__home-title5,
.menu-item .menu-item__home-title6 {
  border: none !important;
  outline: none !important;
  background: transparent;
  box-shadow: none !important;
}

/* 开始菜单各级标题按钮的特定样式 */
.menu-item .menu-item__home-title1,
.menu-item__home-title1 .button-text {
  font-size: 22px !important;
  width: 80px !important;
  padding-top: 5px !important;
  border: none !important;
  outline: none !important;
}

.menu-item .menu-item__home-title2,
.menu-item__home-title2 .button-text {
  font-size: 20px !important;
  width: 68px !important;
  padding-top: 7px !important;
  border: none !important;
  outline: none !important;
}

.menu-item .menu-item__home-title3,
.menu-item__home-title3 .button-text {
  font-size: 18px !important;
  width: 63px !important;
  padding-top: 9px !important;
  border: none !important;
  outline: none !important;
}

.menu-item .menu-item__home-title4,
.menu-item__home-title4 .button-text {
  font-size: 16px !important;
  width: 58px !important;
  padding-top: 10px !important;
  border: none !important;
  outline: none !important;
}

.menu-item .menu-item__home-title5,
.menu-item__home-title5 .button-text {
  font-size: 14px !important;
  width: 53px !important;
  padding-top: 11px !important;
  border: none !important;
  outline: none !important;
}

.menu-item .menu-item__home-title6,
.menu-item__home-title6 .button-text {
  font-size: 12px !important;
  width: 48px !important;
  padding-top: 12px !important;
  border: none !important;
  outline: none !important;
}

.menu-item .menu-item__home-normal-text,
.menu-item__home-normal-text .button-text {
  font-size: 18px !important;
  width: 70px;
  font-weight: bold !important;
  padding-top: 8px !important;
  padding-left: 2px;
  border: none !important;
  outline: none !important;
}

/* ==================== 开始菜单标题按钮响应式样式 ==================== */

/* 中等屏幕适配 - 保持字体大小不变 */
@media (max-width: 1200px) {
  .menu-item .menu-item__home-normal-text,
  .menu-item .menu-item__home-title1,
  .menu-item .menu-item__home-title2,
  .menu-item .menu-item__home-title3,
  .menu-item .menu-item__home-title4,
  .menu-item .menu-item__home-title5,
  .menu-item .menu-item__home-title6 {
    width: 60px !important;
    border: none !important;
    outline: none !important;
  }

  .menu-item__home-normal-text .button-text,
  .menu-item__home-title1 .button-text,
  .menu-item__home-title2 .button-text,
  .menu-item__home-title3 .button-text,
  .menu-item__home-title4 .button-text,
  .menu-item__home-title5 .button-text,
  .menu-item__home-title6 .button-text {
    width: 35px;
  }

  /* 保持各级标题的原始字体大小 */
  .menu-item .menu-item__home-normal-text,
  .menu-item__home-normal-text .button-text {
    font-size: 18px !important;
  }

  .menu-item .menu-item__home-title1,
  .menu-item__home-title1 .button-text {
    font-size: 22px !important;
  }

  .menu-item .menu-item__home-title2,
  .menu-item__home-title2 .button-text {
    font-size: 20px !important;
  }

  .menu-item .menu-item__home-title3,
  .menu-item__home-title3 .button-text {
    font-size: 18px !important;
  }

  .menu-item .menu-item__home-title4,
  .menu-item__home-title4 .button-text {
    font-size: 16px !important;
  }

  .menu-item .menu-item__home-title5,
  .menu-item__home-title5 .button-text {
    font-size: 14px !important;
  }

  .menu-item .menu-item__home-title6,
  .menu-item__home-title6 .button-text {
    font-size: 12px !important;
  }
}

/* 小屏幕适配 */
@media (max-width: 900px) {
  .menu-item .menu-item__home-normal-text,
  .menu-item .menu-item__home-title1,
  .menu-item .menu-item__home-title2,
  .menu-item .menu-item__home-title3,
  .menu-item .menu-item__home-title4,
  .menu-item .menu-item__home-title5,
  .menu-item .menu-item__home-title6 {
    width: 50px !important;
    font-size: 10px !important;
    padding-left: 2px !important;
    border: none !important;
    outline: none !important;
  }

  .menu-item__home-normal-text .button-text,
  .menu-item__home-title1 .button-text,
  .menu-item__home-title2 .button-text,
  .menu-item__home-title3 .button-text,
  .menu-item__home-title4 .button-text,
  .menu-item__home-title5 .button-text,
  .menu-item__home-title6 .button-text {
    width: 30px;
    font-size: 10px !important;
  }

  /* 在小屏幕上调整各级标题的特定样式 */
  .menu-item .menu-item__home-title1,
  .menu-item__home-title1 .button-text {
    font-size: 14px !important;
    padding-left: 5px !important;
  }

  .menu-item .menu-item__home-title2,
  .menu-item__home-title2 .button-text {
    font-size: 13px !important;
    padding-left: 6px !important;
  }

  .menu-item .menu-item__home-title3,
  .menu-item__home-title3 .button-text {
    font-size: 12px !important;
    padding-left: 7px !important;
  }

  .menu-item .menu-item__home-normal-text,
  .menu-item__home-normal-text .button-text {
    font-size: 14px !important;
    padding-left: 1px !important;
  }
}

/* ==================== 布局菜单纸张类型按钮样式 ==================== */

/* 布局菜单中的纸张类型、方向、页面模式、页边距、全屏和编辑器设置选择器 */
.ribbon-group .menu-item .paper-size,
.ribbon-group .menu-item .paper-direction,
.ribbon-group .menu-item .page-mode,
.ribbon-group .menu-item .paper-margin,
.ribbon-group .menu-item .fullscreen,
.ribbon-group .menu-item .editor-option {
  position: relative;
  display: inline-block;
  margin: 0 2px;
}

.ribbon-group .menu-item .paper-size i,
.ribbon-group .menu-item .paper-direction i,
.ribbon-group .menu-item .page-mode i,
.ribbon-group .menu-item .paper-margin i,
.ribbon-group .menu-item .fullscreen i,
.ribbon-group .menu-item .editor-option i {
  width: 24px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  background-repeat: no-repeat;
  background-size: 18px 18px;
  background-position: center;
  border: none !important;
  outline: none !important;
  box-sizing: border-box;
}

/* 布局菜单按钮图标样式 - 修复图标路径 */
.ribbon-group .menu-item .paper-size i {
  background-image: url('./assets/images/paper-size.svg');
}

.ribbon-group .menu-item .paper-direction i {
  background-image: url('./assets/images/paper-direction.svg');
}

.ribbon-group .menu-item .page-mode i {
  background-image: url('./assets/images/page-mode.svg');
}

.ribbon-group .menu-item .paper-margin i {
  background-image: url('./assets/images/paper-margin.svg');
}

.ribbon-group .menu-item .fullscreen i {
  background-image: url('./assets/images/request-fullscreen.svg');
}

.ribbon-group .menu-item .fullscreen.exist i {
  background-image: url('./assets/images/exit-fullscreen.svg');
}

.ribbon-group .menu-item .editor-option i {
  background-image: url('./assets/images/option.svg');
}

.ribbon-group .menu-item .paper-size i:hover,
.ribbon-group .menu-item .paper-direction i:hover,
.ribbon-group .menu-item .page-mode i:hover,
.ribbon-group .menu-item .paper-margin i:hover,
.ribbon-group .menu-item .fullscreen i:hover,
.ribbon-group .menu-item .editor-option i:hover {
  background-color: #e8f4fd !important;
}

.ribbon-group .menu-item .paper-size i:active,
.ribbon-group .menu-item .paper-direction i:active,
.ribbon-group .menu-item .page-mode i:active,
.ribbon-group .menu-item .paper-margin i:active,
.ribbon-group .menu-item .fullscreen i:active,
.ribbon-group .menu-item .editor-option i:active {
  background-color: #d1e7dd !important;
}

/* 布局菜单纸张类型、方向和页面模式下拉选项 */
.ribbon-group .menu-item .paper-size .options,
.ribbon-group .menu-item .paper-direction .options,
.ribbon-group .menu-item .page-mode .options {
  position: fixed;
  min-width: 100px;
  padding: 8px 0;
  background: #fff;
  font-size: 12px;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  display: none;
  z-index: 9999;
}

.ribbon-group .menu-item .paper-size .options.visible,
.ribbon-group .menu-item .paper-direction .options.visible,
.ribbon-group .menu-item .page-mode .options.visible {
  display: block;
}

.ribbon-group .menu-item .paper-size .options ul,
.ribbon-group .menu-item .paper-direction .options ul,
.ribbon-group .menu-item .page-mode .options ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.ribbon-group .menu-item .paper-size .options li,
.ribbon-group .menu-item .paper-direction .options li,
.ribbon-group .menu-item .page-mode .options li {
  padding: 8px 12px;
  margin: 0;
  user-select: none;
  transition: all 0.2s ease;
  text-align: center;
  cursor: pointer;
  color: #333;
  white-space: nowrap;
  font-size: 12px;
}

.ribbon-group .menu-item .paper-size .options li:hover,
.ribbon-group .menu-item .paper-direction .options li:hover,
.ribbon-group .menu-item .page-mode .options li:hover {
  background-color: #f5f5f5;
  color: #007acc;
}

.ribbon-group .menu-item .paper-size .options li.active,
.ribbon-group .menu-item .paper-direction .options li.active,
.ribbon-group .menu-item .page-mode .options li.active {
  background-color: #e8f4f8;
  color: #007acc;
  font-weight: 500;
}

/* 动画效果 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 右侧工具栏样式1按钮样式 ==================== */
