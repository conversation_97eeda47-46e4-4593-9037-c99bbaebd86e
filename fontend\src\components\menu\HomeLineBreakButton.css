.menu-item__home-line-break {
  width: 35px !important;
  height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  border-radius: 4px !important;
  transition: background-color 0.2s ease !important;
  font-size: 12px !important;
  color: #333 !important;
  user-select: none !important;
  box-sizing: border-box !important;
  position: relative !important;
  padding: 5px !important;
}

.menu-item__home-line-break i {
  width: 24px !important;
  height: 24px !important;
  display: inline-block !important;
  position: relative !important;
}

.menu-item__home-line-break i::before {
  content: "¶";
  font-size: 18px !important;
  font-weight: bold !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

.menu-item__home-line-break:hover {
  background-color: #f0f0f0 !important;
}

.menu-item__home-line-break.active {
  background-color: #e6f7ff !important;
  border: 1px solid #91d5ff !important;
}

.menu-item__home-line-break.active i::before {
  color: #1890ff !important;
}