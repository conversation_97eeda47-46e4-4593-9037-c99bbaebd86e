.menu-item__date {
  position: relative;
}

.menu-item__date i {
  background-image: url('../../assets/images/date.svg');
}

/* 日期下拉框 - 智能定位样式 */
.menu-item__date .options {
  width: 180px; /* 增加宽度 */
  position: fixed !important; /* 使用fixed定位确保浮于最上层 */
  z-index: 999999 !important; /* 确保最高层级 */
  background: #fff;
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  /* 直接显示，不要动画效果 */
  opacity: 1;
  visibility: visible;
  transform: none;
  transition: none;
  pointer-events: auto;
}

/* 隐藏状态 */
.menu-item__date .options:not(.visible) {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

/* 日期选项样式 */
.menu-item__date .options li {
  padding: 8px 12px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 日期选项悬停效果 */
.menu-item__date .options li:hover {
  background: #f5f7fa;
  color: #409eff;
}