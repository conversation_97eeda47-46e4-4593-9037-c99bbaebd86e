<!-- 排版工具组件 -->
<div class="typography-tools" editor-component="typeset">
  <!-- 样式区域 -->
  <div class="typography-style-container">
    <div class="typography-section-title">图书样式：</div>
    <select class="typography-style-select" data-action="style-select">
      <option value="style1">样式1</option>
      <option value="style2">样式2</option>
      <option value="style3">样式3</option>
      <option value="style4">样式4</option>
      <option value="style5">样式5</option>
      <option value="style6">样式6</option>
      <option value="style7">样式7</option>
      <option value="style8">样式8</option>
      <option value="style9">样式9</option>
      <option value="style10">样式10</option>
      <option value="style11">样式11</option>
      <option value="style12">样式12</option>
      <option value="style11">样式13</option>
      <option value="style12">样式14</option>
      <option value="style11">样式15</option>
      <option value="style12">样式16</option>
      <option value="style11">样式17</option>
      <option value="style12">样式18</option>
      <option value="style11">样式19</option>
      <option value="style12">样式20</option>
    </select>
    <button class="typography-button style-apply-button" data-action="apply-style">
      <span class="typography-button-text">应用</span>
    </button>
  </div>

  <!-- 图片工具区域 -->
  <div class="typography-buttons image-tools-buttons">
    <button class="typography-button image-tool-button" data-action="change-image" title="更改图片">
      <span class="typography-button-text">更改图片</span>
    </button>
    <button class="typography-button image-tool-button" data-action="save-image" title="另存为图片">
      <span class="typography-button-text">另存为图片</span>
    </button>
  </div>

  <!-- 表格边框工具区域 -->
  <div class="typography-buttons table-border-tools-buttons">
    <button class="typography-button table-border-tool-button" data-action="table-border-top" title="切换上边框">
      <span class="typography-button-text">上无线框</span>
    </button>
    <button class="typography-button table-border-tool-button" data-action="table-border-right" title="切换右边框">
      <span class="typography-button-text">右无线框</span>
    </button>
    <button class="typography-button table-border-tool-button" data-action="table-border-bottom" title="切换下边框">
      <span class="typography-button-text">下无线框</span>
    </button>
    <button class="typography-button table-border-tool-button" data-action="table-border-left" title="切换左边框">
      <span class="typography-button-text">左无线框</span>
    </button>
    <button class="typography-button table-border-tool-button" data-action="table-slash-forward"
      title="添加/移除正斜线（从左上到右下）">
      <span class="typography-button-text">正斜线</span>
    </button>
    <button class="typography-button table-border-tool-button" data-action="table-slash-back" title="添加/移除反斜线（从右上到左下）">
      <span class="typography-button-text">反斜线</span>
    </button>
  </div>

</div>