/* 正文按钮容器 */
.menu-item .menu-item__normal-text {
  width: 50px;
  height: 30px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  padding: 0 2px;
  box-sizing: border-box;
  border: none !important;
}

/* 正文按钮悬停效果 */
.menu-item__normal-text:hover {
  background-color: #e8f4fd;
  border: none !important;
  outline: none !important;
}

/* 正文按钮激活状态 */
.menu-item__normal-text.active {
  background-color: #d1e7dd;
  border: none !important;
  outline: none !important;
}

/* 正文按钮文字 */
.menu-item__normal-text .button-text {
  font-size: 12px;
  color: #333;
  font-weight: normal;
  user-select: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1;
}
