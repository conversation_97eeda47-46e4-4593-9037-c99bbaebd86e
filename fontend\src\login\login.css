/* 登录页面样式 */

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #374151;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* 登录容器 */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

/* 背景装饰 */
.background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* 登录卡片 */
.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 10px 40px 10px 40px !important;
    width: 100%;
    max-width: 420px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 登录头部 */
.login-header {
    text-align: center;
}

.logo {
    display: inline-block;
}

.login-title {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
}

.login-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
}

/* 表单样式 */
.login-form {
    width: 100%;
}

.form-group {
    margin-bottom: 4px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 12px;
    z-index: 2;
    pointer-events: none;
}

.form-input {
    width: 100%;
    padding: 12px 12px 12px 44px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #ffffff;
}

.form-input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-input::placeholder {
    color: #9ca3af;
}

/* 密码显示切换 */
.password-toggle {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.password-toggle:hover {
    background-color: #f3f4f6;
}

/* 验证码样式 */
.captcha-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.captcha-input {
    flex: 1;
}

.captcha-display {
    display: flex;
    align-items: center;
    background: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 12px 16px;
    min-width: 100px;
    justify-content: center;
    gap: 8px;
}

.captcha-code {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    font-weight: bold;
    color: #4f46e5;
    letter-spacing: 2px;
}

.captcha-refresh {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: #6b7280;
    transition: all 0.2s;
}

.captcha-refresh:hover {
    color: #4f46e5;
    background-color: rgba(79, 70, 229, 0.1);
}

/* 复选框样式 */
.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.checkbox-input {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-input:checked + .checkbox-custom {
    background-color: #4f46e5;
    border-color: #4f46e5;
}

.checkbox-input:checked + .checkbox-custom::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-label {
    font-size: 14px;
    color: #6b7280;
}

/* 登录按钮 */
.login-button {
    width: 100%;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 4px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
}

.login-button:active {
    transform: translateY(0);
}

.login-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.button-text {
    transition: opacity 0.3s ease;
}

.button-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.login-button.loading .button-text {
    opacity: 0;
}

.login-button.loading .button-loading {
    opacity: 1;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 错误消息 */
.error-message {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
    min-height: 16px;
}

.form-error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px;
    border-radius: 8px;
    font-size: 14px;
    margin-top: 16px;
    display: none;
}

.form-error.show {
    display: block;
}

/* 底部链接 */
.login-footer {
    text-align: center;
}

.footer-text {
    font-size: 14px;
    color: #6b7280;
}

.footer-link {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s;
}

.footer-link:hover {
    color: #3730a3;
    text-decoration: underline;
}

/* 版本信息 */
.version-info {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    z-index: 1;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container {
        padding: 16px;
    }
    
    .login-card {
        padding: 10px;
        border-radius: 16px;
    }
    
    .login-title {
        font-size: 24px;
    }
    
    .captcha-wrapper {
        flex-direction: column;
    }
    
    .captcha-display {
        align-self: stretch;
    }
}

/* 成功状态 */
.form-input.success {
    border-color: #10b981;
}

.form-input.error {
    border-color: #ef4444;
}

/* 动画效果 */
.login-card {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
