import './FontStyleGroup.css'
import { FontSizeAddButton } from './FontSizeAddButton'
import { FontSizeMinusButton } from './FontSizeMinusButton'
import { BoldButton } from './BoldButton'
import { ItalicButton } from './ItalicButton'
import { UnderlineButton } from './UnderlineButton'
import { StrikeoutButton } from './StrikeoutButton'
import { SuperscriptButton } from './SuperscriptButton'
import { SubscriptButton } from './SubscriptButton'
import { ColorButton } from './ColorButton'
import { HighlightButton } from './HighlightButton'

export class FontStyleGroup {
  private container: HTMLElement;
  private fontSizeAddButton: FontSizeAddButton;
  private fontSizeMinusButton: FontSizeMinusButton;
  private boldButton: BoldButton;
  private italicButton: ItalicButton;
  private underlineButton: UnderlineButton;
  private strikeoutButton: StrikeoutButton;
  private superscriptButton: SuperscriptButton;
  private subscriptButton: SubscriptButton;
  private colorButton: ColorButton;
  private highlightButton: HighlightButton;

  constructor(container: HTMLElement, command: any) {
    this.container = container
    
    // 设置容器内容
    this.container.innerHTML = this.render()
    
    // 创建按钮实例
    const fontSizeAddContainer = this.container.querySelector('.font-size-add-button-container') as HTMLElement
    this.fontSizeAddButton = new FontSizeAddButton(fontSizeAddContainer, command)
    
    const fontSizeMinusContainer = this.container.querySelector('.font-size-minus-button-container') as HTMLElement
    this.fontSizeMinusButton = new FontSizeMinusButton(fontSizeMinusContainer, command)
    
    const boldContainer = this.container.querySelector('.bold-button-container') as HTMLElement
    this.boldButton = new BoldButton(boldContainer, command)
    
    const italicContainer = this.container.querySelector('.italic-button-container') as HTMLElement
    this.italicButton = new ItalicButton(italicContainer, command)
    
    const underlineContainer = this.container.querySelector('.underline-button-container') as HTMLElement
    this.underlineButton = new UnderlineButton(underlineContainer, command)
    
    const strikeoutContainer = this.container.querySelector('.strikeout-button-container') as HTMLElement
    this.strikeoutButton = new StrikeoutButton(strikeoutContainer, command)
    
    const superscriptContainer = this.container.querySelector('.superscript-button-container') as HTMLElement
    this.superscriptButton = new SuperscriptButton(superscriptContainer, command)
    
    const subscriptContainer = this.container.querySelector('.subscript-button-container') as HTMLElement
    this.subscriptButton = new SubscriptButton(subscriptContainer, command)
    
    const colorContainer = this.container.querySelector('.color-button-container') as HTMLElement
    this.colorButton = new ColorButton(colorContainer, command)
    
    const highlightContainer = this.container.querySelector('.highlight-button-container') as HTMLElement
    this.highlightButton = new HighlightButton(highlightContainer, command)
  }

  private render(): string {
    return `<div class="font-style-group">
      <div class="font-size-add-button-container"></div>
      <div class="font-size-minus-button-container"></div>
      <div class="bold-button-container"></div>
      <div class="italic-button-container"></div>
      <div class="underline-button-container"></div>
      <div class="strikeout-button-container"></div>
      <div class="superscript-button-container"></div>
      <div class="subscript-button-container"></div>
      <div class="color-button-container"></div>
      <div class="highlight-button-container"></div>
    </div>`
  }

  // 更新按钮状态
  public updateState(payload: any): void {
    this.boldButton.updateState(payload.bold)
    this.italicButton.updateState(payload.italic)
    this.underlineButton.updateState(payload.underline)
    this.strikeoutButton.updateState(payload.strikeout)
    this.superscriptButton.updateState(payload.type === 'SUPERSCRIPT')
    this.subscriptButton.updateState(payload.type === 'SUBSCRIPT')
    this.colorButton.updateState(payload.color)
    this.highlightButton.updateState(payload.highlight)
  }
} 