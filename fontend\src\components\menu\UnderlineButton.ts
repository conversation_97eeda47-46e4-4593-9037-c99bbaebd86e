import './UnderlineButton.css'

export class UnderlineButton {
  private element: HTMLDivElement;
  private selectElement: HTMLSpanElement;
  private optionsElement: HTMLDivElement;
  private command: any;
  private isApple: boolean;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    this.isApple = typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent)
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.underline-button') as HTMLDivElement
    this.selectElement = this.element.querySelector('.select') as HTMLSpanElement
    this.optionsElement = this.element.querySelector('.options') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="underline-button" title="下划线(${this.isApple ? '⌘' : 'Ctrl'}+U)">
      <i></i>
      <span class="select"></span>
      <div class="options">
        <ul>
          <li data-decoration-style='solid'>
            <i></i>
          </li>
          <li data-decoration-style='double'>
            <i></i>
          </li>
          <li data-decoration-style='dashed'>
            <i></i>
          </li>
          <li data-decoration-style='dotted'>
            <i></i>
          </li>
          <li data-decoration-style='wavy'>
            <i></i>
          </li>
        </ul>
      </div>
    </div>`
  }

  private bindEvents(): void {
    // 点击选择器显示/隐藏选项
    this.selectElement.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      this.optionsElement.classList.toggle('visible')
    }

    // 点击按钮图标执行下划线命令
    this.element.querySelector('i')!.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      console.log('underline')
      this.command.executeUnderline()
    }

    // 选择下划线样式
    const ulElement = this.optionsElement.querySelector('ul')!
    ulElement.onclick = (evt) => {
      evt.stopPropagation() // 阻止事件冒泡
      const target = evt.target as HTMLElement
      const li = target.closest('li') as HTMLLIElement
      if (li && li.dataset.decorationStyle) {
        const decorationStyle = li.dataset.decorationStyle as any
        this.command.executeUnderline({
          style: decorationStyle
        })
        this.optionsElement.classList.remove('visible')
      }
    }

    // 点击页面其他地方关闭选项框
    document.addEventListener('click', (e) => {
      if (!this.element.contains(e.target as Node)) {
        this.optionsElement.classList.remove('visible')
      }
    })
  }

  // 更新按钮状态
  public updateState(isUnderline: boolean): void {
    if (isUnderline) {
      this.element.classList.add('active')
    } else {
      this.element.classList.remove('active')
    }
  }
} 