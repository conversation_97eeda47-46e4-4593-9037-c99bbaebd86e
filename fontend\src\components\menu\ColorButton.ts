import './ColorButton.css'

export class ColorButton {
  private element: HTMLDivElement;
  private colorControlDom: HTMLInputElement;
  private colorSpanDom: HTMLSpanElement;
  private command: any;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.color-button') as HTMLDivElement
    this.colorControlDom = this.element.querySelector('#color') as HTMLInputElement
    this.colorSpanDom = this.element.querySelector('span') as HTMLSpanElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="color-button" title="字体颜色">
      <i></i>
      <span></span>
      <input type="color" id="color" />
    </div>`
  }

  private bindEvents(): void {
    this.colorControlDom.oninput = () => {
      this.command.executeColor(this.colorControlDom.value)
    }
    
    this.element.onclick = () => {
      console.log('color')
      this.colorControlDom.click()
    }
  }

  // 更新按钮状态
  public updateState(color: string | null): void {
    if (color) {
      this.element.classList.add('active')
      this.colorControlDom.value = color
      this.colorSpanDom.style.backgroundColor = color
    } else {
      this.element.classList.remove('active')
      this.colorControlDom.value = '#000000'
      this.colorSpanDom.style.backgroundColor = '#000000'
    }
  }
} 