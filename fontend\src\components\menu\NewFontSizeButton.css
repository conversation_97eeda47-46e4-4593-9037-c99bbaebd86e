/* 新字号按钮样式 */
.new-font-size-button {
  width: 80px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
  border-radius: 4px;
  transition: all 0.2s ease;
  background: #ffffff; /* 改为纯白背景，提高对比度 */
  border: 1px solid #d4d7de; /* 稍微加深边框颜色 */
  z-index: 1000; /* 确保按钮本身不被遮盖 */
  height: 32px; /* 明确设置高度 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 添加轻微阴影 */
}

.new-font-size-button:hover {
  background: rgba(25, 55, 88, .04);
  border-color: #c0c4cc;
}

.new-font-size-button.active {
  background: rgba(25, 55, 88, .08);
  border-color: #409eff;
}

/* 字号选择显示区域 */
.new-font-size-button .select {
  width: 100%;
  height: 32px;
  font-size: 13px;
  line-height: 32px;
  user-select: none;
  border: none;
  padding: 0 20px 0 8px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #303133; /* 改为更深的颜色，提高可读性 */
  background: transparent;
  font-weight: 600; /* 增加字体粗细 */
}

/* 下拉箭头 */
.new-font-size-button .select::after {
  position: absolute;
  content: "";
  top: 50%;
  right: 6px;
  width: 0;
  height: 0;
  margin-top: -2px;
  border-color: #606266 transparent transparent; /* 加深箭头颜色 */
  border-style: solid solid none;
  border-width: 4px 4px 0;
  transition: transform 0.2s ease;
}

/* 下拉框展开时箭头旋转 */
.new-font-size-button .options.visible + .select::after,
.new-font-size-button.active .select::after {
  transform: rotate(180deg);
}

/* 字号选择下拉框 */
.new-font-size-button .options {
  position: fixed !important;
  top: 100%;
  left: 0;
  width: 70px;
  max-height: 300px;
  background: #e2e2e2;
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  z-index: 999999 !important; /* 确保浮于最上层 */
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  pointer-events: none; /* 隐藏时不响应鼠标事件 */
}

/* 下拉框显示状态 */
.new-font-size-button .options.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto; /* 显示时恢复鼠标事件 */
  z-index: 999999 !important; /* 确保显示时的层级 */
}

/* 字号选项列表 */
.new-font-size-button .options ul {
  list-style: none;
  margin: 0;
  padding: 4px 0;
  max-height: 296px;
  overflow-y: auto;
}

/* 滚动条样式 */
.new-font-size-button .options ul::-webkit-scrollbar {
  width: 6px;
}

.new-font-size-button .options ul::-webkit-scrollbar-track {
  background: #f5f7fa;
}

.new-font-size-button .options ul::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.new-font-size-button .options ul::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

/* 字号选项项目 */
.new-font-size-button .options li {
  padding: 8px 12px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid transparent;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  font-weight: 500;
}

/* 字号选项悬停效果 */
.new-font-size-button .options li:hover {
  background: #f5f7fa;
  color: #409eff;
}

/* 字号选项激活状态 */
.new-font-size-button .options li.active {
  background: #ecf5ff;
  color: #409eff;
  font-weight: 600;
}

.new-font-size-button .options li.active::after {
  content: "✓";
  float: right;
  color: #409eff;
  font-weight: bold;
}

/* 常用字号分组分隔线 */
.new-font-size-button .options li:nth-child(6)::after,
.new-font-size-button .options li:nth-child(15)::after {
  content: "";
  position: absolute;
  left: 12px;
  right: 12px;
  bottom: 0;
  height: 1px;
  background: #e2e6ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .new-font-size-button {
    width: 70px;
  }
  
  .new-font-size-button .options {
    width: 80px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .new-font-size-button {
    border-width: 2px;
  }
  
  .new-font-size-button .options {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .new-font-size-button,
  .new-font-size-button .select::after,
  .new-font-size-button .options,
  .new-font-size-button .options li {
    transition: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .new-font-size-button {
    background: rgba(255, 255, 255, 0.1);
    border-color: #4c4d4f;
  }
  
  .new-font-size-button:hover {
    background: rgba(255, 255, 255, 0.15);
  }
  
  .new-font-size-button .select {
    color: #464646;
  }
  
  .new-font-size-button .options {
    background: #f0f0f0;
    border-color: #e7e7e7;
  }
  
  .new-font-size-button .options li {
    color: #3a3a3a;
  }
  
  .new-font-size-button .options li:hover {
    background: #3a3d41;
  }
  
  .new-font-size-button .options li.active {
    background: #1e3a5f;
    color: #66b3ff;
  }
}
