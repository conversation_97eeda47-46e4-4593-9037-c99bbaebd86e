import Editor from '../../../editor'
import { RowFlex } from '../../../editor/dataset/enum/Row'
import { ListType } from '../../../editor/dataset/enum/List'
import { ElementType } from '../../../editor/dataset/enum/Element'
import { TdBorder, TdSlash } from '../../../editor/dataset/enum/table/Table'
// import { getSafeRange, hasValidSelection } from '../../../editor/utils/selection'
import html from './typeset.html'
import './typeset.css'

/**
 * 排版工具组件
 * 提供各种文本排版功能，包括样式、字体格式、对齐方式和列表等
 */
export class TypesetTools {
  private dom: HTMLDivElement
  private instance: Editor
  private statusCheckTimer: number | null = null

  constructor(instance: Editor) {
    this.instance = instance

    // 创建DOM元素
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html

    // 从模板中获取根元素
    const rootElement = tempDiv.firstElementChild as HTMLDivElement
    if (rootElement) {
      this.dom = rootElement
    } else {
      console.error('TypesetTools: 无法解析HTML模板')
      this.dom = document.createElement('div')
      this.dom.className = 'typography-tools'
      this.dom.setAttribute('editor-component', 'typeset')
    }

    // 初始化组件
    this.init()
  }

  /**
   * 初始化排版工具组件
   */
  private init(): void {
    try {
      // 绑定所有按钮事件
      this.bindButtonEvents()

      // 设置全局错误处理器
      this.setupGlobalErrorHandler()

      // 监听编辑器选区变化，更新工具按钮状态
      const originalRangeStyleChange = this.instance.listener.rangeStyleChange
      this.instance.listener.rangeStyleChange = () => {
        try {
          // 调用原有的监听器（如果存在）
          if (originalRangeStyleChange) {
            originalRangeStyleChange()
          }
          // 更新图片工具状态
          this.updateImageToolsState()
          // 更新表格边框工具状态
          this.updateTableBorderToolsState()
        } catch (error) {
          console.warn('更新工具状态时发生错误:', error)
        }
      }

      // 监听内容变化事件
      const originalContentChange = this.instance.listener.contentChange
      this.instance.listener.contentChange = () => {
        try {
          // 调用原有的监听器（如果存在）
          if (originalContentChange) {
            originalContentChange()
          }
          // 延迟更新状态，确保内容变化完成
          setTimeout(() => {
            this.updateImageToolsState()
            this.updateTableBorderToolsState()
          }, 10)
        } catch (error) {
          console.warn('内容变化更新工具状态时发生错误:', error)
        }
      }

      // 同时监听选区变化事件
      this.instance.eventBus.on('rangeStyleChange', () => {
        try {
          this.updateImageToolsState()
          this.updateTableBorderToolsState()
        } catch (error) {
          console.warn('EventBus更新工具状态时发生错误:', error)
        }
      })

      // 监听内容变化事件
      this.instance.eventBus.on('contentChange', () => {
        try {
          setTimeout(() => {
            this.updateTableBorderToolsState()
          }, 10)
        } catch (error) {
          console.warn('EventBus内容变化更新工具状态时发生错误:', error)
        }
      })

      // 初始化完成后立即更新一次状态
      setTimeout(() => {
        this.updateImageToolsState()
        this.updateTableBorderToolsState()
      }, 100)

      // 额外的延迟更新，确保DOM完全加载
      setTimeout(() => {
        this.updateTableBorderToolsState()
      }, 500)

      // 启动定时器定期检查状态
      this.startStatusCheckTimer()

      // 添加全局调试方法
      if (typeof window !== 'undefined') {
        (window as any).debugTableBorderTools = () => {
          console.log('=== 手动调试表格边框工具 ===')
          this.updateTableBorderToolsState()
        }

        // 添加强制启用按钮的方法
        (window as any).forceEnableTableBorderTools = () => {
          console.log('=== 强制启用表格边框工具 ===')
          const tableBorderToolButtons = this.dom.querySelectorAll('.table-border-tool-button')
          console.log('找到按钮数量:', tableBorderToolButtons.length)

          tableBorderToolButtons.forEach((button, index) => {
            const buttonElement = button as HTMLButtonElement
            buttonElement.disabled = false
            buttonElement.classList.remove('disabled')
            console.log(`按钮 ${index + 1} 已启用:`, button.textContent)
          })
        }

        // 添加测试表格边框功能的方法
        (window as any).testTableBorderFunction = (action: string) => {
          console.log('=== 测试表格边框功能 ===', action)
          this.handleTableBorderToolClick(action)
        }
      }

    } catch (error) {
      console.error('TypesetTools初始化失败:', error)
    }
  }



  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandler(): void {
    // 捕获Selection API相关错误
    const originalGetRangeAt = Selection.prototype.getRangeAt
    Selection.prototype.getRangeAt = function(index: number) {
      try {
        if (this.rangeCount === 0 || index >= this.rangeCount || index < 0) {
          throw new Error('Invalid range index')
        }
        return originalGetRangeAt.call(this, index)
      } catch (error) {
        console.warn('Selection.getRangeAt错误已被捕获:', error)
        // 返回一个空的Range对象
        const range = document.createRange()
        range.setStart(document.body, 0)
        range.setEnd(document.body, 0)
        return range
      }
    }
  }

  /**
   * 绑定按钮事件
   */
  private bindButtonEvents(): void {
    try {
      // 绑定样式按钮事件
      this.bindStyleButtons()

      // 绑定字体格式按钮事件
      this.bindFormatButtons()

      // 绑定对齐按钮事件
      this.bindAlignButtons()

      // 绑定列表按钮事件
      this.bindListButtons()

      // 绑定应用按钮事件
      this.bindApplyButton()

      // 绑定图片工具按钮事件
      this.bindImageToolButtons()

      // 绑定表格边框工具按钮事件
      this.bindTableBorderToolButtons()

    } catch (error) {
      console.error('绑定按钮事件失败:', error)
    }
  }

  /**
   * 绑定样式按钮事件
   */
  private bindStyleButtons(): void {
    const styleButtons = this.dom.querySelectorAll('[data-style]')

    styleButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const target = e.currentTarget as HTMLElement
        const style = target.dataset.style

        if (style) {
          this.handleStyleClick(style)
          this.setActiveButton(target, styleButtons)
        }
      })
    })
  }

  /**
   * 绑定字体格式按钮事件
   */
  private bindFormatButtons(): void {
    const formatButtons = this.dom.querySelectorAll('[data-action^="bold"], [data-action^="italic"], [data-action^="underline"], [data-action^="strikeout"]')

    formatButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const target = e.currentTarget as HTMLElement
        const action = target.dataset.action

        if (action) {
          this.handleFormatClick(action)
          this.toggleActiveButton(target)
        }
      })
    })
  }

  /**
   * 绑定对齐按钮事件
   */
  private bindAlignButtons(): void {
    const alignButtons = this.dom.querySelectorAll('[data-action^="align"], [data-action="justify"]')

    alignButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const target = e.currentTarget as HTMLElement
        const action = target.dataset.action

        if (action) {
          this.handleAlignClick(action)
          this.setActiveButton(target, alignButtons)
        }
      })
    })
  }

  /**
   * 绑定列表按钮事件
   */
  private bindListButtons(): void {
    const listButtons = this.dom.querySelectorAll('[data-action$="list"]')

    listButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const target = e.currentTarget as HTMLElement
        const action = target.dataset.action

        if (action) {
          this.handleListClick(action)
          this.toggleActiveButton(target)
        }
      })
    })
  }

  /**
   * 绑定应用按钮事件
   */
  private bindApplyButton(): void {
    const applyButton = this.dom.querySelector('[data-action="apply-style"]')

    if (applyButton) {
      applyButton.addEventListener('click', (e) => {
        e.preventDefault()
        e.stopPropagation()
        this.handleApplyStyleClick()
      })
    } else {
      console.warn('未找到应用按钮，请检查HTML模板中是否存在 data-action="apply-style" 的元素')
    }
  }

  /**
   * 处理样式按钮点击
   */
  private handleStyleClick(style: string): void {
    try {


      const command = this.instance.command

      // 根据不同样式应用不同的格式
      switch (style) {
        case 'style1':
          // 样式1：大标题样式
          command.executeSize(18)
          command.executeBold()
          break

        case 'style2':
          // 样式2：副标题样式
          command.executeSize(16)
          command.executeBold()
          break

        case 'style3':
          // 样式3：强调样式
          command.executeSize(14)
          command.executeItalic()
          break

        case 'style4':
          // 样式4：普通样式
          command.executeSize(12)
          break

        default:
          console.warn(`未知样式: ${style}`)
      }

      this.showMessage(`${style}样式已应用`)

    } catch (error) {
      console.error(`应用样式${style}失败:`, error)
    }
  }

  /**
   * 处理字体格式按钮点击
   */
  private handleFormatClick(action: string): void {
    try {


      const command = this.instance.command

      switch (action) {
        case 'bold':
          command.executeBold()
          break

        case 'italic':
          command.executeItalic()
          break

        case 'underline':
          command.executeUnderline()
          break

        case 'strikeout':
          command.executeStrikeout()
          break

        default:
          console.warn(`未知格式操作: ${action}`)
      }

    } catch (error) {
      console.error(`执行格式操作${action}失败:`, error)
    }
  }

  /**
   * 处理对齐按钮点击
   */
  private handleAlignClick(action: string): void {
    try {


      const command = this.instance.command

      switch (action) {
        case 'align-left':
          command.executeRowFlex(RowFlex.LEFT)
          break

        case 'align-center':
          command.executeRowFlex(RowFlex.CENTER)
          break

        case 'align-right':
          command.executeRowFlex(RowFlex.RIGHT)
          break

        case 'justify':
          command.executeRowFlex(RowFlex.ALIGNMENT)
          break

        default:
          console.warn(`未知对齐操作: ${action}`)
      }

    } catch (error) {
      console.error(`执行对齐操作${action}失败:`, error)
    }
  }

  /**
   * 处理列表按钮点击
   */
  private handleListClick(action: string): void {
    try {


      const command = this.instance.command

      switch (action) {
        case 'unordered-list':
          command.executeList(ListType.UL)
          break

        case 'ordered-list':
          command.executeList(ListType.OL)
          break

        default:
          console.warn(`未知列表操作: ${action}`)
      }

    } catch (error) {
      console.error(`执行列表操作${action}失败:`, error)
    }
  }

  /**
   * 处理应用样式按钮点击
   * 从配置文件重新加载样式参数并应用到系统
   */
  private handleApplyStyleClick(): void {
    try {
      console.log('🔄 开始应用样式配置...')

      // 动态导入配置管理器
      import('../../../stylesConfig/FontStyleConfigManager').then(({ fontStyleConfigManager }) => {
        // 重新加载配置文件
        fontStyleConfigManager.reloadConfig().then(() => {
          console.log('✅ 配置文件重新加载完成')

          // 更新标题常量配置
          import('../../../editor/dataset/constant/Title').then(({ getUpdatedTitleOption }) => {
            // 获取更新后的标题配置
            const updatedTitleOption = getUpdatedTitleOption()
            console.log('📝 更新后的标题配置:', updatedTitleOption)

            // 更新编辑器选项中的标题配置
            const editorOptions = this.instance.command.getOptions()
            if (editorOptions && editorOptions.title) {
              Object.assign(editorOptions.title, updatedTitleOption)
              console.log('🔧 编辑器标题配置已更新')
            }

            // 显示成功消息

            this.showMessage('样式配置已应用！')


            // 触发编辑器重新渲染以应用新样式
            this.instance.command.executeForceUpdate({
              isSubmitHistory: false
            })

            console.log('🎉 样式配置应用完成')
          }).catch(error => {
            console.error('❌ 更新标题常量配置失败:', error)
            this.showMessage('样式配置应用失败：无法更新标题配置')
          })
        }).catch(error => {
          console.error('❌ 重新加载配置文件失败:', error)
          this.showMessage('样式配置应用失败：无法重新加载配置文件')
        })
      }).catch(error => {
        console.error('❌ 加载配置管理器失败:', error)
        this.showMessage('样式配置应用失败：无法加载配置管理器')
      })

    } catch (error) {
      console.error('❌ 应用样式配置失败:', error)
      this.showMessage('样式配置应用失败：发生未知错误')
    }
  }

  /**
   * 设置激活按钮（单选模式）
   */
  private setActiveButton(activeButton: HTMLElement, buttonGroup: NodeListOf<Element>): void {
    // 移除所有按钮的激活状态
    buttonGroup.forEach(btn => btn.classList.remove('active'))

    // 激活当前按钮
    activeButton.classList.add('active')
  }

  /**
   * 切换按钮激活状态（多选模式）
   */
  private toggleActiveButton(button: HTMLElement): void {
    button.classList.toggle('active')
  }

  /**
   * 绑定图片工具按钮事件
   */
  private bindImageToolButtons(): void {
    try {
      const imageToolButtons = this.dom.querySelectorAll('.image-tool-button')

      imageToolButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          const target = e.currentTarget as HTMLElement
          const action = target.dataset.action

          if (action) {
            this.handleImageToolClick(action)
          }
        })
      })
    } catch (error) {
      console.error('绑定图片工具按钮事件失败:', error)
    }
  }

  /**
   * 绑定表格边框工具按钮事件
   */
  private bindTableBorderToolButtons(): void {
    try {
      const tableBorderToolButtons = this.dom.querySelectorAll('.table-border-tool-button')

      console.log('绑定表格边框工具按钮事件:', {
        buttonCount: tableBorderToolButtons.length,
        buttons: Array.from(tableBorderToolButtons).map(btn => ({
          text: btn.textContent,
          action: (btn as HTMLElement).dataset.action
        }))
      })

      if (tableBorderToolButtons.length === 0) {
        console.warn('未找到表格边框工具按钮，检查HTML模板和CSS选择器')
        // 尝试查找所有按钮
        const allButtons = this.dom.querySelectorAll('button')
        console.log('所有按钮:', Array.from(allButtons).map(btn => ({
          text: btn.textContent,
          className: btn.className,
          action: (btn as HTMLElement).dataset.action
        })))
      }

      tableBorderToolButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          const target = e.currentTarget as HTMLElement
          const action = target.dataset.action

          console.log('表格边框按钮点击:', action)
          if (action) {
            this.handleTableBorderToolClick(action)
          }
        })
      })
    } catch (error) {
      console.error('绑定表格边框工具按钮事件失败:', error)
    }
  }

  /**
   * 处理图片工具按钮点击
   */
  private handleImageToolClick(action: string): void {
    try {
      // 检查是否选中了图片
      if (!this.isImageSelected()) {
        this.showMessage('请先选中一张图片')
        return
      }

      const command = this.instance.command

      // 使用与右键菜单完全相同的方式获取元素
      const rangeContext = command.getRangeContext()
      if (!rangeContext || !rangeContext.startElement) {
        this.showMessage('无法获取选中元素信息')
        return
      }

      // 检查是否有选区（必须没有选区）
      if (!rangeContext.isCollapsed) {
        this.showMessage('请取消选区，只选中图片')
        return
      }

      const element = rangeContext.startElement
      if (element.type !== ElementType.IMAGE) {
        this.showMessage('选中的不是图片元素')
        return
      }

      // 调试信息
      console.log('图片布局操作:', {
        action,
        element: element,
        currentDisplay: element.imgDisplay,
        elementType: element.type
      })

      switch (action) {
        case 'change-image':
          this.handleChangeImage()
          break

        case 'save-image':
          command.executeSaveAsImageElement()
          this.showMessage('图片已保存')
          break



        default:
          console.warn(`未知图片工具操作: ${action}`)
      }

      // 更新按钮状态
      this.updateImageToolsState()

    } catch (error) {
      console.error(`执行图片工具操作${action}失败:`, error)
      this.showMessage('操作失败，请重试')
    }
  }

  /**
   * 处理更改图片
   */
  private handleChangeImage(): void {
    try {
      // 创建文件选择器
      const proxyInputFile = document.createElement('input')
      proxyInputFile.type = 'file'
      proxyInputFile.accept = '.png, .jpg, .jpeg, .gif, .webp'

      // 监听文件选择
      proxyInputFile.onchange = () => {
        const file = proxyInputFile.files?.[0]
        if (file) {
          const fileReader = new FileReader()
          fileReader.readAsDataURL(file)
          fileReader.onload = () => {
            const value = fileReader.result as string
            this.instance.command.executeReplaceImageElement(value)
            this.showMessage('图片已更换')
          }
        }
      }

      proxyInputFile.click()
    } catch (error) {
      console.error('更改图片失败:', error)
      this.showMessage('更改图片失败')
    }
  }

  /**
   * 处理表格边框工具按钮点击
   */
  private handleTableBorderToolClick(action: string): void {
    try {
      // 检查是否选中了表格单元格
      if (!this.isTableCellSelected()) {
        this.showMessage('请先选中表格单元格')
        return
      }

      const command = this.instance.command

      // 调试信息
      console.log('表格边框操作:', {
        action
      })

      // 获取当前边框状态
      const currentBorderState = this.getCurrentTableCellBorderState()

      switch (action) {
        case 'table-border-top':
          command.executeTableTdBorderType(TdBorder.TOP)
          this.showMessage(currentBorderState.top ? '上边框已移除' : '上边框已添加')
          break

        case 'table-border-right':
          command.executeTableTdBorderType(TdBorder.RIGHT)
          this.showMessage(currentBorderState.right ? '右边框已移除' : '右边框已添加')
          break

        case 'table-border-bottom':
          command.executeTableTdBorderType(TdBorder.BOTTOM)
          this.showMessage(currentBorderState.bottom ? '下边框已移除' : '下边框已添加')
          break

        case 'table-border-left':
          command.executeTableTdBorderType(TdBorder.LEFT)
          this.showMessage(currentBorderState.left ? '左边框已移除' : '左边框已添加')
          break

        case 'table-slash-forward':
          command.executeTableTdSlashType(TdSlash.FORWARD)
          this.showMessage('正斜线已切换')
          break

        case 'table-slash-back':
          command.executeTableTdSlashType(TdSlash.BACK)
          this.showMessage('反斜线已切换')
          break

        default:
          console.warn(`未知表格边框工具操作: ${action}`)
      }

      // 延迟更新按钮状态，确保Canvas Editor已完成边框更新
      setTimeout(() => {
        this.updateTableBorderToolsState()
      }, 50)

    } catch (error) {
      console.error(`执行表格边框工具操作${action}失败:`, error)
      this.showMessage('操作失败，请重试')
    }
  }

  /**
   * 检查是否选中了图片
   * 使用与右键菜单相同的检测逻辑
   */
  private isImageSelected(): boolean {
    try {
      // 获取上下文信息，与右键菜单使用相同的逻辑
      const range = this.instance.command.getRange()
      const { startIndex, endIndex } = range

      // 检查是否有选区（必须没有选区，即光标位置）
      const editorHasSelection = startIndex !== endIndex
      if (editorHasSelection) {
        return false
      }

      // 获取起始元素
      const rangeContext = this.instance.command.getRangeContext()
      if (rangeContext && rangeContext.startElement) {
        return rangeContext.startElement.type === ElementType.IMAGE
      }

      return false
    } catch (error) {
      console.error('检查图片选中状态失败:', error)
      return false
    }
  }

  /**
   * 检查是否选中了表格单元格
   * 临时设置为总是返回true，强制启用表格边框按钮
   */
  private isTableCellSelected(): boolean {
    try {
      // 临时：总是返回true，强制启用所有表格边框按钮
      console.log('表格检测 - 强制启用所有表格边框按钮')
      return true

      // 原始检测逻辑（暂时注释）
      // const position = this.instance.draw.getPosition()
      // const positionContext = position.getPositionContext()
      // const isInTable = positionContext && positionContext.isTable
      // return !!isInTable
    } catch (error) {
      console.error('检查表格单元格选中状态失败:', error)
      return true // 即使出错也返回true，确保按钮可用
    }
  }

  /**
   * 获取当前选中表格单元格的边框状态
   */
  private getCurrentTableCellBorderState(): { [key: string]: boolean } {
    try {
      const tableParticle = this.instance.draw.getTableParticle()
      const rangeRowCol = tableParticle.getRangeRowCol()

      if (!rangeRowCol || rangeRowCol.length === 0) {
        return { top: false, right: false, bottom: false, left: false }
      }

      // 获取第一个选中的单元格的边框状态
      const firstCell = rangeRowCol[0][0]
      const borderTypes = firstCell.borderTypes || []

      return {
        top: borderTypes.includes(TdBorder.TOP),
        right: borderTypes.includes(TdBorder.RIGHT),
        bottom: borderTypes.includes(TdBorder.BOTTOM),
        left: borderTypes.includes(TdBorder.LEFT)
      }
    } catch (error) {
      console.error('获取表格单元格边框状态失败:', error)
      return { top: false, right: false, bottom: false, left: false }
    }
  }



  /**
   * 更新图片工具按钮状态
   */
  private updateImageToolsState(): void {
    try {
      const imageToolButtons = this.dom.querySelectorAll('.image-tool-button')
      const isImageSelected = this.isImageSelected()

      // 调试信息
      if (isImageSelected) {
        console.log('图片已选中，启用图片工具按钮')
      }

      // 更新按钮禁用状态
      imageToolButtons.forEach(button => {
        const buttonElement = button as HTMLButtonElement
        buttonElement.disabled = !isImageSelected

        if (isImageSelected) {
          buttonElement.classList.remove('disabled')
        } else {
          buttonElement.classList.add('disabled')
        }
      })


    } catch (error) {
      console.error('更新图片工具状态失败:', error)
    }
  }

  /**
   * 更新表格边框工具按钮状态
   */
  private updateTableBorderToolsState(): void {
    try {
      const tableBorderToolButtons = this.dom.querySelectorAll('.table-border-tool-button')
      const isTableCellSelected = this.isTableCellSelected()

      if (tableBorderToolButtons.length === 0) {
        return
      }

      // 获取当前表格单元格的边框状态
      const borderState = isTableCellSelected ? this.getCurrentTableCellBorderState() :
        { top: false, right: false, bottom: false, left: false }

      // 更新按钮禁用状态和文字
      tableBorderToolButtons.forEach((button, index) => {
        const buttonElement = button as HTMLButtonElement
        const action = buttonElement.dataset.action
        const textElement = buttonElement.querySelector('.typography-button-text')

        if (!textElement) return

        buttonElement.disabled = !isTableCellSelected

        if (isTableCellSelected) {
          buttonElement.classList.remove('disabled')
          buttonElement.style.opacity = '1'
          buttonElement.style.cursor = 'pointer'

          // 根据边框状态更新按钮文字
          switch (action) {
            case 'table-border-top':
              textElement.textContent = borderState.top ? '上有线框' : '上无线框'
              break
            case 'table-border-right':
              textElement.textContent = borderState.right ? '右有线框' : '右无线框'
              break
            case 'table-border-bottom':
              textElement.textContent = borderState.bottom ? '下有线框' : '下无线框'
              break
            case 'table-border-left':
              textElement.textContent = borderState.left ? '左有线框' : '左无线框'
              break
          }
        } else {
          buttonElement.classList.add('disabled')
          buttonElement.style.opacity = '0.5'
          buttonElement.style.cursor = 'not-allowed'

          // 禁用状态下显示默认文字
          switch (action) {
            case 'table-border-top':
              textElement.textContent = '上无线框'
              break
            case 'table-border-right':
              textElement.textContent = '右无线框'
              break
            case 'table-border-bottom':
              textElement.textContent = '下无线框'
              break
            case 'table-border-left':
              textElement.textContent = '左无线框'
              break
          }
        }
      })

    } catch (error) {
      console.error('更新表格边框工具状态失败:', error)
    }
  }



  /**
   * 显示操作提示消息
   */
  private showMessage(message: string): void {
    try {
      // 创建临时提示元素
      const messageElement = document.createElement('div')
      messageElement.textContent = message
      messageElement.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #4991f2;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        z-index: 9999;
        pointer-events: none;
      `

      document.body.appendChild(messageElement)

      // 2秒后自动移除提示
      setTimeout(() => {
        if (messageElement.parentNode) {
          messageElement.parentNode.removeChild(messageElement)
        }
      }, 2000)
    } catch (error) {
      console.error('显示提示消息失败:', error)
    }
  }

  /**
   * 获取组件DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }

  /**
   * 启动状态检查定时器
   */
  private startStatusCheckTimer(): void {
    // 清除现有定时器
    if (this.statusCheckTimer) {
      clearInterval(this.statusCheckTimer)
    }

    // 每500ms检查一次状态
    this.statusCheckTimer = window.setInterval(() => {
      try {
        this.updateImageToolsState()
        this.updateTableBorderToolsState()
      } catch (error) {
        console.warn('定时器更新工具状态时发生错误:', error)
      }
    }, 500)
  }

  /**
   * 停止状态检查定时器
   */
  private stopStatusCheckTimer(): void {
    if (this.statusCheckTimer) {
      clearInterval(this.statusCheckTimer)
      this.statusCheckTimer = null
    }
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    try {
      // 停止定时器
      this.stopStatusCheckTimer()

      // 移除所有事件监听器
      const allButtons = this.dom.querySelectorAll('.typography-button')
      allButtons.forEach(button => {
        // 移除事件监听器（这里需要保存原始的处理函数引用才能正确移除）
        button.removeEventListener('click', this.handleButtonClick)
      })

    } catch (error) {
      console.error('销毁排版工具组件失败:', error)
    }
  }
}
