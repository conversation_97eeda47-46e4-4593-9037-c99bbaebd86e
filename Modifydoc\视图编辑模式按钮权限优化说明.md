# 视图编辑模式按钮权限优化说明

## 问题背景

在之前的实现中，当编辑器切换到只读模式时，所有菜单功能都会被禁用（除了搜索和打印功能）。这包括了视图菜单中的六个编辑模式按钮。

## 问题分析

这种设计存在一个严重的用户体验问题：
- 当用户切换到只读模式后，视图编辑模式按钮也被禁用
- 用户无法通过点击"编辑模式"按钮来退出只读模式
- 这会导致用户被"困"在只读模式中，无法恢复编辑功能

## 解决方案

### 修改内容
在所有六个视图编辑模式按钮组件中，修改了 `updateMenuPermissions` 方法：

```typescript
private updateMenuPermissions(mode: EditorMode): void {
  const isReadonly = mode === EditorMode.READONLY
  const enableMenuList = ['search', 'print']

  document.querySelectorAll<HTMLDivElement>('.menu-item>div').forEach(dom => {
    const menu = dom.dataset.menu

    // 视图编辑模式按钮在只读模式下不应被禁用，用户需要这些按钮来切换模式
    if (dom.classList.contains('view-editor-mode-btn')) {
      return // 跳过视图编辑模式按钮的禁用处理
    }

    if (isReadonly && (!menu || !enableMenuList.includes(menu))) {
      dom.classList.add('disable')
    } else {
      dom.classList.remove('disable')
    }
  })
}
```

### 修改的组件
- ViewEditModeButton.ts
- ViewDesignModeButton.ts
- ViewCleanModeButton.ts
- ViewFormModeButton.ts
- ViewReadonlyModeButton.ts
- ViewPrintModeButton.ts

## 优化效果

### 用户体验改进
1. **避免用户被困在只读模式**：用户可以随时通过视图菜单切换回编辑模式
2. **保持功能一致性**：视图编辑模式按钮始终可用，提供稳定的用户体验
3. **符合直觉操作**：用户期望编辑模式切换按钮在任何情况下都能使用

### 功能逻辑
- 只读模式下，大部分编辑功能被禁用（符合只读模式的设计目的）
- 视图编辑模式按钮保持可用（确保用户能够切换模式）
- 搜索和打印功能仍然可用（符合只读模式的基本需求）

## 测试验证

可以通过以下步骤验证修改效果：

1. 启动编辑器
2. 点击视图菜单中的"只读模式"按钮
3. 确认编辑器进入只读模式（文档不可编辑）
4. 确认视图菜单中的六个编辑模式按钮仍然可点击
5. 点击"编辑模式"按钮，确认能够成功退出只读模式

## 总结

这个优化确保了用户在使用编辑器时不会因为模式切换而陷入无法操作的状态，提升了整体的用户体验和产品可用性。