import { CanvasEditor } from '../../editor'
import html from './ImageButton.html'
import './ImageButton.css'

export class ImageButton {
  private dom: HTMLDivElement
  private imageDom: HTMLDivElement
  private imageFileDom: HTMLInputElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.imageDom = this.dom
    this.imageFileDom = this.dom.querySelector<HTMLInputElement>('input')!
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.imageDom.onclick = () => {
      this.imageFileDom.click()
    }
    
    this.imageFileDom.onchange = () => {
      const file = this.imageFileDom.files![0]!
      if (!file) return
      
      const fileReader = new FileReader()
      fileReader.readAsDataURL(file)
      
      fileReader.onload = () => {
        // 计算宽高
        const image = new Image()
        const value = fileReader.result as string
        image.src = value
        
        image.onload = () => {
          this.instance.command.executeImage({
            value,
            width: image.width,
            height: image.height
          })
          // 清空input的值，允许选择同一个文件
          this.imageFileDom.value = ''
        }
      }
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 