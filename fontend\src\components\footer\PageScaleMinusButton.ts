import { CanvasEditor } from '../../editor'
import html from './PageScaleMinusButton.html'
import './PageScaleMinusButton.css'

export class PageScaleMinusButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executePageScaleMinus()
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}