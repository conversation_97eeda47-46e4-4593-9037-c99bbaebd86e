#!/usr/bin/env python
"""
完整的 MySQL 远程数据库功能测试
测试所有登录和数据库功能是否正常工作
"""

import os
import sys
import django
from pathlib import Path

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_editor_backend.settings')
django.setup()

from django.db import connection
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from api.models import Document, DocumentVersion
from django.conf import settings

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def print_result(test_name, success, details=""):
    """打印测试结果"""
    status = "✅" if success else "❌"
    print(f"{status} {test_name}")
    if details:
        print(f"   {details}")

def test_database_connection():
    """测试数据库连接"""
    print_header("数据库连接测试")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION(), DATABASE(), USER()")
            result = cursor.fetchone()
            mysql_version = result[0]
            database_name = result[1]
            user_name = result[2]
            
            print_result("MySQL 连接", True, f"版本: {mysql_version}")
            print_result("数据库名称", True, f"数据库: {database_name}")
            print_result("连接用户", True, f"用户: {user_name}")
            
            # 检查表数量
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print_result("数据库表", True, f"共 {len(tables)} 个表")
            
            return True
    except Exception as e:
        print_result("数据库连接", False, f"连接失败: {e}")
        return False

def test_user_authentication():
    """测试用户认证功能"""
    print_header("用户认证测试")
    
    try:
        # 检查现有用户
        users = User.objects.all()
        print_result("用户查询", True, f"共 {users.count()} 个用户")
        
        # 检查超级用户
        superusers = User.objects.filter(is_superuser=True)
        print_result("超级用户", superusers.exists(), f"共 {superusers.count()} 个超级用户")
        
        if superusers.exists():
            admin_user = superusers.first()
            print_result("管理员用户", True, f"用户名: {admin_user.username}")
            
            # 测试认证（注意：这里不能测试密码，因为密码是加密的）
            print_result("用户对象", True, f"ID: {admin_user.id}, 邮箱: {admin_user.email}")
        
        # 创建测试用户
        test_user, created = User.objects.get_or_create(
            username='testuser_mysql',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'MySQL',
                'last_name': 'Test'
            }
        )
        
        if created:
            test_user.set_password('testpass123')
            test_user.save()
            print_result("创建测试用户", True, f"用户: {test_user.username}")
        else:
            print_result("测试用户存在", True, f"用户: {test_user.username}")
        
        # 测试认证
        auth_user = authenticate(username='testuser_mysql', password='testpass123')
        print_result("用户认证", auth_user is not None, "密码认证测试")
        
        return True
        
    except Exception as e:
        print_result("用户认证测试", False, f"测试失败: {e}")
        return False

def test_document_operations():
    """测试文档操作功能"""
    print_header("文档操作测试")
    
    try:
        # 获取测试用户
        test_user = User.objects.get(username='testuser_mysql')
        
        # 创建测试文档
        doc, created = Document.objects.get_or_create(
            title='MySQL 远程数据库测试文档',
            defaults={
                'content': {
                    'type': 'doc',
                    'content': [
                        {
                            'type': 'paragraph',
                            'content': [
                                {
                                    'type': 'text',
                                    'text': '这是一个存储在远程 MySQL 数据库中的测试文档。'
                                }
                            ]
                        },
                        {
                            'type': 'paragraph',
                            'content': [
                                {
                                    'type': 'text',
                                    'text': '测试时间: 2025-06-13'
                                }
                            ]
                        }
                    ]
                },
                'author': test_user,
                'is_public': True,
                'tags': 'mysql,远程数据库,测试,canvas-editor'
            }
        )
        
        if created:
            print_result("创建文档", True, f"文档: {doc.title}")
        else:
            print_result("文档存在", True, f"文档: {doc.title}")
        
        # 创建文档版本
        version, created = DocumentVersion.objects.get_or_create(
            document=doc,
            version_number=1,
            defaults={
                'content': doc.content,
                'created_by': test_user,
                'comment': 'MySQL 远程数据库初始版本'
            }
        )
        
        if created:
            print_result("创建文档版本", True, f"版本: v{version.version_number}")
        else:
            print_result("文档版本存在", True, f"版本: v{version.version_number}")
        
        # 测试查询
        all_docs = Document.objects.all()
        public_docs = Document.objects.filter(is_public=True)
        user_docs = Document.objects.filter(author=test_user)
        
        print_result("文档查询", True, f"总文档: {all_docs.count()}")
        print_result("公开文档", True, f"公开文档: {public_docs.count()}")
        print_result("用户文档", True, f"用户文档: {user_docs.count()}")
        
        # 测试更新
        doc.tags = 'mysql,远程数据库,测试,canvas-editor,更新测试'
        doc.save()
        print_result("文档更新", True, "标签更新成功")
        
        return True
        
    except Exception as e:
        print_result("文档操作测试", False, f"测试失败: {e}")
        return False

def test_data_persistence():
    """测试数据持久化"""
    print_header("数据持久化测试")
    
    try:
        # 统计各种数据
        user_count = User.objects.count()
        doc_count = Document.objects.count()
        version_count = DocumentVersion.objects.count()
        
        print_result("用户数据", True, f"用户总数: {user_count}")
        print_result("文档数据", True, f"文档总数: {doc_count}")
        print_result("版本数据", True, f"版本总数: {version_count}")
        
        # 检查数据完整性
        if doc_count > 0:
            latest_doc = Document.objects.latest('created_at')
            print_result("最新文档", True, f"标题: {latest_doc.title}")
            print_result("文档作者", True, f"作者: {latest_doc.author.username}")
            print_result("创建时间", True, f"时间: {latest_doc.created_at}")
        
        # 检查关联数据
        for doc in Document.objects.all():
            versions = doc.versions.all()
            print_result(f"文档 '{doc.title}' 版本", True, f"{versions.count()} 个版本")
        
        return True
        
    except Exception as e:
        print_result("数据持久化测试", False, f"测试失败: {e}")
        return False

def test_database_performance():
    """测试数据库性能"""
    print_header("数据库性能测试")
    
    try:
        import time
        
        # 测试查询性能
        start_time = time.time()
        users = list(User.objects.all())
        query_time = time.time() - start_time
        print_result("用户查询性能", True, f"{len(users)} 个用户，耗时: {query_time:.3f}秒")
        
        start_time = time.time()
        docs = list(Document.objects.select_related('author').all())
        query_time = time.time() - start_time
        print_result("文档查询性能", True, f"{len(docs)} 个文档，耗时: {query_time:.3f}秒")
        
        # 测试连接池
        with connection.cursor() as cursor:
            cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
            result = cursor.fetchone()
            print_result("数据库连接", True, f"当前连接数: {result[1]}")
        
        return True
        
    except Exception as e:
        print_result("性能测试", False, f"测试失败: {e}")
        return False

def run_complete_mysql_test():
    """运行完整的 MySQL 测试"""
    print("🗄️ Canvas Editor Backend MySQL 远程数据库完整测试")
    print("="*70)
    
    # 检查当前目录
    if not Path("manage.py").exists():
        print("❌ 错误: 请在 backend 目录下运行此脚本")
        return False
    
    print(f"📁 测试目录: {os.getcwd()}")
    print(f"🔧 数据库类型: {getattr(settings, 'DATABASE_TYPE', 'unknown')}")
    print(f"🌐 MySQL 主机: {settings.DATABASES['default']['HOST']}")
    print(f"📊 数据库名称: {settings.DATABASES['default']['NAME']}")
    
    # 运行所有测试
    tests = [
        ("数据库连接", test_database_connection),
        ("用户认证", test_user_authentication),
        ("文档操作", test_document_operations),
        ("数据持久化", test_data_persistence),
        ("数据库性能", test_database_performance),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results.append((test_name, False))
    
    # 显示总结
    print_header("MySQL 完整测试总结")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 恭喜！MySQL 远程数据库完全配置成功！")
        print("✨ 所有登录和数据库功能都正常工作！")
        print("\n📋 测试总结:")
        print("   ✅ 远程 MySQL 数据库连接正常")
        print("   ✅ 用户认证系统工作正常")
        print("   ✅ 文档 CRUD 操作正常")
        print("   ✅ 数据持久化正常")
        print("   ✅ 数据库性能良好")
        print("\n🚀 启动项目:")
        print("   python manage.py runserver 8000")
        print("\n🔗 访问地址:")
        print("   管理后台: http://127.0.0.1:8000/admin/")
        print("   API 文档: http://127.0.0.1:8000/api/docs/")
        print("   健康检查: http://127.0.0.1:8000/api/health/")
    else:
        print("\n⚠️  部分功能需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    run_complete_mysql_test()
