import './RedoButton.css'

export class RedoButton {
  private element: HTMLDivElement;
  private command: any;
  private isApple: boolean;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    this.isApple = typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent)
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.redo-button') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="redo-button" title="重做(${this.isApple ? '⌘' : 'Ctrl'}+Y)">
      <i></i>
    </div>`
  }

  private bindEvents(): void {
    this.element.onclick = () => {
      console.log('redo')
      this.command.executeRedo()
    }
  }

  // 更新按钮状态
  public updateState(canRedo: boolean): void {
    if (canRedo) {
      this.element.classList.remove('no-allow')
    } else {
      this.element.classList.add('no-allow')
    }
  }
} 