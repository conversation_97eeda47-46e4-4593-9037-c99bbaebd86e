import Editor from '../../editor'
import html from './CatalogModeButton.html'
import './CatalogModeButton.css'
import { Catalog } from '../rightTools'

export class CatalogModeButton {
  private dom: HTMLDivElement
  private instance: Editor
  private catalogInstance: Catalog | null = null
  private isCatalogShow = false  // 默认设为false

  constructor(instance: Editor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    // 等DOM加载完成后再获取目录组件实例
    setTimeout(() => {
      // 从全局获取catalogInstance实例
      this.catalogInstance = (window as any).catalogInstance
      if (!this.catalogInstance) {
        console.error('找不到目录组件实例，请检查全局变量catalogInstance')
      }
    }, 300)

    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.switchCatalog()
    }
  }

  private switchCatalog(): void {
    if (!this.catalogInstance) {
      // 重试获取catalogInstance
      this.catalogInstance = (window as any).catalogInstance
      if (!this.catalogInstance) {
        console.error('找不到目录组件实例，请检查全局变量catalogInstance')
        return
      }
    }

    // 使用Catalog组件的toggle方法
    this.catalogInstance.toggle()
    // 更新本地状态
    this.isCatalogShow = this.catalogInstance.isShown()
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}