# Generated by Django 5.0.7 on 2025-06-13 15:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='文档标题')),
                ('content', models.JSONField(help_text='Canvas Editor 的 JSON 格式内容', verbose_name='文档内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_public', models.BooleanField(default=False, verbose_name='是否公开')),
                ('tags', models.CharField(blank=True, help_text='用逗号分隔', max_length=500, verbose_name='标签')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='作者')),
            ],
            options={
                'verbose_name': '文档',
                'verbose_name_plural': '文档',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='DocumentVersion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version_number', models.PositiveIntegerField(verbose_name='版本号')),
                ('content', models.JSONField(verbose_name='版本内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('comment', models.TextField(blank=True, verbose_name='版本说明')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='versions', to='api.document', verbose_name='文档')),
            ],
            options={
                'verbose_name': '文档版本',
                'verbose_name_plural': '文档版本',
                'ordering': ['-version_number'],
                'unique_together': {('document', 'version_number')},
            },
        ),
    ]
