.footer .page-mode {
  position: relative;
  cursor: pointer;
}

.footer .page-mode i {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/page-mode.svg');
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.footer .page-mode .options {
  position: absolute;
  left: 0;
  bottom: 25px;
  width: 80px;
  border-radius: 4px;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  display: none;
  z-index: 1;
}

.footer .page-mode .options.visible {
  display: block;
}

.footer .page-mode li {
  display: block;
  padding: 8px 10px;
  cursor: pointer;
}

.footer .page-mode li:hover {
  background-color: #f7f7f7;
}

.footer .page-mode li.active {
  color: #4a89ff;
}