.footer .paper-size {
  position: relative;
  cursor: pointer;
}

.footer .paper-size i {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/paper-size.svg');
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.footer .paper-size .options {
  position: absolute;
  right: 0;
  bottom: 25px;
  width: 120px;
  border-radius: 4px;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  display: none;
  z-index: 1;
}

.footer .paper-size .options.visible {
  display: block;
}

.footer .paper-size li {
  display: block;
  padding: 8px 10px;
  cursor: pointer;
}

.footer .paper-size li:hover {
  background-color: #f7f7f7;
}

.footer .paper-size li.active {
  color: #4a89ff;
} 