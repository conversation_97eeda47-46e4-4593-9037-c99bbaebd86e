import { CanvasEditor } from '../../editor'
import html from './SeparatorButton.html'
import './SeparatorButton.css'

export class SeparatorButton {
  private dom: HTMLDivElement
  private separatorDom: HTMLDivElement
  private separatorOptionDom: HTMLDivElement
  private instance: CanvasEditor
  private documentClickHandler: (e: MouseEvent) => void

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.separatorDom = this.dom
    this.separatorOptionDom = this.dom.querySelector<HTMLDivElement>('.options')!
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.separatorDom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 切换显示状态
      const isVisible = this.separatorOptionDom.classList.contains('visible')

      // 先隐藏所有其他的下拉框
      this.hideAllDropdowns()

      if (!isVisible) {
        // 显示当前下拉框并定位
        this.showDropdown()
      }
    }
    
    this.separatorOptionDom.onmousedown = (evt) => {
      evt.stopPropagation() // 阻止事件冒泡
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        let payload: number[] = []
        const separatorDash = li.dataset.separator?.split(',').map(Number)
        if (separatorDash) {
          const isSingleLine = separatorDash.every(d => d === 0)
          if (!isSingleLine) {
            payload = separatorDash
          }
        }
        this.instance.command.executeSeparator(payload)

        // 选择后关闭下拉框
        this.hideDropdown()
      }
    }
    
    // 点击外部关闭下拉框
    this.documentClickHandler = (e) => {
      const target = e.target as Node
      if (!this.dom.contains(target) && !this.separatorOptionDom.contains(target)) {
        this.hideDropdown()
      }
    }
    document.addEventListener('click', this.documentClickHandler)
  }

  // 显示下拉框并定位到按钮下方
  private showDropdown(): void {
    // 先设置基本样式
    this.separatorOptionDom.style.position = 'fixed'
    this.separatorOptionDom.style.zIndex = '999999'

    // 添加visible类，直接显示不要动画
    this.separatorOptionDom.classList.add('visible')

    // 立即计算位置
    this.positionDropdown()
  }

  // 精确定位下拉框到按钮下方
  private positionDropdown(): void {
    const rect = this.separatorDom.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 默认位置：按钮下方
    let left = rect.left
    let top = rect.bottom + 4

    // 水平边界检查（分隔符下拉框宽度约150px）
    if (left + 150 > viewportWidth) {
      left = viewportWidth - 150 - 10
    }
    if (left < 10) {
      left = 10
    }

    // 垂直边界检查
    if (top + 200 > viewportHeight) {
      top = rect.top - 200 - 4
    }
    if (top < 10) {
      top = 10
    }

    // 应用位置
    this.separatorOptionDom.style.left = left + 'px'
    this.separatorOptionDom.style.top = top + 'px'
  }

  // 隐藏下拉框
  private hideDropdown(): void {
    this.separatorOptionDom.classList.remove('visible')
  }

  // 隐藏所有下拉框（避免多个下拉框同时显示）
  private hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options.visible, .menu-item__table__collapse[style*="block"]')
    allDropdowns.forEach(dropdown => {
      if (dropdown.classList.contains('visible')) {
        dropdown.classList.remove('visible')
      } else {
        (dropdown as HTMLElement).style.display = 'none'
      }
    })
  }
  
  // 销毁组件时移除全局事件监听
  public destroy(): void {
    document.removeEventListener('click', this.documentClickHandler)
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 