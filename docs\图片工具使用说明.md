# Canvas Editor 图片工具使用说明

## 📍 功能位置

图片工具位于Canvas Editor右侧工具栏中：
1. 点击右侧工具栏的 **"图书编排"** tab
2. 点击 **"排版"** 子tab
3. 在页面下方找到 **"图片工具"** 区域

## 🖼️ 使用前提

**重要**: 图片工具只有在选中图片时才可用。

### 如何选中图片
1. 在文档中点击任意图片
2. 图片周围会出现选择框
3. 此时图片工具按钮会自动启用

### 如何插入图片
如果文档中还没有图片，可以通过以下方式插入：
1. 使用主菜单的 **插入 → 图片**
2. 或者直接拖拽图片到编辑器中
3. 或者复制粘贴图片

## 🛠️ 功能详解

### 1. 更改图片
**功能**: 替换当前选中的图片为新图片

**使用方法**:
1. 选中要替换的图片
2. 点击 **"更改图片"** 按钮
3. 在弹出的文件选择器中选择新图片
4. 确认选择后图片会自动替换

**支持格式**: PNG, JPG, JPEG, GIF, WEBP

### 2. 另存为图片
**功能**: 将选中的图片保存到本地

**使用方法**:
1. 选中要保存的图片
2. 点击 **"另存为图片"** 按钮
3. 浏览器会自动下载图片文件

**文件名**: 自动生成，格式为 `图片ID.png`

### 3. 嵌入型
**功能**: 将图片设置为嵌入式布局

**效果**: 图片作为文档内容的一部分，不影响文字流动

**适用场景**: 
- 图表、表格等需要与文字紧密结合的图片
- 需要精确定位的图片

### 4. 上下型环绕
**功能**: 设置文字在图片上下方显示

**效果**: 文字在图片的上方和下方显示，图片左右两侧没有文字

**适用场景**:
- 宽度较大的图片
- 需要突出显示的图片
- 横向布局的图表

### 5. 四周型环绕
**功能**: 设置文字环绕图片四周

**效果**: 文字围绕图片的四周流动显示

**适用场景**:
- 尺寸适中的图片
- 需要节省空间的布局
- 杂志风格的排版

### 6. 置文字上方
**功能**: 将图片浮动在文字上方

**效果**: 图片覆盖在文字上方，可以自由定位

**适用场景**:
- 水印效果
- 装饰性图片
- 背景图片

### 7. 置文字下方
**功能**: 将图片浮动在文字下方

**效果**: 图片位于文字下方，作为背景显示

**适用场景**:
- 背景装饰
- 页面背景
- 底层图案

## 🎯 使用技巧

### 快速切换布局
1. 选中图片后，当前布局模式的按钮会显示为蓝色（激活状态）
2. 直接点击其他布局按钮即可快速切换
3. 每次切换都会显示操作提示

### 批量操作
1. 如果需要对多张图片应用相同布局，可以逐个选中并应用
2. 每张图片的布局设置是独立的

### 状态指示
- **灰色按钮**: 未选中图片或选中的不是图片元素
- **白色按钮**: 可用但未激活的功能
- **蓝色按钮**: 当前图片正在使用的布局模式

## ⚠️ 注意事项

### 选中状态
- 必须确保图片被正确选中（有选择框）
- 如果选中了文字或其他元素，图片工具会自动禁用
- 点击文档空白处会取消选择，按钮会变为禁用状态

### 布局效果
- 不同的布局模式适用于不同的场景
- 建议根据文档类型和图片特点选择合适的布局
- 可以随时切换布局模式，实时预览效果

### 文件格式
- 更改图片时只能选择支持的图片格式
- 建议使用PNG或JPG格式以获得最佳兼容性
- 大尺寸图片可能需要较长的加载时间

### 浏览器兼容性
- 功能在主流浏览器中都能正常工作
- 文件选择和下载功能依赖浏览器支持
- 建议使用Chrome或Firefox以获得最佳体验

## 🔧 故障排除

### 按钮一直是灰色的
**原因**: 图片没有被正确选中
**解决方法**: 
1. 确保点击了图片而不是图片旁边的空白
2. 检查图片周围是否有选择框
3. 尝试重新点击图片

### 布局切换没有效果
**原因**: 可能是图片类型或文档状态问题
**解决方法**:
1. 检查浏览器控制台是否有错误信息
2. 尝试刷新页面重新操作
3. 确认图片是通过正常方式插入的

### 文件选择器无法打开
**原因**: 浏览器安全设置或弹窗阻止
**解决方法**:
1. 检查浏览器是否阻止了弹窗
2. 确保在用户点击后立即触发文件选择
3. 尝试使用其他浏览器

### 图片保存失败
**原因**: 浏览器下载设置或权限问题
**解决方法**:
1. 检查浏览器的下载设置
2. 确认有足够的磁盘空间
3. 尝试手动右键保存图片

## 📞 技术支持

如果遇到其他问题，可以：
1. 查看浏览器控制台的错误信息
2. 检查Canvas Editor的版本兼容性
3. 参考项目文档或联系开发团队

---

*使用说明版本: v1.0*  
*适用于: Canvas Editor v0.9.110+*  
*最后更新: 2025年6月25日*
