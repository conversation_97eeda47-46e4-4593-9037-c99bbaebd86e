"""
API 序列化器
"""
from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Document, DocumentVersion

class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']

class DocumentVersionSerializer(serializers.ModelSerializer):
    """文档版本序列化器"""
    created_by = UserSerializer(read_only=True)
    
    class Meta:
        model = DocumentVersion
        fields = ['id', 'version_number', 'content', 'created_at', 'created_by', 'comment']

class DocumentSerializer(serializers.ModelSerializer):
    """文档序列化器"""
    author = UserSerializer(read_only=True)
    versions = DocumentVersionSerializer(many=True, read_only=True)
    
    class Meta:
        model = Document
        fields = ['id', 'title', 'content', 'author', 'created_at', 'updated_at', 
                 'is_public', 'tags', 'versions']
    
    def create(self, validated_data):
        """创建文档时自动设置作者"""
        validated_data['author'] = self.context['request'].user
        return super().create(validated_data)

class DocumentListSerializer(serializers.ModelSerializer):
    """文档列表序列化器（简化版）"""
    author = UserSerializer(read_only=True)
    
    class Meta:
        model = Document
        fields = ['id', 'title', 'author', 'created_at', 'updated_at', 'is_public', 'tags']
