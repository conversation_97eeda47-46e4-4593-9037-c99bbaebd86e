# Django 5 和核心依赖
Django==5.0.7
djangorestframework==3.15.2

# 数据库支持
# mysqlclient==2.2.4  # Windows 安装困难，使用 PyMySQL 替代
# 备选 MySQL 客户端（如果 mysqlclient 安装有问题）
PyMySQL==1.1.1

# 环境变量管理
python-decouple==3.8

# CORS 支持（用于前后端分离）
django-cors-headers==4.3.1

# 开发工具
django-debug-toolbar==4.2.0

# 时间处理
pytz==2024.1

# JSON 处理增强
djangorestframework-simplejwt==5.3.0

# 文件上传处理
Pillow==10.4.0

# 数据验证
django-filter==24.2

# API 文档生成
drf-spectacular==0.27.2

# 缓存支持
django-redis==5.4.0

# 异步任务（可选）
celery==5.3.4
redis==5.0.7

# 生产环境服务器
gunicorn==22.0.0

# 静态文件处理
whitenoise==6.6.0
