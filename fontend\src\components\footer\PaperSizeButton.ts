import { CanvasEditor } from '../../editor'
import html from './PaperSizeButton.html'
import './PaperSizeButton.css'

export class PaperSizeButton {
  private dom: HTMLDivElement
  private paperSizeOptionsDom: HTMLDivElement
  private instance: CanvasEditor
  private documentClickHandler: (e: MouseEvent) => void

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.paperSizeOptionsDom = this.dom.querySelector<HTMLDivElement>('.options')!
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      this.paperSizeOptionsDom.classList.toggle('visible')
    }
    
    this.paperSizeOptionsDom.onclick = (evt) => {
      evt.stopPropagation() // 阻止事件冒泡
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        const paperType = li.dataset.paperSize!
        const [width, height] = paperType.split('*').map(Number)
        this.instance.command.executePaperSize(width, height)
        
        // 更新活动状态
        this.paperSizeOptionsDom.querySelectorAll('li').forEach(item => {
          item.classList.remove('active')
        })
        li.classList.add('active')
        
        // 选择后关闭下拉框
        this.paperSizeOptionsDom.classList.remove('visible')
      }
    }
    
    // 点击外部关闭下拉框
    this.documentClickHandler = (e) => {
      if (!this.dom.contains(e.target as Node)) {
        this.paperSizeOptionsDom.classList.remove('visible')
      }
    }
    document.addEventListener('click', this.documentClickHandler)
  }
  
  // 销毁组件时移除全局事件监听
  public destroy(): void {
    document.removeEventListener('click', this.documentClickHandler)
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 