import './PainterButton.css'

export class PainterButton {
  private element: HTMLDivElement;
  private command: any;
  private isFirstClick = true;
  private painterTimeout: number | undefined;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.painter-button') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="painter-button" title="格式刷(双击可连续使用)">
      <i></i>
    </div>`
  }

  private bindEvents(): void {
    this.element.onclick = () => {
      if (this.isFirstClick) {
        this.isFirstClick = false
        this.painterTimeout = window.setTimeout(() => {
          console.log('painter-click')
          this.isFirstClick = true
          this.command.executePainter({
            isDblclick: false
          })
        }, 200)
      } else {
        window.clearTimeout(this.painterTimeout)
      }
    }

    this.element.ondblclick = () => {
      console.log('painter-dblclick')
      this.isFirstClick = true
      window.clearTimeout(this.painterTimeout)
      this.command.executePainter({
        isDblclick: true
      })
    }
  }

  // 更新按钮状态
  public updateState(isPainterActive: boolean): void {
    if (isPainterActive) {
      this.element.classList.add('active')
    } else {
      this.element.classList.remove('active')
    }
  }
} 