/* 标题4按钮容器 */
.menu-item .menu-item__title4 {
  width: 50px;
  height: 30px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  padding: 0 2px;
  box-sizing: border-box;
}

/* 标题4按钮悬停效果 */
.menu-item__title4:hover {
  background-color: #e8f4fd;
}

/* 标题4按钮激活状态 */
.menu-item__title4.active {
  background-color: #d1e7dd;
  border: 1px solid #badbcc;
}

/* 标题4按钮文字 */
.menu-item__title4 .button-text {
  font-size: 12px;
  color: #333;
  font-weight: bold;
  user-select: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1;
}
