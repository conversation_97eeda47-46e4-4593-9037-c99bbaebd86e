#!/usr/bin/env python
"""
数据库切换脚本
用于在 SQLite 和 MySQL 之间切换
"""

import os
import sys
from pathlib import Path

def update_env_file(db_type):
    """更新 .env 文件中的数据库类型"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ .env 文件不存在")
        return False
    
    # 读取现有内容
    with open(env_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 更新数据库类型
    updated = False
    for i, line in enumerate(lines):
        if line.startswith('DATABASE_TYPE='):
            lines[i] = f'DATABASE_TYPE={db_type}\n'
            updated = True
            break
    
    if not updated:
        lines.append(f'DATABASE_TYPE={db_type}\n')
    
    # 写回文件
    with open(env_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    return True

def test_mysql_connection():
    """测试 MySQL 连接"""
    try:
        import pymysql
        connection = pymysql.connect(
            host='***********',
            port=3306,
            user='book_editor',
            password='eN2eB5mFKpA2PDmB',
            database='book_editor',
            charset='utf8mb4'
        )
        connection.close()
        print("✅ MySQL 连接测试成功")
        return True
    except Exception as e:
        print(f"❌ MySQL 连接测试失败: {e}")
        return False

def switch_to_sqlite():
    """切换到 SQLite"""
    print("🔄 切换到 SQLite 数据库...")
    
    if update_env_file('sqlite'):
        print("✅ 已切换到 SQLite 数据库")
        print("📁 数据库文件: db.sqlite3")
        print("💡 适用于本地开发和测试")
        return True
    else:
        print("❌ 切换失败")
        return False

def switch_to_mysql():
    """切换到 MySQL"""
    print("🔄 切换到 MySQL 数据库...")
    
    # 测试连接
    if not test_mysql_connection():
        print("⚠️  MySQL 连接失败，但仍会更新配置")
    
    if update_env_file('mysql'):
        print("✅ 已切换到 MySQL 数据库")
        print("🌐 远程主机: ***********:3306")
        print("📊 数据库: book_editor")
        print("💡 适用于生产环境")
        return True
    else:
        print("❌ 切换失败")
        return False

def show_current_config():
    """显示当前配置"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ .env 文件不存在")
        return
    
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找数据库类型
    db_type = 'sqlite'  # 默认值
    for line in content.split('\n'):
        if line.startswith('DATABASE_TYPE='):
            db_type = line.split('=')[1].strip()
            break
    
    print(f"📊 当前数据库类型: {db_type.upper()}")
    
    if db_type == 'mysql':
        print("🌐 MySQL 配置:")
        print("   主机: ***********:3306")
        print("   数据库: book_editor")
        print("   用户: book_editor")
    else:
        print("📁 SQLite 配置:")
        print("   文件: db.sqlite3")

def main():
    """主函数"""
    print("🔄 数据库切换工具")
    print("=" * 30)
    
    # 检查当前目录
    if not Path("manage.py").exists():
        print("❌ 请在 backend 目录下运行此脚本")
        sys.exit(1)
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python switch_db.py sqlite   # 切换到 SQLite")
        print("  python switch_db.py mysql    # 切换到 MySQL")
        print("  python switch_db.py status   # 显示当前配置")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == 'sqlite':
        switch_to_sqlite()
    elif command == 'mysql':
        switch_to_mysql()
    elif command == 'status':
        show_current_config()
    else:
        print("❌ 无效的命令")
        print("支持的命令: sqlite, mysql, status")
        sys.exit(1)
    
    print("\n💡 提示: 切换数据库后请运行 'python manage.py migrate'")

if __name__ == "__main__":
    main()
