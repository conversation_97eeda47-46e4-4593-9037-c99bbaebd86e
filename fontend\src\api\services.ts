/**
 * API服务类
 * 提供具体的API调用方法
 */

import { httpClient, ApiResponse } from './http-client'
import { API_ENDPOINTS } from './config'

// 文档数据接口
export interface Document {
  id: number
  title: string
  content: any // 编辑器数据
  created_at: string
  updated_at: string
  author?: string
  version?: number
  is_public?: boolean
}

// 文档版本接口
export interface DocumentVersion {
  id: number
  document: number
  version_number: number
  content: any
  created_at: string
  comment?: string
}

// 创建文档请求接口
export interface CreateDocumentRequest {
  title: string
  content: any
  is_public?: boolean
}

// 更新文档请求接口
export interface UpdateDocumentRequest {
  title?: string
  content?: any
  is_public?: boolean
}

// 分页响应接口
export interface PaginatedResponse<T> {
  count: number
  next: string | null
  previous: string | null
  results: T[]
}

// 健康检查响应接口
export interface HealthCheckResponse {
  status: string
  timestamp: string
  version: string
  database: string
  debug: boolean
}

/**
 * 健康检查服务
 */
export class HealthService {
  /**
   * 检查API健康状态
   */
  static async check(): Promise<HealthCheckResponse> {
    const response = await httpClient.get<HealthCheckResponse>(API_ENDPOINTS.HEALTH)
    return response.data
  }
}

/**
 * 文档服务
 */
export class DocumentService {
  /**
   * 获取文档列表
   */
  static async getDocuments(params?: {
    page?: number
    page_size?: number
    search?: string
    ordering?: string
  }): Promise<PaginatedResponse<Document>> {
    const queryParams = new URLSearchParams()
    
    if (params?.page) queryParams.append('page', String(params.page))
    if (params?.page_size) queryParams.append('page_size', String(params.page_size))
    if (params?.search) queryParams.append('search', params.search)
    if (params?.ordering) queryParams.append('ordering', params.ordering)

    const endpoint = queryParams.toString() 
      ? `${API_ENDPOINTS.DOCUMENTS}?${queryParams.toString()}`
      : API_ENDPOINTS.DOCUMENTS

    const response = await httpClient.get<PaginatedResponse<Document>>(endpoint)
    return response.data
  }

  /**
   * 获取单个文档
   */
  static async getDocument(id: string | number): Promise<Document> {
    const response = await httpClient.get<Document>(API_ENDPOINTS.DOCUMENT_DETAIL(id))
    return response.data
  }

  /**
   * 创建文档
   */
  static async createDocument(data: CreateDocumentRequest): Promise<Document> {
    const response = await httpClient.post<Document>(API_ENDPOINTS.DOCUMENTS, data)
    return response.data
  }

  /**
   * 更新文档
   */
  static async updateDocument(id: string | number, data: UpdateDocumentRequest): Promise<Document> {
    const response = await httpClient.patch<Document>(API_ENDPOINTS.DOCUMENT_DETAIL(id), data)
    return response.data
  }

  /**
   * 删除文档
   */
  static async deleteDocument(id: string | number): Promise<void> {
    await httpClient.delete(API_ENDPOINTS.DOCUMENT_DETAIL(id))
  }

  /**
   * 保存文档内容（自动创建或更新）
   */
  static async saveDocument(data: {
    id?: string | number
    title: string
    content: any
    is_public?: boolean
  }): Promise<Document> {
    if (data.id) {
      // 更新现有文档
      return this.updateDocument(data.id, {
        title: data.title,
        content: data.content,
        is_public: data.is_public
      })
    } else {
      // 创建新文档
      return this.createDocument({
        title: data.title,
        content: data.content,
        is_public: data.is_public
      })
    }
  }

  /**
   * 获取文档版本列表
   */
  static async getDocumentVersions(documentId: string | number): Promise<DocumentVersion[]> {
    const response = await httpClient.get<DocumentVersion[]>(API_ENDPOINTS.DOCUMENT_VERSIONS(documentId))
    return response.data
  }

  /**
   * 创建文档版本
   */
  static async createDocumentVersion(documentId: string | number, data: {
    content: any
    comment?: string
  }): Promise<DocumentVersion> {
    const response = await httpClient.post<DocumentVersion>(
      API_ENDPOINTS.DOCUMENT_VERSIONS(documentId), 
      data
    )
    return response.data
  }
}

/**
 * 文件上传服务
 */
export class UploadService {
  /**
   * 上传图片
   */
  static async uploadImage(file: File, additionalData?: {
    alt?: string
    title?: string
  }): Promise<{ url: string; id?: number }> {
    const response = await httpClient.upload<{ url: string; id?: number }>(
      API_ENDPOINTS.UPLOAD.IMAGE, 
      file, 
      additionalData
    )
    return response.data
  }

  /**
   * 上传文档附件
   */
  static async uploadAttachment(file: File, additionalData?: {
    description?: string
  }): Promise<{ url: string; id?: number; filename: string }> {
    const response = await httpClient.upload<{ url: string; id?: number; filename: string }>(
      API_ENDPOINTS.UPLOAD.ATTACHMENT, 
      file, 
      additionalData
    )
    return response.data
  }
}

/**
 * API服务管理器
 * 提供统一的服务访问入口
 */
export class ApiService {
  static health = HealthService
  static document = DocumentService
  static upload = UploadService

  /**
   * 初始化API服务
   */
  static init(config?: {
    authToken?: string
    baseHeaders?: Record<string, string>
  }): void {
    if (config?.authToken) {
      httpClient.setAuthToken(config.authToken)
    }

    if (config?.baseHeaders) {
      httpClient.setDefaultHeaders(config.baseHeaders)
    }
  }

  /**
   * 设置认证令牌
   */
  static setAuthToken(token: string): void {
    httpClient.setAuthToken(token)
  }

  /**
   * 移除认证令牌
   */
  static removeAuthToken(): void {
    httpClient.removeAuthToken()
  }

  /**
   * 测试API连接
   */
  static async testConnection(): Promise<boolean> {
    try {
      await HealthService.check()
      return true
    } catch (error) {
      console.error('API连接测试失败:', error)
      return false
    }
  }
}

// 导出默认服务实例
export default ApiService
