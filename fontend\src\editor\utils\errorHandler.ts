/**
 * 全局错误处理器
 * 用于处理Selection API和其他常见的浏览器API错误
 */

/**
 * Selection API错误类型
 */
export enum SelectionErrorType {
  INVALID_INDEX = 'INVALID_INDEX',
  NO_SELECTION = 'NO_SELECTION',
  DOM_EXCEPTION = 'DOM_EXCEPTION',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 错误信息接口
 */
export interface ISelectionError {
  type: SelectionErrorType
  message: string
  originalError?: Error
  timestamp: number
}

/**
 * Selection错误处理器类
 */
export class SelectionErrorHandler {
  private static instance: SelectionErrorHandler
  private errorLog: ISelectionError[] = []
  private maxLogSize = 100

  private constructor() {
    this.setupGlobalErrorHandling()
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): SelectionErrorHandler {
    if (!SelectionErrorHandler.instance) {
      SelectionErrorHandler.instance = new SelectionErrorHandler()
    }
    return SelectionErrorHandler.instance
  }

  /**
   * 设置全局错误处理
   */
  private setupGlobalErrorHandling(): void {
    // 监听全局错误事件
    window.addEventListener('error', (event) => {
      this.handleGlobalError(event.error, event.message)
    })

    // 监听未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleGlobalError(event.reason, 'Unhandled Promise Rejection')
    })
  }

  /**
   * 处理全局错误
   */
  private handleGlobalError(error: any, message: string): void {
    // 检查是否是Selection API相关错误
    if (this.isSelectionError(error, message)) {
      const selectionError = this.parseSelectionError(error, message)
      this.logError(selectionError)
      
      // 可以在这里添加自动修复逻辑
      this.attemptAutoFix(selectionError)
    }
  }

  /**
   * 检查是否是Selection API错误
   */
  private isSelectionError(error: any, message: string): boolean {
    const selectionKeywords = [
      'getRangeAt',
      'Selection',
      'not a valid index',
      'range',
      'selection'
    ]
    
    const errorMessage = (error?.message || message || '').toLowerCase()
    return selectionKeywords.some(keyword => 
      errorMessage.includes(keyword.toLowerCase())
    )
  }

  /**
   * 解析Selection错误
   */
  private parseSelectionError(error: any, message: string): ISelectionError {
    const errorMessage = error?.message || message || ''
    
    let type = SelectionErrorType.UNKNOWN
    
    if (errorMessage.includes('not a valid index')) {
      type = SelectionErrorType.INVALID_INDEX
    } else if (errorMessage.includes('no selection')) {
      type = SelectionErrorType.NO_SELECTION
    } else if (error instanceof DOMException) {
      type = SelectionErrorType.DOM_EXCEPTION
    }

    return {
      type,
      message: errorMessage,
      originalError: error,
      timestamp: Date.now()
    }
  }

  /**
   * 记录错误
   */
  private logError(error: ISelectionError): void {
    this.errorLog.push(error)
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift()
    }

    // 在开发环境下输出详细错误信息
    if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
      console.warn('Selection API错误:', error)
    }
  }

  /**
   * 尝试自动修复
   */
  private attemptAutoFix(error: ISelectionError): void {
    switch (error.type) {
      case SelectionErrorType.INVALID_INDEX:
        this.fixInvalidIndexError()
        break
      case SelectionErrorType.NO_SELECTION:
        this.fixNoSelectionError()
        break
      default:
        // 对于未知错误，尝试清除选择状态
        this.clearSelectionSafely()
        break
    }
  }

  /**
   * 修复无效索引错误
   */
  private fixInvalidIndexError(): void {
    try {
      const selection = window.getSelection()
      if (selection) {
        selection.removeAllRanges()
      }
    } catch (e) {
      console.warn('修复无效索引错误失败:', e)
    }
  }

  /**
   * 修复无选择错误
   */
  private fixNoSelectionError(): void {
    // 对于无选择错误，通常不需要特殊处理
    // 但可以记录日志用于调试
    console.debug('检测到无选择状态，这通常是正常的')
  }

  /**
   * 安全地清除选择
   */
  private clearSelectionSafely(): void {
    try {
      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0) {
        selection.removeAllRanges()
      }
    } catch (e) {
      console.warn('清除选择状态失败:', e)
    }
  }

  /**
   * 获取错误日志
   */
  public getErrorLog(): ISelectionError[] {
    return [...this.errorLog]
  }

  /**
   * 清除错误日志
   */
  public clearErrorLog(): void {
    this.errorLog = []
  }

  /**
   * 获取错误统计
   */
  public getErrorStats(): Record<SelectionErrorType, number> {
    const stats: Record<SelectionErrorType, number> = {
      [SelectionErrorType.INVALID_INDEX]: 0,
      [SelectionErrorType.NO_SELECTION]: 0,
      [SelectionErrorType.DOM_EXCEPTION]: 0,
      [SelectionErrorType.UNKNOWN]: 0
    }

    this.errorLog.forEach(error => {
      stats[error.type]++
    })

    return stats
  }
}

/**
 * 初始化全局错误处理器
 */
export function initializeErrorHandler(): SelectionErrorHandler {
  return SelectionErrorHandler.getInstance()
}

/**
 * 包装函数，用于安全地执行可能抛出Selection错误的代码
 */
export function safeExecute<T>(
  fn: () => T,
  fallback?: T,
  errorHandler?: (error: Error) => void
): T | undefined {
  try {
    return fn()
  } catch (error) {
    const handler = SelectionErrorHandler.getInstance()
    
    if (error instanceof Error) {
      // 检查是否是Selection相关错误
      if (handler['isSelectionError'](error, error.message)) {
        const selectionError = handler['parseSelectionError'](error, error.message)
        handler['logError'](selectionError)
        handler['attemptAutoFix'](selectionError)
      }
      
      // 调用自定义错误处理器
      if (errorHandler) {
        errorHandler(error)
      }
    }
    
    return fallback
  }
}
