/**
 * Selection API 修复功能测试
 */

import { 
  getSafeRange, 
  hasValidSelection, 
  getSafeSelectionText,
  SafeSelectionHandler 
} from '../fontend/src/editor/utils/selection'

import { 
  SelectionErrorHandler,
  safeExecute 
} from '../fontend/src/editor/utils/errorHandler'

import { 
  ExtensionConflictDetector,
  createExtensionSafeWrapper 
} from '../fontend/src/editor/utils/extensionCompat'

describe('Selection API 修复测试', () => {
  let mockSelection: Partial<Selection>
  let originalGetSelection: typeof window.getSelection

  beforeEach(() => {
    // 保存原始方法
    originalGetSelection = window.getSelection

    // 创建模拟Selection对象
    mockSelection = {
      rangeCount: 0,
      isCollapsed: true,
      getRangeAt: jest.fn(),
      addRange: jest.fn(),
      removeAllRanges: jest.fn(),
      toString: jest.fn(() => '')
    }

    // 模拟window.getSelection
    window.getSelection = jest.fn(() => mockSelection as Selection)
  })

  afterEach(() => {
    // 恢复原始方法
    window.getSelection = originalGetSelection
  })

  describe('安全Selection工具函数', () => {
    test('getSafeRange - 无选择时返回null', () => {
      mockSelection.rangeCount = 0
      const range = getSafeRange()
      expect(range).toBeNull()
    })

    test('getSafeRange - 有选择时返回Range', () => {
      const mockRange = document.createRange()
      mockSelection.rangeCount = 1
      mockSelection.getRangeAt = jest.fn(() => mockRange)

      const range = getSafeRange()
      expect(range).toBe(mockRange)
      expect(mockSelection.getRangeAt).toHaveBeenCalledWith(0)
    })

    test('getSafeRange - getRangeAt抛出错误时返回null', () => {
      mockSelection.rangeCount = 1
      mockSelection.getRangeAt = jest.fn(() => {
        throw new DOMException('0 is not a valid index', 'IndexSizeError')
      })

      const range = getSafeRange()
      expect(range).toBeNull()
    })

    test('hasValidSelection - 无选择时返回false', () => {
      mockSelection.rangeCount = 0
      expect(hasValidSelection()).toBe(false)
    })

    test('hasValidSelection - 有选择且未折叠时返回true', () => {
      mockSelection.rangeCount = 1
      mockSelection.isCollapsed = false
      expect(hasValidSelection()).toBe(true)
    })

    test('getSafeSelectionText - 返回选择的文本', () => {
      const testText = 'selected text'
      mockSelection.rangeCount = 1
      mockSelection.toString = jest.fn(() => testText)

      const text = getSafeSelectionText()
      expect(text).toBe(testText)
    })

    test('getSafeSelectionText - 无选择时返回空字符串', () => {
      mockSelection.rangeCount = 0
      const text = getSafeSelectionText()
      expect(text).toBe('')
    })
  })

  describe('SafeSelectionHandler', () => {
    let handler: SafeSelectionHandler

    beforeEach(() => {
      handler = new SafeSelectionHandler()
    })

    test('isSelection - 检查选择状态', () => {
      mockSelection.rangeCount = 0
      expect(handler.isSelection()).toBe(false)

      mockSelection.rangeCount = 1
      mockSelection.isCollapsed = false
      expect(handler.isSelection()).toBe(true)
    })

    test('handleSelection - 处理选择事件', () => {
      const callback = jest.fn()
      const mockRange = document.createRange()
      
      mockSelection.rangeCount = 1
      mockSelection.getRangeAt = jest.fn(() => mockRange)

      handler.handleSelection(callback)
      expect(callback).toHaveBeenCalledWith(mockRange)
    })

    test('handleSelection - 处理错误情况', () => {
      const callback = jest.fn()
      
      mockSelection.rangeCount = 1
      mockSelection.getRangeAt = jest.fn(() => {
        throw new Error('Test error')
      })

      handler.handleSelection(callback)
      expect(callback).toHaveBeenCalledWith(null)
    })
  })

  describe('SelectionErrorHandler', () => {
    let errorHandler: SelectionErrorHandler

    beforeEach(() => {
      errorHandler = SelectionErrorHandler.getInstance()
      errorHandler.clearErrorLog()
    })

    test('safeExecute - 正常执行', () => {
      const testFn = jest.fn(() => 'success')
      const result = safeExecute(testFn)
      
      expect(result).toBe('success')
      expect(testFn).toHaveBeenCalled()
    })

    test('safeExecute - 错误处理', () => {
      const testFn = jest.fn(() => {
        throw new DOMException('getRangeAt error', 'IndexSizeError')
      })
      const fallback = 'fallback'
      
      const result = safeExecute(testFn, fallback)
      expect(result).toBe(fallback)
    })

    test('错误统计功能', () => {
      // 模拟一些错误
      safeExecute(() => {
        throw new DOMException('0 is not a valid index', 'IndexSizeError')
      })

      const stats = errorHandler.getErrorStats()
      expect(stats.INVALID_INDEX).toBeGreaterThan(0)
    })
  })

  describe('ExtensionConflictDetector', () => {
    let detector: ExtensionConflictDetector

    beforeEach(() => {
      detector = ExtensionConflictDetector.getInstance()
    })

    test('reportConflict - 报告冲突', () => {
      const extensionName = 'test-extension'
      detector.reportConflict(extensionName)
      
      expect(detector.hasConflicts()).toBe(true)
      expect(detector.getConflictingExtensions()).toContain(extensionName)
    })

    test('createExtensionSafeWrapper - 正常执行', () => {
      const testFn = jest.fn((a: number, b: number) => a + b)
      const wrappedFn = createExtensionSafeWrapper(testFn, 0)
      
      const result = wrappedFn(1, 2)
      expect(result).toBe(3)
      expect(testFn).toHaveBeenCalledWith(1, 2)
    })

    test('createExtensionSafeWrapper - 错误处理', () => {
      const testFn = jest.fn(() => {
        throw new DOMException('0 is not a valid index', 'IndexSizeError')
      })
      const fallback = 'error'
      const wrappedFn = createExtensionSafeWrapper(testFn, fallback)
      
      const result = wrappedFn()
      expect(result).toBe(fallback)
    })
  })

  describe('集成测试', () => {
    test('模拟真实的Selection API错误场景', () => {
      // 模拟扩展导致的getRangeAt错误
      mockSelection.rangeCount = 0 // 实际上没有选择
      mockSelection.getRangeAt = jest.fn(() => {
        throw new DOMException('0 is not a valid index', 'IndexSizeError')
      })

      // 使用安全函数应该不会抛出错误
      expect(() => {
        const range = getSafeRange()
        expect(range).toBeNull()
      }).not.toThrow()

      // 检查是否有有效选择
      expect(hasValidSelection()).toBe(false)

      // 获取选择文本应该返回空字符串
      expect(getSafeSelectionText()).toBe('')
    })

    test('模拟复制操作中的Selection错误', () => {
      // 创建一个模拟的复制函数
      const mockCopyFunction = () => {
        const selection = window.getSelection()
        if (!selection) throw new Error('No selection available')
        
        if (selection.rangeCount === 0) {
          throw new DOMException('0 is not a valid index', 'IndexSizeError')
        }
        
        return selection.getRangeAt(0)
      }

      // 使用安全包装器
      const safeCopyFunction = createExtensionSafeWrapper(mockCopyFunction, null)

      // 应该不会抛出错误，而是返回fallback值
      const result = safeCopyFunction()
      expect(result).toBeNull()
    })
  })
})

describe('DOM环境测试', () => {
  test('真实DOM环境下的Selection操作', () => {
    // 创建测试元素
    const testDiv = document.createElement('div')
    testDiv.innerHTML = 'Test content for selection'
    document.body.appendChild(testDiv)

    try {
      // 创建选择
      const range = document.createRange()
      range.selectNodeContents(testDiv)
      
      const selection = window.getSelection()
      if (selection) {
        selection.removeAllRanges()
        selection.addRange(range)

        // 测试安全函数
        const safeRange = getSafeRange()
        expect(safeRange).not.toBeNull()
        
        const hasSelection = hasValidSelection()
        expect(hasSelection).toBe(true)
        
        const selectionText = getSafeSelectionText()
        expect(selectionText).toBe('Test content for selection')
      }
    } finally {
      // 清理
      document.body.removeChild(testDiv)
      const selection = window.getSelection()
      if (selection) {
        selection.removeAllRanges()
      }
    }
  })
})
