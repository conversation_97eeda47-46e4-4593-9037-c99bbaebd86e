/* 开始菜单字号按钮容器 */
.menu-item .menu-item__home-font-size {
  width: 50px;
  position: relative;
}

/* 开始菜单字号选择显示区域 */
.menu-item__home-font-size .select {
  width: 35px;
  height: 100%;
  font-size: 14px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-top: 10px;
}

/* 开始菜单字号按钮图标 */
.menu-item__home-font-size i {
  transform: translateX(-5px);
  background-image: url('../../assets/images/font-size.svg');
}

/* 开始菜单字号选择下拉框 */
.menu-item__home-font-size .options {
  width: 80px;
  max-height: 250px;
  overflow-y: auto;
  position: fixed !important; /* 使用fixed定位确保浮于最上层 */
  z-index: 999999 !important; /* 确保最高层级 */
  background: #fff;
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  pointer-events: none; /* 隐藏时不响应鼠标事件 */
}

/* 下拉框显示状态 */
.menu-item__home-font-size .options.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto; /* 显示时恢复鼠标事件 */
}

/* 开始菜单字号选项样式 */
.menu-item__home-font-size .options li {
  padding: 6px 12px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 开始菜单字号选项悬停效果 */
.menu-item__home-font-size .options li:hover {
  background: #f5f7fa;
  color: #409eff;
}

/* 开始菜单字号选项激活状态 */
.menu-item__home-font-size .options li.active {
  background: #ecf5ff;
  color: #409eff;
  font-weight: 600;
}

/* 滚动条样式 */
.menu-item__home-font-size .options::-webkit-scrollbar {
  width: 6px;
}

.menu-item__home-font-size .options::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.menu-item__home-font-size .options::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.menu-item__home-font-size .options::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
