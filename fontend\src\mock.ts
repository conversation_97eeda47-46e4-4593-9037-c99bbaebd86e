import { RowMarginButton } from './components/menu'
import Editor, {
  BlockType,
  ControlType,
  EditorMode,
  ElementType,
  IElement,
  IEditorOption,
  ImageDisplay,
  RowFlex,
  ListStyle,
  ListType,
  TitleLevel
} from './editor'
import { NumberType } from './editor/dataset/enum/Common'

// 定义中文版式示例文档内容
const elementList: IElement[] = []

// 添加文档标题（一级标题，居中）
elementList.push(
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.FIRST,
    rowFlex: RowFlex.CENTER,
    valueList: [
      {
        id: 'paragraph-1', // 添加自定义ID
        value: '中文版式示例文档',
        size: 24,
        rowFlex: RowFlex.CENTER
      },
      { value: '\n',},
      {
        id: 'paragraph-2', // 添加自定义ID
        value: '中文版式示例文档',
        size: 34,
        rowFlex: RowFlex.CENTER,
        rowMargin: 5,
        color:  '#0000FF'


      }

    ],
  },
  {
    value: '\n'
  }
)

// 添加文档副标题（二级标题，居中）
elementList.push(
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.SECOND,
    rowFlex: RowFlex.CENTER,
    valueList: [
      {
        value: '包含各种常用元素和格式',
        size: 18
      }
    ]
  },
  {
    value: '\n'
  },
  {
    value: '\n'
  }
)

// 添加第一部分标题（三级标题）
elementList.push(
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.THIRD,
    valueList: [
      {
        value: '一、文档标题示例',
        size: 16
      }
    ]
  },
  {
    value: '\n'
  }
)

// 正文段落（首行缩进2个字符）
elementList.push(
  {
    value: '　　本文档展示了中文排版中常用的各种元素和格式，包括标题、段落、表格、图片、超链接等。正文默认使用宋体，首行缩进两个中文字符。标题使用黑体，根据级别不同使用不同的字号。',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '　　第二段落继续介绍文档的内容。正文段落之间的间距为一个回车符。正文采用默认行间距，字体大小为16像素，正文颜色为默认黑色。',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '\n'
  }
)

// 添加第二部分标题（三级标题）
elementList.push(
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.THIRD,
    valueList: [
      {
        value: '二、各级标题示例',
        size: 16
      }
    ]
  },
  {
    value: '\n'
  }
)

// 各级标题展示
elementList.push(
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.FIRST,
    valueList: [
      {
        value: '1. 一级标题（24像素）',
        size: 24
      }
    ]
  },
  {
    value: '\n'
  },
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.SECOND,
    valueList: [
      {
        value: '2. 二级标题（18像素）',
        size: 18
      }
    ]
  },
  {
    value: '\n'
  },
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.THIRD,
    valueList: [
      {
        value: '3. 三级标题（16像素）',
        size: 16
      }
    ]
  },
  {
    value: '\n'
  },
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.FOURTH,
    valueList: [
      {
        value: '4. 四级标题（14像素）',
        size: 14
      }
    ]
  },
  {
    value: '\n'
  },
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.FIFTH,
    valueList: [
      {
        value: '5. 五级标题（13像素）',
        size: 13
      }
    ]
  },
  {
    value: '\n'
  },
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.SIXTH,
    valueList: [
      {
        value: '6. 六级标题（12像素）',
        size: 12
      }
    ]
  },
  {
    value: '\n'
  },
  {
    value: '\n'
  }
)

// 添加第三部分标题（三级标题）
elementList.push(
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.THIRD,
    valueList: [
      {
        value: '三、文本格式示例',
        size: 16
      }
    ]
  },
  {
    value: '\n'
  }
)

// 文本格式示例
elementList.push(
  {
    value: '　　下面展示了各种文本格式效果：',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '　　1. ',
    size: 16
  },
  {
    value: '粗体文本',
    bold: true,
    size: 16
  },
  {
    value: '：使用bold属性设置为true。',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '　　2. ',
    size: 16
  },
  {
    value: '斜体文本',
    italic: true,
    size: 16
  },
  {
    value: '：使用italic属性设置为true。',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '　　3. ',
    size: 16
  },
  {
    value: '下划线文本',
    underline: true,
    size: 16
  },
  {
    value: '：使用underline属性设置为true。',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '　　4. ',
    size: 16
  },
  {
    value: '删除线文本',
    strikeout: true,
    size: 16
  },
  {
    value: '：使用strikeout属性设置为true。',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '　　5. ',
    size: 16
  },
  {
    value: '红色文本',
    color: '#FF0000',
    size: 16
  },
  {
    value: '：使用color属性设置颜色值。',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '　　6. ',
    size: 16
  },
  {
    value: '背景高亮文本',
    highlight: '#FFFF00',
    size: 16
  },
  {
    value: '：使用highlight属性设置背景颜色。',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '　　7. ',
    size: 16
  },
  {
    value: 'X',
    type: ElementType.SUBSCRIPT,
    size: 16
  },
  {
    value: '（下标）和',
    size: 16
  },
  {
    value: 'Y',
    type: ElementType.SUPERSCRIPT,
    size: 16
  },
  {
    value: '（上标）：使用SUBSCRIPT和SUPERSCRIPT元素类型。',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '\n'
  }
)

// 添加第四部分标题（三级标题）
elementList.push(
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.THIRD,
    valueList: [
      {
        value: '四、列表示例',
        size: 16
      }
    ]
  },
  {
    value: '\n'
  }
)

// 添加列表示例
elementList.push(
  {
    value: '　　下面是有序列表示例：',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '',
    type: ElementType.LIST,
    listType: ListType.OL,
    valueList: [
      {
        value: '列表项一'
      },
      {
        value: '列表项二'
      },
      {
        value: '列表项三\n子项一\n子项二'
      }
    ]
  },
  {
    value: '\n'
  },
  {
    value: '　　下面是无序列表示例：',
    size: 16
  },
  {
    value: '\n'
  },
  {
    value: '',
    type: ElementType.LIST,
    listType: ListType.UL,
    valueList: [
      {
        value: '无序列表项一'
      },
      {
        value: '无序列表项二'
      },
      {
        value: '无序列表项三'
      }
    ]
  },
  {
    value: '\n'
  },
  {
    value: '\n'
  }
)

// 添加第五部分标题（三级标题）
elementList.push(
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.THIRD,
    valueList: [
      {
        value: '五、表格示例',
        size: 16
      }
    ]
  },
  {
    value: '\n'
  }
)

// 添加表格示例说明
elementList.push(
  {
    value: '　　下面是一个简单的表格示例：',
    size: 16
  },
  {
    value: '\n'
  }
)

// 添加表格
elementList.push({
  type: ElementType.TABLE,
  value: '',
  colgroup: [
    {
      width: 120
    },
    {
      width: 120
    },
    {
      width: 120
    },
    {
      width: 120
    }
  ],
  trList: [
    {
      height: 40,
      tdList: [
        {
          colspan: 1,
          rowspan: 1,
          value: [
            { value: '表头1', size: 16, bold: true }
          ]
        },
        {
          colspan: 1,
          rowspan: 1,
          value: [
            { value: '表头2', size: 16, bold: true }
          ]
        },
        {
          colspan: 1,
          rowspan: 1,
          value: [
            { value: '表头3', size: 16, bold: true }
          ]
        },
        {
          colspan: 1,
          rowspan: 1,
          value: [
            { value: '表头4', size: 16, bold: true }
          ]
        }
      ]
    },
    {
      height: 40,
      tdList: [
        {
          colspan: 1,
          rowspan: 1,
          value: [
            { value: '数据1', size: 16 }
          ]
        },
        {
          colspan: 1,
          rowspan: 1,
          value: [
            { value: '数据2', size: 16 }
          ]
        },
        {
          colspan: 1,
          rowspan: 1,
          value: [
            { value: '数据3', size: 16 }
          ]
        },
        {
          colspan: 1,
          rowspan: 1,
          value: [
            { value: '数据4', size: 16 }
          ]
        }
      ]
    },
    {
      height: 40,
      tdList: [
        {
          colspan: 1,
          rowspan: 1,
          value: [
            { value: '数据5', size: 16 }
          ]
        },
        {
          colspan: 2,
          rowspan: 1,
          value: [
            { value: '合并单元格', size: 16 }
          ]
        },
        {
          colspan: 1,
          rowspan: 1,
          value: [
            { value: '数据8', size: 16 }
          ]
        }
      ]
    }
  ]
})

elementList.push(
  {
    value: '\n'
  },
  {
    value: '\n'
  }
)

// 添加第六部分标题（三级标题）
elementList.push(
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.THIRD,
    valueList: [
      {
        value: '六、图片与超链接示例',
        size: 16
      }
    ]
  },
  {
    value: '\n'
  }
)

// 添加图片示例说明
elementList.push(
  {
    value: '　　下面是图片示例：',
    size: 16
  },
  {
    value: '\n'
  }
)

// 添加示例图片
elementList.push({
  type: ElementType.IMAGE,
  value: `data:image/png;base64,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`,
  width: 300,
  height: 150,
  id: 'example-image'
} as IElement)

elementList.push(
  {
    value: '\n'
  },
  {
    value: '　　下面是超链接示例：',
    size: 16
  },
  {
    value: '\n'
  },
  {
    type: ElementType.HYPERLINK,
    value: '',
    url: 'https://github.com/Hufe921/canvas-editor',
    valueList: [
      {
        value: 'Canvas Editor 项目链接',
        size: 16,
        color: '#0000FF',
        underline: true
      }
    ]
  },
  {
    value: '\n'
  },
  {
    value: '\n'
  }
)

// 添加第七部分标题（三级标题）
elementList.push(
  {
    value: '',
    type: ElementType.TITLE,
    level: TitleLevel.THIRD,
    valueList: [
      {
        value: '七、控件示例',
        size: 16
      }
    ]
  },
  {
    value: '\n'
  }
)

// 添加各种控件示例
elementList.push(
  {
    value: '　　文本控件：',
    size: 16
  },
  {
    type: ElementType.CONTROL,
    value: '',
    control: {
      conceptId: '1',
      type: ControlType.TEXT,
      value: null,
      placeholder: '请输入文本',
      prefix: '{',
      postfix: '}'
    }
  },
  {
    value: '\n'
  },
  {
    value: '　　下拉控件：',
    size: 16
  },
  {
    type: ElementType.CONTROL,
    value: '',
    control: {
      conceptId: '2',
      type: ControlType.SELECT,
      value: null,
      code: null,
      placeholder: '请选择',
      prefix: '{',
      postfix: '}',
      valueSets: [
        {
          value: '选项一',
          code: '1'
        },
        {
          value: '选项二',
          code: '2'
        },
        {
          value: '选项三',
          code: '3'
        }
      ]
    }
  },
  {
    value: '\n'
  },
  {
    value: '　　复选框控件：',
    size: 16
  },

  {

    type: ElementType.CONTROL,
    value: '',
    control: {
      conceptId: '3',
      type: ControlType.CHECKBOX,
      code: '1',
      value: null,
      valueSets: [

        {

          value: '是',
          code: '1'
        },
        {
          value: '否',
          code: '-1'
        },
        {
          value: '不确定',
          code: '0'
        }
      ]
    }
  },
  {
    value: '\n'
  },
  {
    value: '　　日期控件：',
    size: 16
  },
  {
    type: ElementType.CONTROL,
    value: '',
    control: {
      conceptId: '4',
      type: ControlType.DATE,
      value: [
        {
          value: ''
        }
      ],
      placeholder: '请选择日期'
    }
  },
  {
    value: '\n'
  },
  {
    value: '\n'
  }
)

// 添加结尾
elementList.push(
  {
    value: '　　以上就是中文版式示例文档的全部内容，包含了各种常用元素和格式。您可以根据需要进行修改和扩展。',
    size: 16
  },
  {
    value: '\n'
  }
)

// 导出元素列表
export const data: IElement[] = elementList

// 导出编辑器数据结构，包含header和footer
export const editorData = {
  header: [
    {
      value: '图书编写测试',
      size: 12,
      rowFlex: RowFlex.CENTER
    },

    {
      value: '\n',
      type: ElementType.SEPARATOR
    }
  ],
  main: data,  // 引用上面导出的data
  footer: [
    {
      value: 'canvas-editor',
      size: 12
    }
  ]
}

// 评论数据
interface IComment {
  id: string
  content: string
  userName: string
  rangeText: string
  createdDate: string
  pageNo?: number
  startIndex?: number
  endIndex?: number
  isEdit?: boolean
  active?: boolean
}

export const commentList: IComment[] = [
  {
    id: '1',
    content: '这是对文档内容的批注示例，您可以添加更多批注。',
    userName: '张三',
    rangeText: '中文版式示例文档',
    createdDate: '2023-11-28 10:30:00',
    pageNo: 1,
    startIndex: 0,
    endIndex: 8,
    isEdit: true,
    active: false
  }
]

// 编辑器选项
export const options: IEditorOption = {
  margins: [100, 120, 100, 120], // 修改边距：[上, 右, 下, 左]，右边增加空间，左边减少100px
  watermark: {
    data: 'Book-EDITOR',
    size: 20,
    color: '#8A9EAB',           // 水印颜色
    opacity: 0.15,              // 水印透明度，0-1之间
    font: 'Microsoft YaHei',    // 水印字体
    repeat: true,               // 是否重复显示水印
    gap: [50, 50],            // 水印间隔 [水平间隔, 垂直间隔]
    numberType: NumberType.ARABIC  // 数字类型，arabic：阿拉伯数字，chinese：中文数字
  },
  pageNumber: {
    format: '第{pageNo}页/共{pageCount}页',
    disabled: false,  // 明确启用页码
    fromPageNo: 0,    // 从第1页开始显示页码（页码从0开始计算）
    startPageNo: 1,   // 起始页码为1
    bottom: 60,       // 距离页面底部60像素
    size: 12,         // 字体大小12
    color: '#000000', // 黑色字体
    rowFlex: RowFlex.CENTER  // 居中对齐
  },
  placeholder: {
    data: '请输入正文'
  },
  zone: {
    tipDisabled: false
  },
  maskMargin: [60, 0, 30, 0] // 菜单栏高度60，底部工具栏30为遮盖层
}
