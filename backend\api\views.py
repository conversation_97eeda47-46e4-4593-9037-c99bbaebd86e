from rest_framework.decorators import api_view, action
from rest_framework.response import Response
from rest_framework import status, viewsets, permissions
from rest_framework.pagination import PageNumberPagination
from rest_framework.views import APIView
from django.conf import settings
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db import models
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import datetime
import random
import string
from django.core.cache import cache

from .models import Document, DocumentVersion
from .serializers import (
    DocumentSerializer,
    DocumentListSerializer,
    DocumentVersionSerializer,
    UserSerializer
)

# Create your views here.

class StandardResultsSetPagination(PageNumberPagination):
    """标准分页配置"""
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

class DocumentViewSet(viewsets.ModelViewSet):
    """文档视图集"""
    queryset = Document.objects.all()
    serializer_class = DocumentSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_public', 'author']
    search_fields = ['title', 'tags']
    ordering_fields = ['created_at', 'updated_at', 'title']
    ordering = ['-updated_at']

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return DocumentListSerializer
        return DocumentSerializer

    def get_queryset(self):
        """根据用户权限过滤查询集"""
        queryset = Document.objects.all()
        if not self.request.user.is_authenticated:
            # 未登录用户只能看到公开文档
            queryset = queryset.filter(is_public=True)
        elif not self.request.user.is_staff:
            # 普通用户可以看到自己的文档和公开文档
            queryset = queryset.filter(
                models.Q(author=self.request.user) | models.Q(is_public=True)
            )
        return queryset

    @action(detail=True, methods=['post'])
    def create_version(self, request, pk=None):
        """为文档创建新版本"""
        document = self.get_object()

        # 检查权限
        if document.author != request.user and not request.user.is_staff:
            return Response(
                {'error': '您没有权限为此文档创建版本'},
                status=status.HTTP_403_FORBIDDEN
            )

        # 获取下一个版本号
        last_version = document.versions.first()
        next_version = (last_version.version_number + 1) if last_version else 1

        # 创建版本
        version_data = {
            'document': document,
            'version_number': next_version,
            'content': request.data.get('content', document.content),
            'created_by': request.user,
            'comment': request.data.get('comment', '')
        }

        version = DocumentVersion.objects.create(**version_data)
        serializer = DocumentVersionSerializer(version)

        return Response(serializer.data, status=status.HTTP_201_CREATED)

@api_view(['GET'])
def health_check(request):
    """
    健康检查端点
    """
    return Response({
        'status': 'healthy',
        'timestamp': datetime.datetime.now().isoformat(),
        'version': '1.0.0',
        'database': settings.DATABASE_TYPE,
        'debug': settings.DEBUG
    }, status=status.HTTP_200_OK)


# ==================== 认证相关视图 ====================

def generate_captcha():
    """生成简单的数字验证码"""
    return ''.join(random.choices(string.digits, k=4))

@method_decorator(csrf_exempt, name='dispatch')
class CaptchaView(APIView):
    """验证码生成API"""
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        """获取验证码"""
        captcha_code = generate_captcha()
        captcha_key = f"captcha_{random.randint(100000, 999999)}"

        # 将验证码存储到缓存中，5分钟过期
        cache.set(captcha_key, captcha_code, 300)

        return Response({
            'captcha_key': captcha_key,
            'captcha_code': captcha_code,  # 开发环境返回验证码，生产环境应该返回图片
            'expires_in': 300
        })

@method_decorator(csrf_exempt, name='dispatch')
class LoginView(APIView):
    """用户登录API"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        """用户登录"""
        username = request.data.get('username')
        password = request.data.get('password')
        captcha_key = request.data.get('captcha_key')
        captcha_code = request.data.get('captcha_code')

        # 验证必填字段
        if not all([username, password, captcha_key, captcha_code]):
            return Response({
                'error': '用户名、密码和验证码都是必填项',
                'code': 'MISSING_FIELDS'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证验证码
        cached_captcha = cache.get(captcha_key)
        if not cached_captcha or cached_captcha != captcha_code:
            return Response({
                'error': '验证码错误或已过期',
                'code': 'INVALID_CAPTCHA'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证用户凭据
        user = authenticate(request, username=username, password=password)
        if user is None:
            return Response({
                'error': '用户名或密码错误',
                'code': 'INVALID_CREDENTIALS'
            }, status=status.HTTP_401_UNAUTHORIZED)

        if not user.is_active:
            return Response({
                'error': '用户账户已被禁用',
                'code': 'ACCOUNT_DISABLED'
            }, status=status.HTTP_401_UNAUTHORIZED)

        # 登录用户
        login(request, user)

        # 清除验证码
        cache.delete(captcha_key)

        # 生成会话令牌（简单实现）
        session_token = f"session_{user.id}_{random.randint(100000, 999999)}"
        cache.set(f"session_{session_token}", user.id, 86400)  # 24小时过期

        return Response({
            'message': '登录成功',
            'user': UserSerializer(user).data,
            'session_token': session_token,
            'expires_in': 86400
        })

@method_decorator(csrf_exempt, name='dispatch')
class LogoutView(APIView):
    """用户登出API"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """用户登出"""
        session_token = request.data.get('session_token')

        # 清除会话令牌
        if session_token:
            cache.delete(f"session_{session_token}")

        # 登出用户
        logout(request)

        return Response({
            'message': '登出成功'
        })

@method_decorator(csrf_exempt, name='dispatch')
class RegisterView(APIView):
    """用户注册API"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        """用户注册"""
        username = request.data.get('username')
        email = request.data.get('email')
        password = request.data.get('password')
        first_name = request.data.get('first_name', '')
        last_name = request.data.get('last_name', '')
        captcha_key = request.data.get('captcha_key')
        captcha_code = request.data.get('captcha_code')

        # 验证必填字段
        if not all([username, email, password, captcha_key, captcha_code]):
            return Response({
                'error': '用户名、邮箱、密码和验证码都是必填项',
                'code': 'MISSING_FIELDS'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证验证码
        cached_captcha = cache.get(captcha_key)
        if not cached_captcha or cached_captcha != captcha_code:
            return Response({
                'error': '验证码错误或已过期',
                'code': 'INVALID_CAPTCHA'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查用户名是否已存在
        if User.objects.filter(username=username).exists():
            return Response({
                'error': '用户名已存在',
                'code': 'USERNAME_EXISTS'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查邮箱是否已存在
        if User.objects.filter(email=email).exists():
            return Response({
                'error': '邮箱已被注册',
                'code': 'EMAIL_EXISTS'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 创建用户
        try:
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name
            )

            # 清除验证码
            cache.delete(captcha_key)

            return Response({
                'message': '注册成功',
                'user': UserSerializer(user).data
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'error': f'注册失败: {str(e)}',
                'code': 'REGISTRATION_FAILED'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ProfileView(APIView):
    """用户资料API"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取用户资料"""
        return Response({
            'user': UserSerializer(request.user).data
        })

    def put(self, request):
        """更新用户资料"""
        user = request.user

        # 更新允许的字段
        if 'first_name' in request.data:
            user.first_name = request.data['first_name']
        if 'last_name' in request.data:
            user.last_name = request.data['last_name']
        if 'email' in request.data:
            # 检查邮箱是否已被其他用户使用
            email = request.data['email']
            if User.objects.filter(email=email).exclude(id=user.id).exists():
                return Response({
                    'error': '邮箱已被其他用户使用',
                    'code': 'EMAIL_EXISTS'
                }, status=status.HTTP_400_BAD_REQUEST)
            user.email = email

        user.save()

        return Response({
            'message': '资料更新成功',
            'user': UserSerializer(user).data
        })
