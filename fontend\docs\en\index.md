---
layout: home

title: canvas-editor
titleTemplate: rich text editor by canvas/svg

hero:
  name: canvas-editor
  text: canvas/svg based rich text editor
  actions:
    - theme: brand
      text: Get Started
      link: /en/guide/start.html
    - theme: alt
      text: View on Github
      link: https://github.com/Hufe921/canvas-editor

features:
  - icon: 💡
    title: WYSIWYG
    details: Similar to word pageable, what you see is what you get
  - icon: ⚡️
    title: Lightweight Data Structure
    details: A piece of JSON can render complex styles
  - icon: 🛠️
    title: Rich Features
    details: Supports familiar rich text operations, tables, watermarks, controls, formulas, etc
  - icon: 📦
    title: Easy to Use
    details: The core npm package is officially released, and the menu bar and toolbar can be maintained by themselves
  - icon: 🔩
    title: Flexible Development Mechanism
    details: Through the interface, you can obtain the life cycle, event callback, custom right-click menu, and shortcut keys
  - icon: 🔑
    title: Full TypeScript Types API
    details: Flexible apis and full TypeScript types
---

<style>
  .main>p {
    max-width:100% !important;
  }
</style>
