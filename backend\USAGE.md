# Canvas Editor Backend 使用指南

## 🚀 快速开始

### 1. 启动服务器

使用便捷启动脚本：
```bash
cd backend
python start.py 8000
```

或者使用传统方式：
```bash
cd backend
python manage.py runserver 8000
```

### 2. 访问服务

- **管理后台**: http://127.0.0.1:8000/admin/
  - 用户名: admin
  - 密码: admin

- **API 文档**: http://127.0.0.1:8000/api/docs/
- **健康检查**: http://127.0.0.1:8000/api/health/

## 🔄 数据库切换

### 切换到 SQLite（本地开发）
```bash
python switch_db.py sqlite
python manage.py migrate
```

### 切换到 MySQL（远程生产）
```bash
python switch_db.py mysql
python manage.py migrate
```

### 查看当前数据库配置
```bash
python switch_db.py status
```

## 📊 API 端点

### 文档管理 API

#### 获取文档列表
```http
GET /api/documents/
```

#### 创建新文档
```http
POST /api/documents/
Content-Type: application/json

{
    "title": "我的文档",
    "content": {"type": "doc", "content": []},
    "is_public": false,
    "tags": "标签1,标签2"
}
```

#### 获取文档详情
```http
GET /api/documents/{id}/
```

#### 更新文档
```http
PUT /api/documents/{id}/
Content-Type: application/json

{
    "title": "更新的标题",
    "content": {"type": "doc", "content": []},
    "is_public": true
}
```

#### 删除文档
```http
DELETE /api/documents/{id}/
```

#### 创建文档版本
```http
POST /api/documents/{id}/create_version/
Content-Type: application/json

{
    "content": {"type": "doc", "content": []},
    "comment": "版本更新说明"
}
```

### 查询参数

- `page`: 页码
- `page_size`: 每页数量
- `search`: 搜索关键词
- `is_public`: 过滤公开文档
- `author`: 按作者过滤
- `ordering`: 排序字段

示例：
```http
GET /api/documents/?page=1&page_size=10&search=标题&is_public=true&ordering=-created_at
```

## 🔐 认证和权限

### 权限规则

1. **未登录用户**: 只能查看公开文档
2. **普通用户**: 可以查看自己的文档和公开文档
3. **管理员**: 可以查看所有文档

### 登录认证

使用 Django 的会话认证或基本认证：

```python
# 使用 requests 库示例
import requests

# 登录
session = requests.Session()
login_data = {
    'username': 'your_username',
    'password': 'your_password'
}
session.post('http://127.0.0.1:8000/admin/login/', data=login_data)

# 使用会话访问 API
response = session.get('http://127.0.0.1:8000/api/documents/')
```

## 🛠️ 开发工具

### 管理命令

```bash
# 创建超级用户
python manage.py createsuperuser

# 生成迁移文件
python manage.py makemigrations

# 应用迁移
python manage.py migrate

# 收集静态文件
python manage.py collectstatic

# 启动 Django shell
python manage.py shell
```

### 数据库操作示例

```python
# 在 Django shell 中
from api.models import Document, DocumentVersion
from django.contrib.auth.models import User

# 创建用户
user = User.objects.create_user('testuser', '<EMAIL>', 'password')

# 创建文档
doc = Document.objects.create(
    title='测试文档',
    content={'type': 'doc', 'content': []},
    author=user,
    is_public=True,
    tags='测试,示例'
)

# 创建版本
version = DocumentVersion.objects.create(
    document=doc,
    version_number=1,
    content={'type': 'doc', 'content': []},
    created_by=user,
    comment='初始版本'
)
```

## 🔧 配置说明

### 环境变量 (.env)

```bash
# Django 配置
SECRET_KEY=your-secret-key
DEBUG=True

# 数据库配置
DATABASE_TYPE=sqlite  # 或 mysql

# MySQL 配置
MYSQL_NAME=book_editor
MYSQL_USER=book_editor
MYSQL_PASSWORD=eN2eB5mFKpA2PDmB
MYSQL_HOST=***********
MYSQL_PORT=3306

# 其他配置
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://localhost:3001,http://127.0.0.1:3001
TIME_ZONE=Asia/Shanghai
LANGUAGE_CODE=zh-hans
```

## 🚀 部署建议

### 生产环境配置

1. 设置 `DEBUG=False`
2. 配置正确的 `ALLOWED_HOSTS`
3. 使用 MySQL 数据库
4. 配置 HTTPS
5. 使用 Gunicorn 或 uWSGI
6. 配置 Nginx 反向代理
7. 设置环境变量而不是 .env 文件

### 使用 Gunicorn

```bash
pip install gunicorn
gunicorn book_editor_backend.wsgi:application --bind 0.0.0.0:8000
```

## 📝 注意事项

1. 确保 MySQL 服务器可访问（如果使用 MySQL）
2. 定期备份数据库
3. 监控服务器性能和日志
4. 生产环境中更改默认的 SECRET_KEY
5. 使用 HTTPS 保护敏感数据
