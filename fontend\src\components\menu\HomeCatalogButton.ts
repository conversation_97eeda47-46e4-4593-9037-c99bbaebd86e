import { CanvasEditor } from '../../editor'
import { Catalog } from '../rightTools/catalog'
import html from './HomeCatalogButton.html'
import './HomeCatalogButton.css'

export class HomeCatalogButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor
  private catalogInstance: Catalog | null = null

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    // 等DOM加载完成后再获取目录组件实例
    setTimeout(() => {
      // 从全局获取catalogInstance实例
      this.catalogInstance = (window as any).catalogInstance
      if (!this.catalogInstance) {
        console.error('找不到目录组件实例，请检查全局变量catalogInstance')
      }
    }, 300)

    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 切换目录面板显示/隐藏
      this.toggleCatalog()
    }
  }

  private toggleCatalog(): void {
    if (!this.catalogInstance) {
      // 重试获取catalogInstance
      this.catalogInstance = (window as any).catalogInstance
      if (!this.catalogInstance) {
        console.error('找不到目录组件实例，请检查全局变量catalogInstance')
        return
      }
    }

    // 使用Catalog组件的toggle方法
    this.catalogInstance.toggle()
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}
