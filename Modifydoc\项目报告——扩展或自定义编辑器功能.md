# Canvas Editor 扩展和自定义功能详细分析

## 📋 目录
- [🎯 菜单系统扩展](#菜单系统扩展)
- [🎛️ 控件系统扩展](#控件系统扩展)
- [🔌 插件开发系统](#插件开发系统)
- [🛠️ 工具栏定制](#工具栏定制)
- [📡 事件系统扩展](#事件系统扩展)
- [🎨 主题和样式定制](#主题和样式定制)
- [⚡ 快捷键系统](#快捷键系统)
- [🔧 高级扩展功能](#高级扩展功能)

---

## 🎯 菜单系统扩展

### 1. 右键菜单 (ContextMenu) 系统

#### 📝 基础接口定义
```typescript
interface IRegisterContextMenu {
  key?: string;                    // 菜单项唯一标识
  isDivider?: boolean;            // 是否为分割线
  icon?: string;                  // 图标名称
  name?: string;                  // 菜单名称，支持%s占位符代表选中文本
  shortCut?: string;              // 快捷键显示文本
  disable?: boolean;              // 是否禁用
  when?: (payload: IContextMenuContext) => boolean;  // 显示条件判断函数
  callback?: (command: Command, context: IContextMenuContext) => any;  // 点击回调函数
  childMenus?: IRegisterContextMenu[];  // 子菜单数组
}

// 上下文信息接口
interface IContextMenuContext {
  editorHasSelection: boolean;     // 编辑器是否有选中内容
  selectedText: string;           // 选中的文本内容
  isInTable: boolean;             // 是否在表格中
  isReadonly: boolean;            // 是否为只读模式
  startElement?: IElement;        // 起始元素
  endElement?: IElement;          // 结束元素
  isCrossRowCol: boolean;         // 是否跨行跨列选择
  editorTextFocus: boolean;       // 编辑器是否获得焦点
}
```

#### 🔧 注册自定义右键菜单

##### 基础菜单注册
```typescript
// 注册自定义右键菜单
editor.register.contextMenuList([
  {
    key: 'customTranslate',
    name: '翻译：%s',              // %s会被替换为选中的文本
    icon: 'translate-icon',
    shortCut: 'Ctrl + T',
    when: (context) => {
      // 只在有选中文本且文本长度大于0时显示
      return context.editorHasSelection && context.selectedText.length > 0
    },
    callback: (command, context) => {
      const selectedText = context.selectedText
      // 调用翻译API
      translateText(selectedText).then(result => {
        console.log('翻译结果:', result)
        // 可以选择替换原文本或显示翻译结果
        command.executeInsertElementList([{
          value: `\n译文: ${result}`
        }])
      })
    }
  },
  {
    isDivider: true  // 分割线
  },
  {
    key: 'customFormat',
    name: '文本格式化',
    icon: 'format-icon',
    when: (context) => context.editorHasSelection,
    childMenus: [  // 子菜单
      {
        key: 'upperCase',
        name: '转换为大写',
        callback: (command, context) => {
          const upperText = context.selectedText.toUpperCase()
          command.executeInsertElementList([{ value: upperText }])
        }
      },
      {
        key: 'lowerCase', 
        name: '转换为小写',
        callback: (command, context) => {
          const lowerText = context.selectedText.toLowerCase()
          command.executeInsertElementList([{ value: lowerText }])
        }
      },
      {
        key: 'capitalize',
        name: '首字母大写',
        callback: (command, context) => {
          const capitalizedText = context.selectedText.charAt(0).toUpperCase() + 
                                 context.selectedText.slice(1).toLowerCase()
          command.executeInsertElementList([{ value: capitalizedText }])
        }
      }
    ]
  }
])
```

##### 表格专用菜单
```typescript
// 表格操作菜单
editor.register.contextMenuList([
  {
    key: 'tableOperations',
    name: '表格操作',
    when: (context) => context.isInTable,  // 只在表格中显示
    childMenus: [
      {
        key: 'insertRowAbove',
        name: '在上方插入行',
        icon: 'insert-row-above',
        callback: (command) => {
          command.executeInsertTableTopRow()
        }
      },
      {
        key: 'insertRowBelow',
        name: '在下方插入行', 
        icon: 'insert-row-below',
        callback: (command) => {
          command.executeInsertTableBottomRow()
        }
      },
      {
        key: 'insertColLeft',
        name: '在左侧插入列',
        icon: 'insert-col-left',
        callback: (command) => {
          command.executeInsertTableLeftCol()
        }
      },
      {
        key: 'insertColRight',
        name: '在右侧插入列',
        icon: 'insert-col-right', 
        callback: (command) => {
          command.executeInsertTableRightCol()
        }
      },
      {
        isDivider: true
      },
      {
        key: 'deleteRow',
        name: '删除行',
        icon: 'delete-row',
        callback: (command) => {
          command.executeDeleteTableRow()
        }
      },
      {
        key: 'deleteCol',
        name: '删除列',
        icon: 'delete-col',
        callback: (command) => {
          command.executeDeleteTableCol()
        }
      },
      {
        key: 'deleteTable',
        name: '删除表格',
        icon: 'delete-table',
        callback: (command) => {
          command.executeDeleteTable()
        }
      },
      {
        isDivider: true
      },
      {
        key: 'mergeCells',
        name: '合并单元格',
        icon: 'merge-cells',
        when: (context) => {
          // 只在选中多个单元格时显示
          return context.isCrossRowCol
        },
        callback: (command) => {
          command.executeMergeTableCell()
        }
      },
      {
        key: 'splitCells',
        name: '拆分单元格',
        icon: 'split-cells',
        callback: (command) => {
          command.executeCancelMergeTableCell()
        }
      }
    ]
  }
])
```

##### 图片专用菜单
```typescript
// 图片操作菜单
editor.register.contextMenuList([
  {
    key: 'imageOperations',
    name: '图片操作',
    when: (context) => {
      return context.startElement?.type === ElementType.IMAGE
    },
    childMenus: [
      {
        key: 'changeImage',
        name: '更换图片',
        icon: 'image-change',
        callback: (command) => {
          // 创建文件选择器
          const input = document.createElement('input')
          input.type = 'file'
          input.accept = '.png,.jpg,.jpeg,.gif,.webp'
          input.onchange = () => {
            const file = input.files?.[0]
            if (file) {
              const reader = new FileReader()
              reader.onload = () => {
                command.executeReplaceImageElement(reader.result as string)
              }
              reader.readAsDataURL(file)
            }
          }
          input.click()
        }
      },
      {
        key: 'resizeImage',
        name: '调整图片大小',
        icon: 'resize-image',
        callback: (command) => {
          // 打开图片尺寸调整对话框
          showImageResizeDialog(command)
        }
      },
      {
        key: 'deleteImage',
        name: '删除图片',
        icon: 'delete-image',
        callback: (command) => {
          command.executeBackspace()
        }
      }
    ]
  }
])
```

#### 🎨 内置菜单系统分析

##### 全局菜单 (globalMenus)
```typescript
// 系统内置的全局菜单
const globalMenus = [
  {
    key: 'cut',
    i18nPath: 'contextmenu.global.cut',
    shortCut: 'Ctrl + X',
    when: (payload) => !payload.isReadonly,
    callback: (command) => command.executeCut()
  },
  {
    key: 'copy', 
    i18nPath: 'contextmenu.global.copy',
    shortCut: 'Ctrl + C',
    when: (payload) => payload.editorHasSelection || payload.isCrossRowCol,
    callback: (command) => command.executeCopy()
  },
  {
    key: 'paste',
    i18nPath: 'contextmenu.global.paste', 
    shortCut: 'Ctrl + V',
    when: (payload) => !payload.isReadonly && payload.editorTextFocus,
    callback: (command) => command.executePaste()
  },
  {
    key: 'selectAll',
    i18nPath: 'contextmenu.global.selectAll',
    shortCut: 'Ctrl + A', 
    callback: (command) => command.executeSelectAll()
  }
]
```

##### 超链接菜单 (hyperlinkMenus)
```typescript
const hyperlinkMenus = [
  {
    key: 'deleteHyperlink',
    i18nPath: 'contextmenu.hyperlink.delete',
    when: (payload) => {
      return !payload.isReadonly && 
             payload.startElement?.type === ElementType.HYPERLINK
    },
    callback: (command) => command.executeDeleteHyperlink()
  },
  {
    key: 'cancelHyperlink',
    i18nPath: 'contextmenu.hyperlink.cancel',
    when: (payload) => {
      return !payload.isReadonly && 
             payload.startElement?.type === ElementType.HYPERLINK
    },
    callback: (command) => command.executeCancelHyperlink()
  }
]
```

##### 控件菜单 (controlMenus)
```typescript
const controlMenus = [
  {
    key: 'deleteControl',
    i18nPath: 'contextmenu.control.delete',
    when: (payload) => {
      return !payload.isReadonly && 
             !payload.editorHasSelection &&
             !!payload.startElement?.controlId &&
             payload.options.mode !== EditorMode.FORM
    },
    callback: (command) => command.executeRemoveControl()
  }
]
```

### 2. 菜单国际化支持

#### 语言包注册
```typescript
// 注册中文语言包
editor.register.langMap('zh', {
  contextmenu: {
    global: {
      cut: '剪切',
      copy: '复制', 
      paste: '粘贴',
      selectAll: '全选'
    },
    table: {
      insertRowTop: '在上方插入行',
      insertRowBottom: '在下方插入行',
      insertColLeft: '在左侧插入列',
      insertColRight: '在右侧插入列',
      deleteRow: '删除行',
      deleteCol: '删除列',
      deleteTable: '删除表格',
      mergeCell: '合并单元格',
      cancelMergeCell: '取消合并单元格'
    },
    image: {
      change: '更换图片',
      resize: '调整大小',
      delete: '删除图片'
    },
    hyperlink: {
      delete: '删除链接',
      cancel: '取消链接'
    }
  },
  toolbar: {
    bold: '加粗',
    italic: '斜体',
    underline: '下划线',
    strikethrough: '删除线',
    color: '字体颜色',
    highlight: '背景色',
    font: '字体',
    size: '字号',
    align: '对齐',
    list: '列表',
    table: '表格',
    image: '图片',
    hyperlink: '超链接'
  }
})

// 注册英文语言包
editor.register.langMap('en', {
  contextmenu: {
    global: {
      cut: 'Cut',
      copy: 'Copy',
      paste: 'Paste', 
      selectAll: 'Select All'
    },
    table: {
      insertRowTop: 'Insert Row Above',
      insertRowBottom: 'Insert Row Below',
      insertColLeft: 'Insert Column Left',
      insertColRight: 'Insert Column Right'
    }
  }
})
```

---

## 🎛️ 控件系统扩展

### 1. 内置控件类型

#### 复选框控件 (Checkbox)
```typescript
// 插入复选框控件
editor.command.executeInsertElementList([
  {
    type: ElementType.CHECKBOX,
    value: '',
    checked: false,           // 是否选中
    disabled: false          // 是否禁用
  }
])

// 自定义复选框按钮
export class CustomCheckboxButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.createButton()
    this.bindEvents()
  }

  private createButton(): void {
    this.dom = document.createElement('div')
    this.dom.className = 'custom-checkbox-button'
    this.dom.innerHTML = `
      <div class="button-content">
        <i class="checkbox-icon"></i>
        <span>复选框</span>
      </div>
    `
    this.dom.title = '插入复选框'
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executeInsertElementList([
        {
          type: ElementType.CHECKBOX,
          value: '',
          checked: false
        }
      ])
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}
```

#### 文本输入控件 (Input)
```typescript
// 插入文本输入控件
editor.command.executeInsertElementList([
  {
    type: ElementType.CONTROL,
    value: '',
    control: {
      type: ControlType.TEXT,
      value: '',
      placeholder: '请输入文本',
      prefix: '{',
      postfix: '}',
      minWidth: 100,
      maxWidth: 200
    }
  }
])
```

#### 下拉选择控件 (Select)
```typescript
// 插入下拉选择控件
editor.command.executeInsertElementList([
  {
    type: ElementType.CONTROL,
    value: '',
    control: {
      type: ControlType.SELECT,
      value: '',
      placeholder: '请选择',
      code: null,
      valueSets: [
        { value: 'option1', code: 'opt1' },
        { value: 'option2', code: 'opt2' },
        { value: 'option3', code: 'opt3' }
      ]
    }
  }
])
```

#### 日期控件 (Date)
```typescript
// 插入日期控件
editor.command.executeInsertElementList([
  {
    type: ElementType.CONTROL,
    value: '',
    control: {
      type: ControlType.DATE,
      value: '',
      placeholder: '请选择日期',
      dateFormat: 'YYYY-MM-DD'
    }
  }
])
```

### 2. 自定义控件开发

#### 创建自定义控件类型
```typescript
// 扩展控件类型枚举
enum CustomControlType {
  RICH_TEXT = 'richText',      // 富文本控件
  FILE_UPLOAD = 'fileUpload',  // 文件上传控件
  SIGNATURE = 'signature',     // 签名控件
  BARCODE = 'barcode',         // 条形码控件
  QR_CODE = 'qrCode'          // 二维码控件
}

// 自定义富文本控件
interface IRichTextControl extends IControl {
  type: CustomControlType.RICH_TEXT
  allowedFormats?: string[]    // 允许的格式：['bold', 'italic', 'underline']
  maxLength?: number          // 最大字符数
  toolbar?: boolean           // 是否显示工具栏
}

// 自定义文件上传控件
interface IFileUploadControl extends IControl {
  type: CustomControlType.FILE_UPLOAD
  accept?: string             // 接受的文件类型
  maxSize?: number           // 最大文件大小(MB)
  multiple?: boolean         // 是否支持多文件
  uploadUrl?: string         // 上传地址
}
```

#### 控件渲染器扩展
```typescript
// 自定义控件渲染器
export class CustomControlRenderer {
  private draw: Draw
  private options: IEditorOption

  constructor(draw: Draw) {
    this.draw = draw
    this.options = draw.getOptions()
  }

  // 渲染富文本控件
  public renderRichTextControl(
    ctx: CanvasRenderingContext2D,
    element: IElement,
    x: number,
    y: number
  ): void {
    const control = element.control as IRichTextControl
    const { value, placeholder, minWidth = 100 } = control

    // 绘制控件边框
    ctx.save()
    ctx.strokeStyle = '#DCDFE6'
    ctx.lineWidth = 1
    ctx.strokeRect(x, y, minWidth, 30)

    // 绘制内容或占位符
    ctx.fillStyle = value ? '#000000' : '#CCCCCC'
    ctx.font = '14px Microsoft YaHei'
    ctx.fillText(value || placeholder || '', x + 8, y + 20)

    // 如果显示工具栏，绘制工具栏
    if (control.toolbar) {
      this.renderControlToolbar(ctx, x, y - 25, minWidth)
    }

    ctx.restore()
  }

  // 渲染文件上传控件
  public renderFileUploadControl(
    ctx: CanvasRenderingContext2D,
    element: IElement,
    x: number,
    y: number
  ): void {
    const control = element.control as IFileUploadControl
    const { value, placeholder = '点击上传文件' } = control

    ctx.save()

    // 绘制上传区域
    ctx.strokeStyle = '#DCDFE6'
    ctx.fillStyle = '#FAFAFA'
    ctx.lineWidth = 1
    ctx.fillRect(x, y, 120, 40)
    ctx.strokeRect(x, y, 120, 40)

    // 绘制上传图标
    ctx.fillStyle = '#909399'
    ctx.font = '16px iconfont'
    ctx.fillText('📁', x + 10, y + 25)

    // 绘制文本
    ctx.fillStyle = value ? '#000000' : '#909399'
    ctx.font = '12px Microsoft YaHei'
    ctx.fillText(value || placeholder, x + 30, y + 25)

    ctx.restore()
  }

  // 渲染控件工具栏
  private renderControlToolbar(
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number
  ): void {
    ctx.save()

    // 工具栏背景
    ctx.fillStyle = '#F5F7FA'
    ctx.fillRect(x, y, width, 25)

    // 工具栏按钮
    const buttons = ['B', 'I', 'U']  // 加粗、斜体、下划线
    buttons.forEach((btn, index) => {
      const btnX = x + 5 + index * 25
      const btnY = y + 2

      // 按钮背景
      ctx.fillStyle = '#FFFFFF'
      ctx.fillRect(btnX, btnY, 20, 20)
      ctx.strokeStyle = '#DCDFE6'
      ctx.strokeRect(btnX, btnY, 20, 20)

      // 按钮文字
      ctx.fillStyle = '#606266'
      ctx.font = '12px Microsoft YaHei'
      ctx.textAlign = 'center'
      ctx.fillText(btn, btnX + 10, btnY + 14)
    })

    ctx.restore()
  }
}
```

#### 控件事件处理
```typescript
// 自定义控件事件处理器
export class CustomControlEventHandler {
  private draw: Draw
  private command: Command

  constructor(draw: Draw, command: Command) {
    this.draw = draw
    this.command = command
  }

  // 处理文件上传控件点击
  public handleFileUploadClick(element: IElement): void {
    const control = element.control as IFileUploadControl

    // 创建文件选择器
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = control.accept || '*/*'
    input.multiple = control.multiple || false

    input.onchange = async () => {
      const files = Array.from(input.files || [])

      // 检查文件大小
      if (control.maxSize) {
        const oversizedFiles = files.filter(file =>
          file.size > control.maxSize! * 1024 * 1024
        )
        if (oversizedFiles.length > 0) {
          alert(`文件大小不能超过 ${control.maxSize}MB`)
          return
        }
      }

      // 上传文件
      try {
        const uploadResults = await this.uploadFiles(files, control.uploadUrl)

        // 更新控件值
        const fileNames = uploadResults.map(result => result.name).join(', ')
        this.command.executeSetControlValue(element.control!.conceptId!, fileNames)

      } catch (error) {
        console.error('文件上传失败:', error)
        alert('文件上传失败')
      }
    }

    input.click()
  }

  // 处理签名控件点击
  public handleSignatureClick(element: IElement): void {
    // 打开签名面板
    this.openSignaturePanel((signatureData: string) => {
      // 将签名数据保存到控件
      this.command.executeSetControlValue(element.control!.conceptId!, signatureData)
    })
  }

  // 上传文件方法
  private async uploadFiles(files: File[], uploadUrl?: string): Promise<any[]> {
    if (!uploadUrl) {
      throw new Error('未配置上传地址')
    }

    const uploadPromises = files.map(file => {
      const formData = new FormData()
      formData.append('file', file)

      return fetch(uploadUrl, {
        method: 'POST',
        body: formData
      }).then(response => response.json())
    })

    return Promise.all(uploadPromises)
  }

  // 打开签名面板
  private openSignaturePanel(onComplete: (data: string) => void): void {
    // 创建签名面板DOM
    const panel = document.createElement('div')
    panel.className = 'signature-panel'
    panel.innerHTML = `
      <div class="signature-modal">
        <div class="signature-header">
          <h3>电子签名</h3>
          <button class="close-btn">×</button>
        </div>
        <div class="signature-body">
          <canvas id="signatureCanvas" width="400" height="200"></canvas>
        </div>
        <div class="signature-footer">
          <button class="clear-btn">清除</button>
          <button class="confirm-btn">确认</button>
        </div>
      </div>
    `

    document.body.appendChild(panel)

    // 初始化签名画布
    const canvas = panel.querySelector('#signatureCanvas') as HTMLCanvasElement
    const ctx = canvas.getContext('2d')!
    let isDrawing = false

    // 绑定绘制事件
    canvas.onmousedown = (e) => {
      isDrawing = true
      ctx.beginPath()
      ctx.moveTo(e.offsetX, e.offsetY)
    }

    canvas.onmousemove = (e) => {
      if (isDrawing) {
        ctx.lineTo(e.offsetX, e.offsetY)
        ctx.stroke()
      }
    }

    canvas.onmouseup = () => {
      isDrawing = false
    }

    // 绑定按钮事件
    panel.querySelector('.clear-btn')!.onclick = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
    }

    panel.querySelector('.confirm-btn')!.onclick = () => {
      const signatureData = canvas.toDataURL()
      onComplete(signatureData)
      document.body.removeChild(panel)
    }

    panel.querySelector('.close-btn')!.onclick = () => {
      document.body.removeChild(panel)
    }
  }
}
```

---

## 🔌 插件开发系统

### 1. 插件架构原理

#### 插件接口定义
```typescript
// 插件函数类型
type PluginFunction<Options = any> = (
  editor: Editor,
  options?: Options
) => PluginResult | void

// 插件返回结果
interface PluginResult {
  name: string                    // 插件名称
  version: string                // 插件版本
  cleanup?: () => void           // 清理函数
  [key: string]: any            // 其他API
}

// 插件选项接口
interface PluginOptions {
  enabled?: boolean              // 是否启用
  theme?: 'light' | 'dark'      // 主题
  customSettings?: Record<string, any>  // 自定义设置
}
```

#### 插件系统核心实现
```typescript
export class Plugin {
  private editor: Editor
  private installedPlugins: Map<string, PluginResult>

  constructor(editor: Editor) {
    this.editor = editor
    this.installedPlugins = new Map()
  }

  // 使用插件
  public use<Options>(
    pluginFunction: PluginFunction<Options>,
    options?: Options
  ): PluginResult | void {
    try {
      const result = pluginFunction(this.editor, options)

      if (result && result.name) {
        this.installedPlugins.set(result.name, result)
        console.log(`插件 ${result.name} v${result.version} 已安装`)
      }

      return result
    } catch (error) {
      console.error('插件安装失败:', error)
      throw error
    }
  }

  // 获取已安装的插件
  public getInstalledPlugins(): Map<string, PluginResult> {
    return this.installedPlugins
  }

  // 卸载插件
  public uninstall(pluginName: string): boolean {
    const plugin = this.installedPlugins.get(pluginName)
    if (plugin && plugin.cleanup) {
      plugin.cleanup()
      this.installedPlugins.delete(pluginName)
      console.log(`插件 ${pluginName} 已卸载`)
      return true
    }
    return false
  }
}
```

### 2. 完整插件开发示例

#### 自定义工具栏插件
```typescript
// 自定义工具栏插件选项
interface CustomToolbarOptions {
  position?: 'top' | 'bottom' | 'left' | 'right'
  buttons?: string[]
  theme?: 'light' | 'dark'
  customButtons?: CustomButtonConfig[]
}

interface CustomButtonConfig {
  key: string
  name: string
  icon: string
  action: (command: Command) => void
}

// 自定义工具栏插件实现
export function customToolbarPlugin(
  editor: Editor,
  options: CustomToolbarOptions = {}
): PluginResult {
  const {
    position = 'top',
    buttons = ['bold', 'italic', 'underline'],
    theme = 'light',
    customButtons = []
  } = options

  console.log('初始化自定义工具栏插件')

  // 创建工具栏容器
  const toolbarContainer = document.createElement('div')
  toolbarContainer.className = `custom-toolbar custom-toolbar-${position} theme-${theme}`

  // 插入到编辑器容器中
  const editorContainer = editor.command.getContainer()
  if (position === 'top') {
    editorContainer.insertBefore(toolbarContainer, editorContainer.firstChild)
  } else {
    editorContainer.appendChild(toolbarContainer)
  }

  // 内置按钮配置
  const builtinButtons: Record<string, CustomButtonConfig> = {
    bold: {
      key: 'bold',
      name: '加粗',
      icon: '𝐁',
      action: (command) => command.executeBold()
    },
    italic: {
      key: 'italic',
      name: '斜体',
      icon: '𝐼',
      action: (command) => command.executeItalic()
    },
    underline: {
      key: 'underline',
      name: '下划线',
      icon: '𝐔',
      action: (command) => command.executeUnderline()
    },
    strikethrough: {
      key: 'strikethrough',
      name: '删除线',
      icon: '𝐒',
      action: (command) => command.executeStrikeout()
    },
    color: {
      key: 'color',
      name: '字体颜色',
      icon: '🎨',
      action: (command) => {
        // 打开颜色选择器
        showColorPicker((color: string) => {
          command.executeColor(color)
        })
      }
    }
  }

  // 渲染按钮
  const allButtons = [
    ...buttons.map(key => builtinButtons[key]).filter(Boolean),
    ...customButtons
  ]

  allButtons.forEach(buttonConfig => {
    const button = createToolbarButton(buttonConfig, editor.command)
    toolbarContainer.appendChild(button)
  })

  // 监听编辑器状态变化，更新按钮状态
  editor.listener.rangeStyleChange = (payload) => {
    updateButtonStates(toolbarContainer, payload)
  }

  // 清理函数
  const cleanup = () => {
    console.log('清理自定义工具栏插件')
    if (toolbarContainer.parentNode) {
      toolbarContainer.parentNode.removeChild(toolbarContainer)
    }
  }

  return {
    name: 'customToolbar',
    version: '1.0.0',
    cleanup,
    getToolbarContainer: () => toolbarContainer,
    addButton: (buttonConfig: CustomButtonConfig) => {
      const button = createToolbarButton(buttonConfig, editor.command)
      toolbarContainer.appendChild(button)
    }
  }
}

// 创建工具栏按钮
function createToolbarButton(
  config: CustomButtonConfig,
  command: Command
): HTMLElement {
  const button = document.createElement('button')
  button.className = 'toolbar-button'
  button.title = config.name
  button.innerHTML = `<span class="button-icon">${config.icon}</span>`

  button.onclick = () => {
    config.action(command)
  }

  return button
}

// 更新按钮状态
function updateButtonStates(
  container: HTMLElement,
  styleInfo: any
): void {
  const buttons = container.querySelectorAll('.toolbar-button')
  buttons.forEach((button, index) => {
    const buttonElement = button as HTMLElement
    const key = buttonElement.dataset.key

    // 根据样式信息更新按钮激活状态
    if (key && styleInfo[key]) {
      buttonElement.classList.add('active')
    } else {
      buttonElement.classList.remove('active')
    }
  })
}

// 颜色选择器
function showColorPicker(onSelect: (color: string) => void): void {
  const colorPicker = document.createElement('input')
  colorPicker.type = 'color'
  colorPicker.onchange = () => {
    onSelect(colorPicker.value)
  }
  colorPicker.click()
}
```

#### 自动保存插件
```typescript
// 自动保存插件选项
interface AutoSaveOptions {
  interval?: number              // 保存间隔(毫秒)
  saveUrl?: string              // 保存接口地址
  onSave?: (data: any) => Promise<void>  // 自定义保存函数
  showNotification?: boolean     // 是否显示保存通知
}

// 自动保存插件实现
export function autoSavePlugin(
  editor: Editor,
  options: AutoSaveOptions = {}
): PluginResult {
  const {
    interval = 30000,           // 默认30秒
    saveUrl,
    onSave,
    showNotification = true
  } = options

  console.log('初始化自动保存插件')

  let saveTimer: NodeJS.Timeout | null = null
  let hasUnsavedChanges = false
  let lastSaveTime = Date.now()

  // 监听内容变化
  const originalContentChange = editor.listener.contentChange
  editor.listener.contentChange = () => {
    hasUnsavedChanges = true

    // 调用原始监听器
    if (originalContentChange) {
      originalContentChange()
    }

    // 重置定时器
    if (saveTimer) {
      clearTimeout(saveTimer)
    }

    saveTimer = setTimeout(() => {
      performAutoSave()
    }, interval)
  }

  // 执行自动保存
  const performAutoSave = async () => {
    if (!hasUnsavedChanges) return

    try {
      const data = editor.command.getValue()

      if (onSave) {
        await onSave(data)
      } else if (saveUrl) {
        await fetch(saveUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(data)
        })
      } else {
        // 默认保存到localStorage
        localStorage.setItem('canvas-editor-autosave', JSON.stringify(data))
      }

      hasUnsavedChanges = false
      lastSaveTime = Date.now()

      if (showNotification) {
        showSaveNotification('文档已自动保存')
      }

      console.log('自动保存成功')
    } catch (error) {
      console.error('自动保存失败:', error)
      if (showNotification) {
        showSaveNotification('自动保存失败', 'error')
      }
    }
  }

  // 手动保存
  const manualSave = async () => {
    await performAutoSave()
  }

  // 显示保存通知
  const showSaveNotification = (message: string, type: 'success' | 'error' = 'success') => {
    const notification = document.createElement('div')
    notification.className = `save-notification ${type}`
    notification.textContent = message
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 10px 20px;
      background: ${type === 'success' ? '#67C23A' : '#F56C6C'};
      color: white;
      border-radius: 4px;
      z-index: 9999;
      transition: opacity 0.3s;
    `

    document.body.appendChild(notification)

    setTimeout(() => {
      notification.style.opacity = '0'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 2000)
  }

  // 页面卸载前保存
  const beforeUnloadHandler = (e: BeforeUnloadEvent) => {
    if (hasUnsavedChanges) {
      e.preventDefault()
      e.returnValue = '您有未保存的更改，确定要离开吗？'
      return e.returnValue
    }
  }

  window.addEventListener('beforeunload', beforeUnloadHandler)

  // 注册快捷键 Ctrl+S
  editor.register.shortcutList([
    {
      key: 'S',
      ctrl: true,
      callback: () => {
        manualSave()
      }
    }
  ])

  // 清理函数
  const cleanup = () => {
    console.log('清理自动保存插件')
    if (saveTimer) {
      clearTimeout(saveTimer)
    }
    window.removeEventListener('beforeunload', beforeUnloadHandler)
  }

  return {
    name: 'autoSave',
    version: '1.0.0',
    cleanup,
    manualSave,
    getLastSaveTime: () => lastSaveTime,
    hasUnsavedChanges: () => hasUnsavedChanges
  }
}
```

#### 协作编辑插件
```typescript
// 协作编辑插件选项
interface CollaborationOptions {
  websocketUrl: string           // WebSocket服务器地址
  userId: string                // 用户ID
  userName: string              // 用户名
  roomId: string                // 房间ID
  cursorColor?: string          // 光标颜色
}

// 协作编辑插件实现
export function collaborationPlugin(
  editor: Editor,
  options: CollaborationOptions
): PluginResult {
  const {
    websocketUrl,
    userId,
    userName,
    roomId,
    cursorColor = '#007ACC'
  } = options

  console.log('初始化协作编辑插件')

  let ws: WebSocket | null = null
  let isConnected = false
  let collaborators: Map<string, CollaboratorInfo> = new Map()

  interface CollaboratorInfo {
    id: string
    name: string
    cursor: { x: number, y: number }
    color: string
  }

  // 建立WebSocket连接
  const connect = () => {
    ws = new WebSocket(websocketUrl)

    ws.onopen = () => {
      isConnected = true
      console.log('协作连接已建立')

      // 加入房间
      sendMessage({
        type: 'join',
        roomId,
        userId,
        userName
      })
    }

    ws.onmessage = (event) => {
      const message = JSON.parse(event.data)
      handleMessage(message)
    }

    ws.onclose = () => {
      isConnected = false
      console.log('协作连接已断开')

      // 尝试重连
      setTimeout(() => {
        if (!isConnected) {
          connect()
        }
      }, 3000)
    }

    ws.onerror = (error) => {
      console.error('协作连接错误:', error)
    }
  }

  // 发送消息
  const sendMessage = (message: any) => {
    if (ws && isConnected) {
      ws.send(JSON.stringify(message))
    }
  }

  // 处理接收到的消息
  const handleMessage = (message: any) => {
    switch (message.type) {
      case 'userJoined':
        handleUserJoined(message)
        break
      case 'userLeft':
        handleUserLeft(message)
        break
      case 'contentChange':
        handleContentChange(message)
        break
      case 'cursorMove':
        handleCursorMove(message)
        break
    }
  }

  // 处理用户加入
  const handleUserJoined = (message: any) => {
    collaborators.set(message.userId, {
      id: message.userId,
      name: message.userName,
      cursor: { x: 0, y: 0 },
      color: message.cursorColor || '#007ACC'
    })

    showCollaboratorNotification(`${message.userName} 加入了协作`)
    updateCollaboratorList()
  }

  // 处理用户离开
  const handleUserLeft = (message: any) => {
    const collaborator = collaborators.get(message.userId)
    if (collaborator) {
      collaborators.delete(message.userId)
      showCollaboratorNotification(`${collaborator.name} 离开了协作`)
      updateCollaboratorList()
    }
  }

  // 处理内容变化
  const handleContentChange = (message: any) => {
    if (message.userId !== userId) {
      // 应用其他用户的更改
      editor.command.setValue(message.content)
    }
  }

  // 处理光标移动
  const handleCursorMove = (message: any) => {
    if (message.userId !== userId) {
      const collaborator = collaborators.get(message.userId)
      if (collaborator) {
        collaborator.cursor = message.cursor
        renderCollaboratorCursors()
      }
    }
  }

  // 监听本地内容变化
  const originalContentChange = editor.listener.contentChange
  editor.listener.contentChange = () => {
    // 发送内容变化
    sendMessage({
      type: 'contentChange',
      roomId,
      userId,
      content: editor.command.getValue()
    })

    if (originalContentChange) {
      originalContentChange()
    }
  }

  // 监听光标位置变化
  const originalRangeStyleChange = editor.listener.rangeStyleChange
  editor.listener.rangeStyleChange = (payload) => {
    // 发送光标位置
    const range = editor.command.getRange()
    sendMessage({
      type: 'cursorMove',
      roomId,
      userId,
      cursor: {
        x: range.startIndex,
        y: range.endIndex
      }
    })

    if (originalRangeStyleChange) {
      originalRangeStyleChange(payload)
    }
  }

  // 渲染协作者光标
  const renderCollaboratorCursors = () => {
    // 清除现有光标
    const existingCursors = document.querySelectorAll('.collaborator-cursor')
    existingCursors.forEach(cursor => cursor.remove())

    // 渲染新光标
    collaborators.forEach(collaborator => {
      const cursor = document.createElement('div')
      cursor.className = 'collaborator-cursor'
      cursor.style.cssText = `
        position: absolute;
        width: 2px;
        height: 20px;
        background: ${collaborator.color};
        pointer-events: none;
        z-index: 1000;
      `

      // 添加用户名标签
      const label = document.createElement('div')
      label.className = 'collaborator-label'
      label.textContent = collaborator.name
      label.style.cssText = `
        position: absolute;
        top: -25px;
        left: 0;
        background: ${collaborator.color};
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 12px;
        white-space: nowrap;
      `

      cursor.appendChild(label)
      document.body.appendChild(cursor)

      // 计算光标位置（这里需要根据实际的位置计算逻辑）
      // const position = calculateCursorPosition(collaborator.cursor)
      // cursor.style.left = position.x + 'px'
      // cursor.style.top = position.y + 'px'
    })
  }

  // 更新协作者列表
  const updateCollaboratorList = () => {
    // 创建或更新协作者列表UI
    let collaboratorPanel = document.querySelector('.collaborator-panel') as HTMLElement
    if (!collaboratorPanel) {
      collaboratorPanel = document.createElement('div')
      collaboratorPanel.className = 'collaborator-panel'
      collaboratorPanel.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        z-index: 1000;
      `
      document.body.appendChild(collaboratorPanel)
    }

    collaboratorPanel.innerHTML = `
      <h4>协作者 (${collaborators.size + 1})</h4>
      <div class="collaborator-item">
        <span style="color: ${cursorColor};">●</span> ${userName} (我)
      </div>
      ${Array.from(collaborators.values()).map(collaborator => `
        <div class="collaborator-item">
          <span style="color: ${collaborator.color};">●</span> ${collaborator.name}
        </div>
      `).join('')}
    `
  }

  // 显示协作者通知
  const showCollaboratorNotification = (message: string) => {
    const notification = document.createElement('div')
    notification.className = 'collaborator-notification'
    notification.textContent = message
    notification.style.cssText = `
      position: fixed;
      top: 50px;
      right: 20px;
      background: #409EFF;
      color: white;
      padding: 10px 15px;
      border-radius: 4px;
      z-index: 9999;
      transition: opacity 0.3s;
    `

    document.body.appendChild(notification)

    setTimeout(() => {
      notification.style.opacity = '0'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 3000)
  }

  // 初始化连接
  connect()

  // 清理函数
  const cleanup = () => {
    console.log('清理协作编辑插件')
    if (ws) {
      ws.close()
    }

    // 清理UI元素
    const cursors = document.querySelectorAll('.collaborator-cursor')
    cursors.forEach(cursor => cursor.remove())

    const panel = document.querySelector('.collaborator-panel')
    if (panel) {
      panel.remove()
    }
  }

  return {
    name: 'collaboration',
    version: '1.0.0',
    cleanup,
    getCollaborators: () => Array.from(collaborators.values()),
    isConnected: () => isConnected,
    sendMessage
  }
}
```

---

## 🛠️ 工具栏定制

### 1. 内置工具栏按钮分析

#### 文本格式化按钮

##### 加粗按钮 (BoldButton)
```typescript
export class BoldButton {
  private element: HTMLDivElement
  private command: Command
  private isApple: boolean

  constructor(container: HTMLElement, command: Command) {
    this.command = command
    this.isApple = /Mac OS X/.test(navigator.userAgent)

    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.bold-button') as HTMLDivElement

    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `
      <div class="bold-button" title="加粗(${this.isApple ? '⌘' : 'Ctrl'}+B)">
        <i class="bold-icon">𝐁</i>
      </div>
    `
  }

  private bindEvents(): void {
    this.element.onclick = () => {
      this.command.executeBold()
    }
  }

  // 更新按钮状态
  public updateState(isBold: boolean): void {
    if (isBold) {
      this.element.classList.add('active')
    } else {
      this.element.classList.remove('active')
    }
  }

  public getElement(): HTMLDivElement {
    return this.element
  }
}
```

##### 颜色选择按钮 (ColorButton)
```typescript
export class ColorButton {
  private element: HTMLDivElement
  private colorControlDom: HTMLInputElement
  private colorSpanDom: HTMLSpanElement
  private command: Command

  constructor(container: HTMLElement, command: Command) {
    this.command = command
    this.createElement(container)
    this.bindEvents()
  }

  private createElement(container: HTMLElement): void {
    container.innerHTML = `
      <div class="color-button" title="字体颜色">
        <i class="color-icon">A</i>
        <span class="color-indicator"></span>
        <input type="color" class="color-control" value="#000000" style="display: none;">
      </div>
    `

    this.element = container.querySelector('.color-button') as HTMLDivElement
    this.colorControlDom = container.querySelector('.color-control') as HTMLInputElement
    this.colorSpanDom = container.querySelector('.color-indicator') as HTMLSpanElement
  }

  private bindEvents(): void {
    // 颜色选择事件
    this.colorControlDom.oninput = () => {
      this.command.executeColor(this.colorControlDom.value)
      this.updateColorIndicator(this.colorControlDom.value)
    }

    // 按钮点击事件
    this.element.onclick = () => {
      this.colorControlDom.click()
    }
  }

  private updateColorIndicator(color: string): void {
    this.colorSpanDom.style.backgroundColor = color
  }

  public updateState(color: string | null): void {
    if (color) {
      this.element.classList.add('active')
      this.colorControlDom.value = color
      this.updateColorIndicator(color)
    } else {
      this.element.classList.remove('active')
      this.colorControlDom.value = '#000000'
      this.updateColorIndicator('#000000')
    }
  }
}
```

##### 字体选择按钮 (FontButton)
```typescript
export class FontButton {
  private element: HTMLDivElement
  private selectElement: HTMLSpanElement
  private optionsElement: HTMLDivElement
  private command: Command
  private documentClickHandler: (e: MouseEvent) => void

  constructor(container: HTMLElement, command: Command) {
    this.command = command
    this.createElement(container)
    this.bindEvents()
  }

  private createElement(container: HTMLElement): void {
    container.innerHTML = `
      <div class="font-button">
        <span class="select" title="字体">微软雅黑</span>
        <div class="options">
          <ul>
            <li data-family="Microsoft YaHei" style="font-family:'Microsoft YaHei';">微软雅黑</li>
            <li data-family="SimSun" style="font-family:'SimSun';">宋体</li>
            <li data-family="SimHei" style="font-family:'SimHei';">黑体</li>
            <li data-family="KaiTi" style="font-family:'KaiTi';">楷体</li>
            <li data-family="FangSong" style="font-family:'FangSong';">仿宋</li>
            <li data-family="Arial" style="font-family:'Arial';">Arial</li>
            <li data-family="Times New Roman" style="font-family:'Times New Roman';">Times New Roman</li>
            <li data-family="Courier New" style="font-family:'Courier New';">Courier New</li>
          </ul>
        </div>
      </div>
    `

    this.element = container.querySelector('.font-button') as HTMLDivElement
    this.selectElement = container.querySelector('.select') as HTMLSpanElement
    this.optionsElement = container.querySelector('.options') as HTMLDivElement
  }

  private bindEvents(): void {
    // 下拉框切换
    this.element.onclick = (e) => {
      e.stopPropagation()
      this.optionsElement.classList.toggle('visible')
    }

    // 字体选择
    this.optionsElement.onclick = (evt) => {
      evt.stopPropagation()
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        const family = li.dataset.family
        if (family) {
          this.command.executeFont(family)
          this.selectElement.textContent = li.textContent || family
          this.optionsElement.classList.remove('visible')
        }
      }
    }

    // 点击外部关闭下拉框
    this.documentClickHandler = (e) => {
      if (!this.element.contains(e.target as Node)) {
        this.optionsElement.classList.remove('visible')
      }
    }
    document.addEventListener('click', this.documentClickHandler)
  }

  public updateState(fontFamily: string | null): void {
    if (fontFamily) {
      // 查找对应的字体名称
      const option = this.optionsElement.querySelector(`[data-family="${fontFamily}"]`)
      if (option) {
        this.selectElement.textContent = option.textContent || fontFamily
      }
    }
  }

  public destroy(): void {
    document.removeEventListener('click', this.documentClickHandler)
  }
}
```

#### 段落格式化按钮

##### 对齐按钮组
```typescript
// 左对齐按钮
export class AlignLeftButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = `
      <div class="align-left-button" title="左对齐(Ctrl+L)">
        <i class="align-left-icon">⬅</i>
      </div>
    `
    this.dom = this.dom.firstElementChild as HTMLDivElement
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executeRowFlex(RowFlex.LEFT)
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}

// 居中对齐按钮
export class AlignCenterButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = `
      <div class="align-center-button" title="居中对齐(Ctrl+E)">
        <i class="align-center-icon">⬌</i>
      </div>
    `
    this.dom = this.dom.firstElementChild as HTMLDivElement
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executeRowFlex(RowFlex.CENTER)
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}

// 右对齐按钮
export class AlignRightButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = `
      <div class="align-right-button" title="右对齐(Ctrl+R)">
        <i class="align-right-icon">➡</i>
      </div>
    `
    this.dom = this.dom.firstElementChild as HTMLDivElement
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executeRowFlex(RowFlex.RIGHT)
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}

// 两端对齐按钮
export class AlignJustifyButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = `
      <div class="align-justify-button" title="两端对齐(Ctrl+J)">
        <i class="align-justify-icon">⬌⬌</i>
      </div>
    `
    this.dom = this.dom.firstElementChild as HTMLDivElement
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executeRowFlex(RowFlex.ALIGNMENT)
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}
```

##### 列表按钮 (ListButton)
```typescript
export class ListButton {
  private dom: HTMLDivElement
  private optionDom: HTMLDivElement
  private instance: CanvasEditor
  private documentClickHandler: (e: MouseEvent) => void

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = `
      <div class="list-button">
        <span class="select" title="列表(Ctrl+Shift+U)">
          <i class="list-icon">≡</i>
        </span>
        <div class="options">
          <ul>
            <li data-list-type="${ListType.UL}" data-list-style="${ListStyle.DISC}">
              <i class="list-style-icon">•</i> 无序列表(圆点)
            </li>
            <li data-list-type="${ListType.UL}" data-list-style="${ListStyle.CIRCLE}">
              <i class="list-style-icon">○</i> 无序列表(空心圆)
            </li>
            <li data-list-type="${ListType.UL}" data-list-style="${ListStyle.SQUARE}">
              <i class="list-style-icon">■</i> 无序列表(方块)
            </li>
            <li data-list-type="${ListType.OL}" data-list-style="${ListStyle.DECIMAL}">
              <i class="list-style-icon">1.</i> 有序列表(数字)
            </li>
            <li data-list-type="${ListType.OL}" data-list-style="${ListStyle.LOWER_ALPHA}">
              <i class="list-style-icon">a.</i> 有序列表(小写字母)
            </li>
            <li data-list-type="${ListType.OL}" data-list-style="${ListStyle.UPPER_ALPHA}">
              <i class="list-style-icon">A.</i> 有序列表(大写字母)
            </li>
            <li data-list-type="${ListType.OL}" data-list-style="${ListStyle.LOWER_ROMAN}">
              <i class="list-style-icon">i.</i> 有序列表(小写罗马)
            </li>
            <li data-list-type="${ListType.OL}" data-list-style="${ListStyle.UPPER_ROMAN}">
              <i class="list-style-icon">I.</i> 有序列表(大写罗马)
            </li>
          </ul>
        </div>
      </div>
    `
    this.dom = this.dom.firstElementChild as HTMLDivElement
    this.optionDom = this.dom.querySelector<HTMLDivElement>('.options')!

    this.bindEvents()
  }

  private bindEvents(): void {
    // 下拉框切换
    this.dom.onclick = (e) => {
      e.stopPropagation()
      this.optionDom.classList.toggle('visible')
    }

    // 列表类型选择
    this.optionDom.onclick = (evt) => {
      evt.stopPropagation()
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        const listType = li.dataset.listType as ListType
        const listStyle = li.dataset.listStyle as ListStyle

        if (listType && listStyle) {
          this.instance.command.executeList(listType, listStyle)
          this.optionDom.classList.remove('visible')
        }
      }
    }

    // 点击外部关闭下拉框
    this.documentClickHandler = (e) => {
      if (!this.dom.contains(e.target as Node)) {
        this.optionDom.classList.remove('visible')
      }
    }
    document.addEventListener('click', this.documentClickHandler)
  }

  public destroy(): void {
    document.removeEventListener('click', this.documentClickHandler)
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}
```

#### 插入功能按钮

##### 表格按钮 (TableButton)
```typescript
export class TableButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = `
      <div class="table-button" title="插入表格">
        <i class="table-icon">⊞</i>
        <span>表格</span>
      </div>
    `
    this.dom = this.dom.firstElementChild as HTMLDivElement
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.showTableSizeSelector()
    }
  }

  private showTableSizeSelector(): void {
    // 创建表格尺寸选择器
    const selector = document.createElement('div')
    selector.className = 'table-size-selector'
    selector.innerHTML = `
      <div class="table-size-grid">
        ${this.generateTableGrid(8, 8)}
      </div>
      <div class="table-size-info">
        <span class="size-text">1 x 1 表格</span>
      </div>
      <div class="table-size-actions">
        <button class="custom-size-btn">自定义大小</button>
      </div>
    `

    // 定位选择器
    const rect = this.dom.getBoundingClientRect()
    selector.style.cssText = `
      position: fixed;
      left: ${rect.left}px;
      top: ${rect.bottom + 5}px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      z-index: 1000;
    `

    document.body.appendChild(selector)

    // 绑定网格悬停事件
    this.bindGridEvents(selector)

    // 点击外部关闭
    const closeHandler = (e: MouseEvent) => {
      if (!selector.contains(e.target as Node) && !this.dom.contains(e.target as Node)) {
        document.body.removeChild(selector)
        document.removeEventListener('click', closeHandler)
      }
    }
    document.addEventListener('click', closeHandler)
  }

  private generateTableGrid(rows: number, cols: number): string {
    let html = ''
    for (let i = 0; i < rows; i++) {
      for (let j = 0; j < cols; j++) {
        html += `<div class="grid-cell" data-row="${i}" data-col="${j}"></div>`
      }
    }
    return html
  }

  private bindGridEvents(selector: HTMLElement): void {
    const cells = selector.querySelectorAll('.grid-cell')
    const sizeText = selector.querySelector('.size-text') as HTMLElement

    cells.forEach(cell => {
      cell.addEventListener('mouseenter', () => {
        const row = parseInt((cell as HTMLElement).dataset.row!)
        const col = parseInt((cell as HTMLElement).dataset.col!)

        // 高亮选中区域
        cells.forEach(c => {
          const cRow = parseInt((c as HTMLElement).dataset.row!)
          const cCol = parseInt((c as HTMLElement).dataset.col!)

          if (cRow <= row && cCol <= col) {
            c.classList.add('highlighted')
          } else {
            c.classList.remove('highlighted')
          }
        })

        // 更新尺寸文本
        sizeText.textContent = `${row + 1} x ${col + 1} 表格`
      })

      cell.addEventListener('click', () => {
        const row = parseInt((cell as HTMLElement).dataset.row!)
        const col = parseInt((cell as HTMLElement).dataset.col!)

        // 插入表格
        this.instance.command.executeInsertTable(row + 1, col + 1)

        // 关闭选择器
        document.body.removeChild(selector)
      })
    })

    // 自定义大小按钮
    const customBtn = selector.querySelector('.custom-size-btn') as HTMLElement
    customBtn.onclick = () => {
      this.showCustomSizeDialog()
      document.body.removeChild(selector)
    }
  }

  private showCustomSizeDialog(): void {
    const dialog = document.createElement('div')
    dialog.className = 'table-custom-dialog'
    dialog.innerHTML = `
      <div class="dialog-overlay">
        <div class="dialog-content">
          <h3>插入表格</h3>
          <div class="dialog-body">
            <div class="form-group">
              <label>行数:</label>
              <input type="number" id="tableRows" value="3" min="1" max="20">
            </div>
            <div class="form-group">
              <label>列数:</label>
              <input type="number" id="tableCols" value="3" min="1" max="20">
            </div>
          </div>
          <div class="dialog-footer">
            <button class="cancel-btn">取消</button>
            <button class="confirm-btn">确定</button>
          </div>
        </div>
      </div>
    `

    document.body.appendChild(dialog)

    // 绑定事件
    const rowsInput = dialog.querySelector('#tableRows') as HTMLInputElement
    const colsInput = dialog.querySelector('#tableCols') as HTMLInputElement
    const cancelBtn = dialog.querySelector('.cancel-btn') as HTMLElement
    const confirmBtn = dialog.querySelector('.confirm-btn') as HTMLElement

    cancelBtn.onclick = () => {
      document.body.removeChild(dialog)
    }

    confirmBtn.onclick = () => {
      const rows = parseInt(rowsInput.value)
      const cols = parseInt(colsInput.value)

      if (rows > 0 && cols > 0) {
        this.instance.command.executeInsertTable(rows, cols)
      }

      document.body.removeChild(dialog)
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}
```

##### 图片按钮 (ImageButton)
```typescript
export class ImageButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = `
      <div class="image-button" title="插入图片">
        <i class="image-icon">🖼</i>
        <span>图片</span>
      </div>
    `
    this.dom = this.dom.firstElementChild as HTMLDivElement
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.showImageOptions()
    }
  }

  private showImageOptions(): void {
    const options = document.createElement('div')
    options.className = 'image-options'
    options.innerHTML = `
      <div class="image-option" data-action="upload">
        <i class="option-icon">📁</i>
        <span>上传图片</span>
      </div>
      <div class="image-option" data-action="url">
        <i class="option-icon">🔗</i>
        <span>网络图片</span>
      </div>
      <div class="image-option" data-action="base64">
        <i class="option-icon">📋</i>
        <span>Base64图片</span>
      </div>
    `

    // 定位选项菜单
    const rect = this.dom.getBoundingClientRect()
    options.style.cssText = `
      position: fixed;
      left: ${rect.left}px;
      top: ${rect.bottom + 5}px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      z-index: 1000;
    `

    document.body.appendChild(options)

    // 绑定选项事件
    options.onclick = (e) => {
      const option = (e.target as HTMLElement).closest('.image-option') as HTMLElement
      if (option) {
        const action = option.dataset.action

        switch (action) {
          case 'upload':
            this.handleImageUpload()
            break
          case 'url':
            this.handleImageUrl()
            break
          case 'base64':
            this.handleImageBase64()
            break
        }

        document.body.removeChild(options)
      }
    }

    // 点击外部关闭
    const closeHandler = (e: MouseEvent) => {
      if (!options.contains(e.target as Node) && !this.dom.contains(e.target as Node)) {
        document.body.removeChild(options)
        document.removeEventListener('click', closeHandler)
      }
    }
    document.addEventListener('click', closeHandler)
  }

  private handleImageUpload(): void {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.png,.jpg,.jpeg,.gif,.webp,.svg'
    input.multiple = false

    input.onchange = () => {
      const file = input.files?.[0]
      if (file) {
        // 检查文件大小 (限制为5MB)
        if (file.size > 5 * 1024 * 1024) {
          alert('图片大小不能超过5MB')
          return
        }

        const reader = new FileReader()
        reader.onload = () => {
          const imageData = reader.result as string
          this.insertImage(imageData)
        }
        reader.readAsDataURL(file)
      }
    }

    input.click()
  }

  private handleImageUrl(): void {
    const url = prompt('请输入图片URL:')
    if (url) {
      // 验证URL格式
      if (this.isValidImageUrl(url)) {
        this.insertImage(url)
      } else {
        alert('请输入有效的图片URL')
      }
    }
  }

  private handleImageBase64(): void {
    const base64 = prompt('请输入Base64图片数据:')
    if (base64) {
      // 验证Base64格式
      if (this.isValidBase64Image(base64)) {
        this.insertImage(base64)
      } else {
        alert('请输入有效的Base64图片数据')
      }
    }
  }

  private insertImage(src: string): void {
    this.instance.command.executeImage({
      value: '',
      type: ElementType.IMAGE,
      src: src,
      width: 200,  // 默认宽度
      height: 150  // 默认高度
    })
  }

  private isValidImageUrl(url: string): boolean {
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg)$/i
    return /^https?:\/\/.+/.test(url) && imageExtensions.test(url)
  }

  private isValidBase64Image(base64: string): boolean {
    return /^data:image\/(png|jpg|jpeg|gif|webp);base64,/.test(base64)
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}
```

##### 超链接按钮 (HyperlinkButton)
```typescript
export class HyperlinkButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = `
      <div class="hyperlink-button" title="插入超链接(Ctrl+K)">
        <i class="hyperlink-icon">🔗</i>
        <span>链接</span>
      </div>
    `
    this.dom = this.dom.firstElementChild as HTMLDivElement
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.showHyperlinkDialog()
    }
  }

  private showHyperlinkDialog(): void {
    const selectedText = this.instance.command.getRangeText()

    const dialog = document.createElement('div')
    dialog.className = 'hyperlink-dialog'
    dialog.innerHTML = `
      <div class="dialog-overlay">
        <div class="dialog-content">
          <h3>插入超链接</h3>
          <div class="dialog-body">
            <div class="form-group">
              <label>显示文本:</label>
              <input type="text" id="linkText" value="${selectedText}" placeholder="请输入显示文本">
            </div>
            <div class="form-group">
              <label>链接地址:</label>
              <input type="url" id="linkUrl" placeholder="请输入链接地址">
            </div>
            <div class="form-group">
              <label>
                <input type="checkbox" id="openInNewTab" checked>
                在新标签页中打开
              </label>
            </div>
          </div>
          <div class="dialog-footer">
            <button class="cancel-btn">取消</button>
            <button class="confirm-btn">确定</button>
          </div>
        </div>
      </div>
    `

    document.body.appendChild(dialog)

    // 获取输入元素
    const textInput = dialog.querySelector('#linkText') as HTMLInputElement
    const urlInput = dialog.querySelector('#linkUrl') as HTMLInputElement
    const newTabCheckbox = dialog.querySelector('#openInNewTab') as HTMLInputElement
    const cancelBtn = dialog.querySelector('.cancel-btn') as HTMLElement
    const confirmBtn = dialog.querySelector('.confirm-btn') as HTMLElement

    // 聚焦到URL输入框
    urlInput.focus()

    // 绑定事件
    cancelBtn.onclick = () => {
      document.body.removeChild(dialog)
    }

    confirmBtn.onclick = () => {
      const text = textInput.value.trim()
      const url = urlInput.value.trim()

      if (!text) {
        alert('请输入显示文本')
        textInput.focus()
        return
      }

      if (!url) {
        alert('请输入链接地址')
        urlInput.focus()
        return
      }

      // 验证URL格式
      if (!this.isValidUrl(url)) {
        alert('请输入有效的链接地址')
        urlInput.focus()
        return
      }

      // 插入超链接
      this.instance.command.executeHyperlink({
        type: ElementType.HYPERLINK,
        value: text,
        url: url,
        target: newTabCheckbox.checked ? '_blank' : '_self'
      })

      document.body.removeChild(dialog)
    }

    // 回车键确认
    const handleEnter = (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        confirmBtn.click()
      }
    }

    textInput.addEventListener('keydown', handleEnter)
    urlInput.addEventListener('keydown', handleEnter)
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      // 如果不是完整URL，检查是否是相对路径或简单格式
      return /^(https?:\/\/|\/|\.\/|#)/.test(url) || /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/.test(url)
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}
```

### 2. 自定义工具栏管理器

#### 工具栏管理器实现
```typescript
export class CustomToolbarManager {
  private container: HTMLElement
  private command: Command
  private buttons: Map<string, ToolbarButton>
  private groups: Map<string, ToolbarGroup>

  constructor(container: HTMLElement, command: Command) {
    this.container = container
    this.command = command
    this.buttons = new Map()
    this.groups = new Map()
    this.initializeToolbar()
  }

  private initializeToolbar(): void {
    this.container.className = 'custom-toolbar'

    // 创建默认按钮组
    this.createGroup('format', '格式化')
    this.createGroup('paragraph', '段落')
    this.createGroup('insert', '插入')
    this.createGroup('tools', '工具')

    // 添加默认按钮
    this.addDefaultButtons()
  }

  // 创建按钮组
  public createGroup(key: string, name: string): ToolbarGroup {
    const group = new ToolbarGroup(key, name)
    this.groups.set(key, group)
    this.container.appendChild(group.getElement())
    return group
  }

  // 添加按钮到指定组
  public addButton(
    groupKey: string,
    buttonKey: string,
    button: ToolbarButton
  ): void {
    const group = this.groups.get(groupKey)
    if (group) {
      group.addButton(buttonKey, button)
      this.buttons.set(buttonKey, button)
    }
  }

  // 移除按钮
  public removeButton(buttonKey: string): void {
    const button = this.buttons.get(buttonKey)
    if (button) {
      // 从所有组中移除
      this.groups.forEach(group => {
        group.removeButton(buttonKey)
      })
      this.buttons.delete(buttonKey)
    }
  }

  // 获取按钮
  public getButton(key: string): ToolbarButton | undefined {
    return this.buttons.get(key)
  }

  // 更新按钮状态
  public updateButtonStates(styleInfo: any): void {
    this.buttons.forEach((button, key) => {
      if (button.updateState) {
        button.updateState(styleInfo[key])
      }
    })
  }

  // 添加默认按钮
  private addDefaultButtons(): void {
    // 格式化组
    this.addButton('format', 'bold', new BoldButton(this.command))
    this.addButton('format', 'italic', new ItalicButton(this.command))
    this.addButton('format', 'underline', new UnderlineButton(this.command))
    this.addButton('format', 'strikethrough', new StrikethroughButton(this.command))
    this.addButton('format', 'color', new ColorButton(this.command))
    this.addButton('format', 'highlight', new HighlightButton(this.command))

    // 段落组
    this.addButton('paragraph', 'alignLeft', new AlignLeftButton(this.command))
    this.addButton('paragraph', 'alignCenter', new AlignCenterButton(this.command))
    this.addButton('paragraph', 'alignRight', new AlignRightButton(this.command))
    this.addButton('paragraph', 'alignJustify', new AlignJustifyButton(this.command))
    this.addButton('paragraph', 'list', new ListButton(this.command))

    // 插入组
    this.addButton('insert', 'table', new TableButton(this.command))
    this.addButton('insert', 'image', new ImageButton(this.command))
    this.addButton('insert', 'hyperlink', new HyperlinkButton(this.command))
    this.addButton('insert', 'pageBreak', new PageBreakButton(this.command))

    // 工具组
    this.addButton('tools', 'undo', new UndoButton(this.command))
    this.addButton('tools', 'redo', new RedoButton(this.command))
    this.addButton('tools', 'search', new SearchButton(this.command))
    this.addButton('tools', 'print', new PrintButton(this.command))
  }
}

// 工具栏按钮基类
export abstract class ToolbarButton {
  protected element: HTMLElement
  protected command: Command

  constructor(command: Command) {
    this.command = command
    this.createElement()
    this.bindEvents()
  }

  protected abstract createElement(): void
  protected abstract bindEvents(): void

  public getElement(): HTMLElement {
    return this.element
  }

  public updateState?(value: any): void
}

// 工具栏按钮组
export class ToolbarGroup {
  private element: HTMLElement
  private key: string
  private name: string
  private buttons: Map<string, ToolbarButton>

  constructor(key: string, name: string) {
    this.key = key
    this.name = name
    this.buttons = new Map()
    this.createElement()
  }

  private createElement(): void {
    this.element = document.createElement('div')
    this.element.className = 'toolbar-group'
    this.element.dataset.group = this.key

    // 添加组标题（可选）
    if (this.name) {
      const title = document.createElement('div')
      title.className = 'group-title'
      title.textContent = this.name
      this.element.appendChild(title)
    }

    // 添加按钮容器
    const buttonContainer = document.createElement('div')
    buttonContainer.className = 'group-buttons'
    this.element.appendChild(buttonContainer)
  }

  public addButton(key: string, button: ToolbarButton): void {
    this.buttons.set(key, button)
    const buttonContainer = this.element.querySelector('.group-buttons')!
    buttonContainer.appendChild(button.getElement())
  }

  public removeButton(key: string): void {
    const button = this.buttons.get(key)
    if (button) {
      const buttonElement = button.getElement()
      if (buttonElement.parentNode) {
        buttonElement.parentNode.removeChild(buttonElement)
      }
      this.buttons.delete(key)
    }
  }

  public getElement(): HTMLElement {
    return this.element
  }

  public getButtons(): Map<string, ToolbarButton> {
    return this.buttons
  }
}
```

---

## 📡 事件系统扩展

### 1. 事件监听器 (Listener) 系统

#### 内置事件监听器
```typescript
export class Listener {
  // 内容变化监听
  public contentChange?: () => void

  // 选区样式变化监听
  public rangeStyleChange?: (payload: IRangeStyle) => void

  // 可见页面变化监听
  public visiblePageNoListChange?: (payload: number[]) => void

  // 页面尺寸变化监听
  public pageSizeChange?: (payload: IPageSizeChangePayload) => void

  // 页面缩放变化监听
  public pageScaleChange?: (payload: number) => void

  // 控件值变化监听
  public controlChange?: (payload: IControlChangePayload) => void

  // 保存快捷键监听
  public saved?: (payload: IEditorData) => void

  // 页面模式变化监听
  public pageModeChange?: (payload: PageMode) => void
}

// 选区样式信息接口
interface IRangeStyle {
  bold?: boolean
  italic?: boolean
  underline?: boolean
  strikethrough?: boolean
  color?: string
  highlight?: string
  font?: string
  size?: number
  rowFlex?: RowFlex
  listType?: ListType
  listStyle?: ListStyle
}

// 页面尺寸变化载荷
interface IPageSizeChangePayload {
  width: number
  height: number
  pageNo: number
}

// 控件变化载荷
interface IControlChangePayload {
  controlId: string
  value: string
  control: IControl
}
```

#### 事件监听器使用示例
```typescript
// 基础事件监听
const editor = new Editor(container, data, options)

// 监听内容变化
editor.listener.contentChange = () => {
  console.log('文档内容发生变化')

  // 获取当前文档数据
  const currentData = editor.command.getValue()

  // 自动保存到本地存储
  localStorage.setItem('document-backup', JSON.stringify(currentData))

  // 标记文档为未保存状态
  markDocumentAsUnsaved()
}

// 监听选区样式变化
editor.listener.rangeStyleChange = (styleInfo) => {
  console.log('选区样式变化:', styleInfo)

  // 更新工具栏按钮状态
  updateToolbarButtonStates(styleInfo)

  // 更新状态栏信息
  updateStatusBar(styleInfo)
}

// 监听页面变化
editor.listener.visiblePageNoListChange = (pageNumbers) => {
  console.log('可见页面:', pageNumbers)

  // 更新页面导航
  updatePageNavigation(pageNumbers)
}

// 监听控件值变化
editor.listener.controlChange = (payload) => {
  console.log('控件值变化:', payload)

  // 验证控件值
  validateControlValue(payload)

  // 触发表单验证
  triggerFormValidation()
}

// 监听保存事件
editor.listener.saved = (data) => {
  console.log('文档已保存')

  // 发送到服务器
  saveToServer(data)

  // 显示保存成功提示
  showSaveSuccessNotification()
}

// 辅助函数实现
function updateToolbarButtonStates(styleInfo: IRangeStyle): void {
  // 更新加粗按钮
  const boldButton = document.querySelector('.bold-button')
  if (boldButton) {
    if (styleInfo.bold) {
      boldButton.classList.add('active')
    } else {
      boldButton.classList.remove('active')
    }
  }

  // 更新斜体按钮
  const italicButton = document.querySelector('.italic-button')
  if (italicButton) {
    if (styleInfo.italic) {
      italicButton.classList.add('active')
    } else {
      italicButton.classList.remove('active')
    }
  }

  // 更新字体选择器
  const fontSelector = document.querySelector('.font-selector') as HTMLSelectElement
  if (fontSelector && styleInfo.font) {
    fontSelector.value = styleInfo.font
  }

  // 更新字号选择器
  const sizeSelector = document.querySelector('.size-selector') as HTMLSelectElement
  if (sizeSelector && styleInfo.size) {
    sizeSelector.value = styleInfo.size.toString()
  }
}

function updateStatusBar(styleInfo: IRangeStyle): void {
  const statusBar = document.querySelector('.status-bar')
  if (statusBar) {
    const statusText = []

    if (styleInfo.font) statusText.push(`字体: ${styleInfo.font}`)
    if (styleInfo.size) statusText.push(`字号: ${styleInfo.size}`)
    if (styleInfo.bold) statusText.push('加粗')
    if (styleInfo.italic) statusText.push('斜体')

    statusBar.textContent = statusText.join(' | ')
  }
}
```

### 2. 事件总线 (EventBus) 系统

#### EventBus 核心实现
```typescript
export class EventBus<T extends Record<string, any>> {
  private events: Map<keyof T, Array<(payload: any) => void>>

  constructor() {
    this.events = new Map()
  }

  // 监听事件
  public on<K extends keyof T>(
    event: K,
    callback: (payload: T[K]) => void
  ): void {
    if (!this.events.has(event)) {
      this.events.set(event, [])
    }
    this.events.get(event)!.push(callback)
  }

  // 移除事件监听
  public off<K extends keyof T>(
    event: K,
    callback?: (payload: T[K]) => void
  ): void {
    const callbacks = this.events.get(event)
    if (callbacks) {
      if (callback) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
      } else {
        // 移除所有监听器
        this.events.delete(event)
      }
    }
  }

  // 触发事件
  public emit<K extends keyof T>(event: K, payload: T[K]): void {
    const callbacks = this.events.get(event)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(payload)
        } catch (error) {
          console.error(`事件 ${String(event)} 处理器执行失败:`, error)
        }
      })
    }
  }

  // 一次性监听
  public once<K extends keyof T>(
    event: K,
    callback: (payload: T[K]) => void
  ): void {
    const onceCallback = (payload: T[K]) => {
      callback(payload)
      this.off(event, onceCallback)
    }
    this.on(event, onceCallback)
  }

  // 清除所有事件监听
  public clear(): void {
    this.events.clear()
  }
}

// 事件映射接口
interface EventBusMap {
  contentChange: IEditorData
  selectionChange: ISelectionChangePayload
  beforePaste: IBeforePastePayload
  afterPaste: IAfterPastePayload
  imageLoad: IImageLoadPayload
  tableCreate: ITableCreatePayload
  controlValueChange: IControlChangePayload
  pageBreak: IPageBreakPayload
  hyperlinkClick: IHyperlinkClickPayload
  customEvent: any
}

// 事件载荷接口定义
interface ISelectionChangePayload {
  startIndex: number
  endIndex: number
  selectedText: string
  selectedElements: IElement[]
}

interface IBeforePastePayload {
  data: string
  type: 'text' | 'html' | 'image'
  preventDefault: () => void
}

interface IAfterPastePayload {
  insertedElements: IElement[]
  insertedText: string
}

interface IImageLoadPayload {
  src: string
  width: number
  height: number
  element: IElement
}

interface ITableCreatePayload {
  rows: number
  cols: number
  tableId: string
}

interface IPageBreakPayload {
  pageNo: number
  position: number
}

interface IHyperlinkClickPayload {
  url: string
  text: string
  element: IElement
  event: MouseEvent
}
```

#### EventBus 使用示例
```typescript
// 获取事件总线实例
const eventBus = editor.eventBus

// 监听内容变化事件
eventBus.on('contentChange', (data) => {
  console.log('内容变化事件:', data)

  // 执行自动保存
  autoSave(data)

  // 更新字数统计
  updateWordCount(data)
})

// 监听选区变化事件
eventBus.on('selectionChange', (payload) => {
  console.log('选区变化:', payload)

  // 更新选区信息显示
  updateSelectionInfo(payload)

  // 检查是否可以执行某些操作
  checkOperationAvailability(payload)
})

// 监听粘贴前事件
eventBus.on('beforePaste', (payload) => {
  console.log('即将粘贴:', payload)

  // 检查粘贴内容
  if (payload.type === 'html' && containsMaliciousContent(payload.data)) {
    // 阻止粘贴
    payload.preventDefault()
    alert('检测到不安全的内容，已阻止粘贴')
  }
})

// 监听图片加载事件
eventBus.on('imageLoad', (payload) => {
  console.log('图片加载完成:', payload)

  // 检查图片尺寸
  if (payload.width > 800 || payload.height > 600) {
    // 提示用户图片过大
    showImageSizeWarning(payload)
  }
})

// 监听超链接点击事件
eventBus.on('hyperlinkClick', (payload) => {
  console.log('超链接点击:', payload)

  // 自定义链接处理逻辑
  if (payload.url.startsWith('internal:')) {
    // 处理内部链接
    handleInternalLink(payload.url)
    payload.event.preventDefault()
  } else if (payload.url.startsWith('mailto:')) {
    // 处理邮件链接
    handleMailtoLink(payload.url)
  }
})

// 触发自定义事件
eventBus.emit('customEvent', {
  type: 'userAction',
  action: 'customFormat',
  data: { format: 'highlight', color: '#ffff00' }
})

// 一次性监听
eventBus.once('tableCreate', (payload) => {
  console.log('首次创建表格:', payload)
  showTableTutorial()
})

// 辅助函数
function autoSave(data: IEditorData): void {
  // 防抖处理
  clearTimeout(autoSaveTimer)
  autoSaveTimer = setTimeout(() => {
    localStorage.setItem('auto-save', JSON.stringify(data))
    console.log('自动保存完成')
  }, 2000)
}

function updateWordCount(data: IEditorData): void {
  const wordCount = calculateWordCount(data)
  const wordCountElement = document.querySelector('.word-count')
  if (wordCountElement) {
    wordCountElement.textContent = `字数: ${wordCount}`
  }
}

function containsMaliciousContent(html: string): boolean {
  // 检查是否包含恶意脚本
  const dangerousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /<iframe/i
  ]

  return dangerousPatterns.some(pattern => pattern.test(html))
}

function showImageSizeWarning(payload: IImageLoadPayload): void {
  const warning = document.createElement('div')
  warning.className = 'image-size-warning'
  warning.innerHTML = `
    <div class="warning-content">
      <p>图片尺寸较大 (${payload.width}x${payload.height})，可能影响文档性能。</p>
      <button onclick="resizeImage('${payload.src}')">调整大小</button>
      <button onclick="closeWarning(this)">忽略</button>
    </div>
  `
  document.body.appendChild(warning)
}

let autoSaveTimer: NodeJS.Timeout
```

### 3. 自定义事件处理器

#### 创建自定义事件处理器
```typescript
export class CustomEventHandler {
  private editor: Editor
  private eventBus: EventBus<EventBusMap>

  constructor(editor: Editor) {
    this.editor = editor
    this.eventBus = editor.eventBus
    this.registerCustomEvents()
  }

  private registerCustomEvents(): void {
    // 注册文档统计事件
    this.eventBus.on('contentChange', (data) => {
      this.handleDocumentStats(data)
    })

    // 注册协作事件
    this.eventBus.on('selectionChange', (payload) => {
      this.handleCollaborativeSelection(payload)
    })

    // 注册安全检查事件
    this.eventBus.on('beforePaste', (payload) => {
      this.handleSecurityCheck(payload)
    })
  }

  // 处理文档统计
  private handleDocumentStats(data: IEditorData): void {
    const stats = this.calculateDocumentStats(data)

    // 更新统计信息显示
    this.updateStatsDisplay(stats)

    // 触发统计更新事件
    this.eventBus.emit('customEvent', {
      type: 'statsUpdate',
      stats
    })
  }

  // 处理协作选区
  private handleCollaborativeSelection(payload: ISelectionChangePayload): void {
    // 发送选区信息到协作服务器
    if (this.isCollaborativeMode()) {
      this.sendSelectionToServer(payload)
    }
  }

  // 处理安全检查
  private handleSecurityCheck(payload: IBeforePastePayload): void {
    if (payload.type === 'html') {
      const sanitizedHtml = this.sanitizeHtml(payload.data)
      if (sanitizedHtml !== payload.data) {
        console.warn('检测到潜在不安全内容，已进行清理')
        // 可以选择替换内容或阻止粘贴
      }
    }
  }

  // 计算文档统计
  private calculateDocumentStats(data: IEditorData): DocumentStats {
    let wordCount = 0
    let charCount = 0
    let paragraphCount = 0
    let imageCount = 0
    let tableCount = 0

    const processElements = (elements: IElement[]) => {
      elements.forEach(element => {
        if (element.type === ElementType.TEXT) {
          const text = element.value || ''
          charCount += text.length
          wordCount += text.split(/\s+/).filter(word => word.length > 0).length
        } else if (element.type === ElementType.IMAGE) {
          imageCount++
        } else if (element.type === ElementType.TABLE) {
          tableCount++
        }

        if (element.value === '\n') {
          paragraphCount++
        }
      })
    }

    if (data.main) processElements(data.main)
    if (data.header) processElements(data.header)
    if (data.footer) processElements(data.footer)

    return {
      wordCount,
      charCount,
      paragraphCount,
      imageCount,
      tableCount
    }
  }

  // 更新统计显示
  private updateStatsDisplay(stats: DocumentStats): void {
    const statsPanel = document.querySelector('.document-stats')
    if (statsPanel) {
      statsPanel.innerHTML = `
        <div class="stat-item">字数: ${stats.wordCount}</div>
        <div class="stat-item">字符: ${stats.charCount}</div>
        <div class="stat-item">段落: ${stats.paragraphCount}</div>
        <div class="stat-item">图片: ${stats.imageCount}</div>
        <div class="stat-item">表格: ${stats.tableCount}</div>
      `
    }
  }

  // 清理HTML内容
  private sanitizeHtml(html: string): string {
    // 移除危险标签和属性
    return html
      .replace(/<script[^>]*>.*?<\/script>/gi, '')
      .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/javascript:/gi, '')
  }

  // 检查是否为协作模式
  private isCollaborativeMode(): boolean {
    // 检查是否启用了协作功能
    return window.collaborativeMode === true
  }

  // 发送选区到服务器
  private sendSelectionToServer(payload: ISelectionChangePayload): void {
    // 实现协作选区同步逻辑
    fetch('/api/collaboration/selection', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userId: getCurrentUserId(),
        selection: payload
      })
    }).catch(error => {
      console.error('发送选区信息失败:', error)
    })
  }
}

// 文档统计接口
interface DocumentStats {
  wordCount: number
  charCount: number
  paragraphCount: number
  imageCount: number
  tableCount: number
}

// 辅助函数
function getCurrentUserId(): string {
  return localStorage.getItem('userId') || 'anonymous'
}
```

---

## ⚡ 快捷键系统

### 1. 快捷键注册接口

#### 快捷键配置接口
```typescript
interface IRegisterShortcut {
  key: KeyMap                    // 按键
  ctrl?: boolean                 // Ctrl键
  meta?: boolean                 // Meta键(Mac的Cmd键)
  mod?: boolean                  // 修饰键(Windows: Ctrl, Mac: Cmd)
  shift?: boolean                // Shift键
  alt?: boolean                  // Alt键
  isGlobal?: boolean            // 是否为全局快捷键
  callback?: (command: Command) => any  // 回调函数
  disable?: boolean             // 是否禁用
}

// 按键映射枚举
enum KeyMap {
  A = 'A', B = 'B', C = 'C', D = 'D', E = 'E', F = 'F', G = 'G',
  H = 'H', I = 'I', J = 'J', K = 'K', L = 'L', M = 'M', N = 'N',
  O = 'O', P = 'P', Q = 'Q', R = 'R', S = 'S', T = 'T', U = 'U',
  V = 'V', W = 'W', X = 'X', Y = 'Y', Z = 'Z',
  F1 = 'F1', F2 = 'F2', F3 = 'F3', F4 = 'F4', F5 = 'F5', F6 = 'F6',
  F7 = 'F7', F8 = 'F8', F9 = 'F9', F10 = 'F10', F11 = 'F11', F12 = 'F12',
  ENTER = 'Enter', ESCAPE = 'Escape', SPACE = 'Space',
  ARROW_UP = 'ArrowUp', ARROW_DOWN = 'ArrowDown',
  ARROW_LEFT = 'ArrowLeft', ARROW_RIGHT = 'ArrowRight',
  BACKSPACE = 'Backspace', DELETE = 'Delete',
  HOME = 'Home', END = 'End', PAGE_UP = 'PageUp', PAGE_DOWN = 'PageDown'
}
```

#### 快捷键注册示例
```typescript
// 注册自定义快捷键
editor.register.shortcutList([
  {
    key: KeyMap.S,
    ctrl: true,
    shift: true,
    callback: (command) => {
      // Ctrl+Shift+S 执行另存为
      console.log('执行另存为')
      showSaveAsDialog(command)
    }
  },
  {
    key: KeyMap.F1,
    callback: (command) => {
      // F1 显示帮助
      showHelpDialog()
    }
  },
  {
    key: KeyMap.B,
    mod: true,  // 跨平台修饰键(Windows: Ctrl, Mac: Cmd)
    callback: (command) => {
      command.executeBold()
    }
  },
  {
    key: KeyMap.I,
    mod: true,
    callback: (command) => {
      command.executeItalic()
    }
  },
  {
    key: KeyMap.U,
    mod: true,
    callback: (command) => {
      command.executeUnderline()
    }
  },
  {
    key: KeyMap.K,
    mod: true,
    callback: (command) => {
      // Ctrl/Cmd+K 插入超链接
      showHyperlinkDialog(command)
    }
  },
  {
    key: KeyMap.F,
    mod: true,
    callback: (command) => {
      // Ctrl/Cmd+F 查找
      showSearchDialog(command)
    }
  },
  {
    key: KeyMap.H,
    mod: true,
    callback: (command) => {
      // Ctrl/Cmd+H 查找替换
      showReplaceDialog(command)
    }
  },
  {
    key: KeyMap.G,
    mod: true,
    callback: (command) => {
      // Ctrl/Cmd+G 跳转到指定行
      showGoToLineDialog(command)
    }
  },
  {
    key: KeyMap.D,
    mod: true,
    callback: (command) => {
      // Ctrl/Cmd+D 复制当前行
      command.executeDuplicateLine()
    }
  },
  {
    key: KeyMap.ARROW_UP,
    ctrl: true,
    shift: true,
    callback: (command) => {
      // Ctrl+Shift+↑ 向上移动行
      command.executeMoveLineUp()
    }
  },
  {
    key: KeyMap.ARROW_DOWN,
    ctrl: true,
    shift: true,
    callback: (command) => {
      // Ctrl+Shift+↓ 向下移动行
      command.executeMoveLineDown()
    }
  },
  {
    key: KeyMap.ENTER,
    ctrl: true,
    callback: (command) => {
      // Ctrl+Enter 在下方插入新行
      command.executeInsertLineBelow()
    }
  },
  {
    key: KeyMap.ENTER,
    ctrl: true,
    shift: true,
    callback: (command) => {
      // Ctrl+Shift+Enter 在上方插入新行
      command.executeInsertLineAbove()
    }
  }
])

// 辅助函数实现
function showSaveAsDialog(command: Command): void {
  const dialog = document.createElement('div')
  dialog.className = 'save-as-dialog'
  dialog.innerHTML = `
    <div class="dialog-overlay">
      <div class="dialog-content">
        <h3>另存为</h3>
        <div class="dialog-body">
          <div class="form-group">
            <label>文件名:</label>
            <input type="text" id="fileName" placeholder="请输入文件名">
          </div>
          <div class="form-group">
            <label>格式:</label>
            <select id="fileFormat">
              <option value="json">JSON格式</option>
              <option value="docx">Word文档</option>
              <option value="pdf">PDF文档</option>
              <option value="html">HTML文档</option>
            </select>
          </div>
        </div>
        <div class="dialog-footer">
          <button class="cancel-btn">取消</button>
          <button class="save-btn">保存</button>
        </div>
      </div>
    </div>
  `

  document.body.appendChild(dialog)

  const fileNameInput = dialog.querySelector('#fileName') as HTMLInputElement
  const formatSelect = dialog.querySelector('#fileFormat') as HTMLSelectElement
  const cancelBtn = dialog.querySelector('.cancel-btn') as HTMLElement
  const saveBtn = dialog.querySelector('.save-btn') as HTMLElement

  fileNameInput.focus()

  cancelBtn.onclick = () => {
    document.body.removeChild(dialog)
  }

  saveBtn.onclick = () => {
    const fileName = fileNameInput.value.trim()
    const format = formatSelect.value

    if (!fileName) {
      alert('请输入文件名')
      return
    }

    // 执行保存
    saveDocument(command, fileName, format)
    document.body.removeChild(dialog)
  }
}

function showSearchDialog(command: Command): void {
  const dialog = document.createElement('div')
  dialog.className = 'search-dialog'
  dialog.innerHTML = `
    <div class="search-panel">
      <div class="search-input-group">
        <input type="text" id="searchInput" placeholder="查找内容">
        <button class="search-btn">查找</button>
        <button class="search-all-btn">查找全部</button>
      </div>
      <div class="search-options">
        <label><input type="checkbox" id="caseSensitive"> 区分大小写</label>
        <label><input type="checkbox" id="wholeWord"> 全字匹配</label>
        <label><input type="checkbox" id="useRegex"> 使用正则表达式</label>
      </div>
      <div class="search-results">
        <span class="result-count">0 个结果</span>
        <button class="close-search">×</button>
      </div>
    </div>
  `

  // 定位搜索面板
  dialog.style.cssText = `
    position: fixed;
    top: 60px;
    right: 20px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1000;
  `

  document.body.appendChild(dialog)

  const searchInput = dialog.querySelector('#searchInput') as HTMLInputElement
  const searchBtn = dialog.querySelector('.search-btn') as HTMLElement
  const closeBtn = dialog.querySelector('.close-search') as HTMLElement

  searchInput.focus()

  searchBtn.onclick = () => {
    const searchText = searchInput.value.trim()
    if (searchText) {
      performSearch(command, searchText)
    }
  }

  closeBtn.onclick = () => {
    document.body.removeChild(dialog)
  }

  // 回车搜索
  searchInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
      searchBtn.click()
    }
  })
}

function performSearch(command: Command, searchText: string): void {
  // 实现搜索逻辑
  const data = command.getValue()
  const results = findTextInDocument(data, searchText)

  console.log(`找到 ${results.length} 个匹配项`)

  // 高亮显示搜索结果
  highlightSearchResults(results)

  // 跳转到第一个结果
  if (results.length > 0) {
    command.executeSetRange(results[0].startIndex, results[0].endIndex)
  }
}

function findTextInDocument(data: IEditorData, searchText: string): SearchResult[] {
  const results: SearchResult[] = []
  let currentIndex = 0

  const searchInElements = (elements: IElement[]) => {
    elements.forEach(element => {
      if (element.type === ElementType.TEXT && element.value) {
        const text = element.value
        let index = text.toLowerCase().indexOf(searchText.toLowerCase())

        while (index !== -1) {
          results.push({
            startIndex: currentIndex + index,
            endIndex: currentIndex + index + searchText.length,
            text: text.substring(index, index + searchText.length),
            element
          })

          index = text.toLowerCase().indexOf(searchText.toLowerCase(), index + 1)
        }

        currentIndex += text.length
      } else {
        currentIndex += 1
      }
    })
  }

  if (data.main) searchInElements(data.main)

  return results
}

interface SearchResult {
  startIndex: number
  endIndex: number
  text: string
  element: IElement
}
```

### 2. 内置快捷键系统

#### 富文本快捷键
```typescript
// 内置的富文本快捷键
const richtextKeys: IRegisterShortcut[] = [
  // 文本格式化
  {
    key: KeyMap.B,
    mod: true,
    callback: (command) => command.executeBold()
  },
  {
    key: KeyMap.I,
    mod: true,
    callback: (command) => command.executeItalic()
  },
  {
    key: KeyMap.U,
    mod: true,
    callback: (command) => command.executeUnderline()
  },
  {
    key: KeyMap.S,
    mod: true,
    shift: true,
    callback: (command) => command.executeStrikeout()
  },

  // 段落对齐
  {
    key: KeyMap.L,
    mod: true,
    callback: (command) => command.executeRowFlex(RowFlex.LEFT)
  },
  {
    key: KeyMap.E,
    mod: true,
    callback: (command) => command.executeRowFlex(RowFlex.CENTER)
  },
  {
    key: KeyMap.R,
    mod: true,
    callback: (command) => command.executeRowFlex(RowFlex.RIGHT)
  },
  {
    key: KeyMap.J,
    mod: true,
    callback: (command) => command.executeRowFlex(RowFlex.ALIGNMENT)
  },

  // 列表操作
  {
    key: KeyMap.U,
    mod: true,
    shift: true,
    callback: (command) => command.executeList(ListType.UL, ListStyle.DISC)
  },
  {
    key: KeyMap.O,
    mod: true,
    shift: true,
    callback: (command) => command.executeList(ListType.OL, ListStyle.DECIMAL)
  },

  // 编辑操作
  {
    key: KeyMap.Z,
    mod: true,
    callback: (command) => command.executeUndo()
  },
  {
    key: KeyMap.Y,
    mod: true,
    callback: (command) => command.executeRedo()
  },
  {
    key: KeyMap.Z,
    mod: true,
    shift: true,
    callback: (command) => command.executeRedo()
  },

  // 选择操作
  {
    key: KeyMap.A,
    mod: true,
    callback: (command) => command.executeSelectAll()
  },

  // 剪贴板操作
  {
    key: KeyMap.X,
    mod: true,
    callback: (command) => command.executeCut()
  },
  {
    key: KeyMap.C,
    mod: true,
    callback: (command) => command.executeCopy()
  },
  {
    key: KeyMap.V,
    mod: true,
    callback: (command) => command.executePaste()
  },

  // 查找替换
  {
    key: KeyMap.F,
    mod: true,
    callback: (command) => command.executeSearch()
  },
  {
    key: KeyMap.H,
    mod: true,
    callback: (command) => command.executeReplace()
  },

  // 保存打印
  {
    key: KeyMap.S,
    mod: true,
    callback: (command) => command.executeSave()
  },
  {
    key: KeyMap.P,
    mod: true,
    callback: (command) => command.executePrint()
  }
]
```

#### 表格快捷键
```typescript
// 表格相关快捷键
const tableKeys: IRegisterShortcut[] = [
  {
    key: KeyMap.TAB,
    callback: (command) => {
      // Tab键移动到下一个单元格
      command.executeNextTableCell()
    }
  },
  {
    key: KeyMap.TAB,
    shift: true,
    callback: (command) => {
      // Shift+Tab移动到上一个单元格
      command.executePrevTableCell()
    }
  },
  {
    key: KeyMap.ENTER,
    callback: (command) => {
      // 在表格中Enter键的特殊处理
      if (command.isInTable()) {
        command.executeTableEnter()
      } else {
        command.executeEnter()
      }
    }
  },
  {
    key: KeyMap.ARROW_UP,
    callback: (command) => {
      if (command.isInTable()) {
        command.executeTableArrowUp()
      }
    }
  },
  {
    key: KeyMap.ARROW_DOWN,
    callback: (command) => {
      if (command.isInTable()) {
        command.executeTableArrowDown()
      }
    }
  }
]
```

### 3. 快捷键冲突处理

#### 快捷键管理器
```typescript
export class ShortcutManager {
  private shortcuts: Map<string, IRegisterShortcut>
  private conflictResolver: ConflictResolver

  constructor() {
    this.shortcuts = new Map()
    this.conflictResolver = new ConflictResolver()
  }

  // 注册快捷键
  public register(shortcut: IRegisterShortcut): boolean {
    const key = this.generateShortcutKey(shortcut)

    // 检查冲突
    if (this.shortcuts.has(key)) {
      const existingShortcut = this.shortcuts.get(key)!
      const resolution = this.conflictResolver.resolve(existingShortcut, shortcut)

      switch (resolution) {
        case ConflictResolution.REPLACE:
          console.warn(`快捷键冲突: ${key}，已替换为新的快捷键`)
          break
        case ConflictResolution.IGNORE:
          console.warn(`快捷键冲突: ${key}，忽略新的快捷键`)
          return false
        case ConflictResolution.MERGE:
          // 合并快捷键处理逻辑
          shortcut = this.mergeShortcuts(existingShortcut, shortcut)
          break
      }
    }

    this.shortcuts.set(key, shortcut)
    return true
  }

  // 生成快捷键标识
  private generateShortcutKey(shortcut: IRegisterShortcut): string {
    const parts = [shortcut.key]

    if (shortcut.ctrl) parts.push('ctrl')
    if (shortcut.meta) parts.push('meta')
    if (shortcut.mod) parts.push('mod')
    if (shortcut.shift) parts.push('shift')
    if (shortcut.alt) parts.push('alt')

    return parts.sort().join('+')
  }

  // 合并快捷键
  private mergeShortcuts(
    existing: IRegisterShortcut,
    newShortcut: IRegisterShortcut
  ): IRegisterShortcut {
    return {
      ...existing,
      callback: (command) => {
        // 先执行原有回调
        if (existing.callback) {
          existing.callback(command)
        }
        // 再执行新回调
        if (newShortcut.callback) {
          newShortcut.callback(command)
        }
      }
    }
  }

  // 移除快捷键
  public unregister(shortcut: IRegisterShortcut): boolean {
    const key = this.generateShortcutKey(shortcut)
    return this.shortcuts.delete(key)
  }

  // 获取所有快捷键
  public getAll(): Map<string, IRegisterShortcut> {
    return new Map(this.shortcuts)
  }

  // 检查快捷键是否存在
  public exists(shortcut: IRegisterShortcut): boolean {
    const key = this.generateShortcutKey(shortcut)
    return this.shortcuts.has(key)
  }
}

// 冲突解决器
class ConflictResolver {
  public resolve(
    existing: IRegisterShortcut,
    newShortcut: IRegisterShortcut
  ): ConflictResolution {
    // 如果新快捷键有更高优先级，则替换
    if (this.getPriority(newShortcut) > this.getPriority(existing)) {
      return ConflictResolution.REPLACE
    }

    // 如果是全局快捷键冲突，则忽略
    if (existing.isGlobal && newShortcut.isGlobal) {
      return ConflictResolution.IGNORE
    }

    // 默认合并
    return ConflictResolution.MERGE
  }

  private getPriority(shortcut: IRegisterShortcut): number {
    let priority = 0

    // 全局快捷键优先级更高
    if (shortcut.isGlobal) priority += 10

    // 系统快捷键优先级更高
    if (this.isSystemShortcut(shortcut)) priority += 5

    return priority
  }

  private isSystemShortcut(shortcut: IRegisterShortcut): boolean {
    // 判断是否为系统内置快捷键
    const systemKeys = ['S', 'C', 'V', 'X', 'Z', 'Y', 'A', 'F', 'P']
    return systemKeys.includes(shortcut.key) && (shortcut.mod || shortcut.ctrl)
  }
}

enum ConflictResolution {
  REPLACE = 'replace',
  IGNORE = 'ignore',
  MERGE = 'merge'
}
```

---

## 🔧 高级扩展功能

### 1. 水印系统扩展

#### 动态水印实现
```typescript
export class DynamicWatermark {
  private draw: Draw
  private options: IEditorOption
  private watermarkData: WatermarkData

  constructor(draw: Draw) {
    this.draw = draw
    this.options = draw.getOptions()
    this.watermarkData = this.initializeWatermarkData()
  }

  private initializeWatermarkData(): WatermarkData {
    return {
      text: this.options.watermark?.data || '',
      timestamp: new Date().toLocaleString(),
      userInfo: this.getCurrentUserInfo(),
      documentInfo: this.getDocumentInfo()
    }
  }

  // 渲染动态水印
  public render(ctx: CanvasRenderingContext2D, pageNo: number): void {
    const watermarkConfig = this.options.watermark
    if (!watermarkConfig || !watermarkConfig.data) return

    const { opacity, font, size, color, repeat, gap } = watermarkConfig
    const width = this.draw.getWidth()
    const height = this.draw.getHeight()

    ctx.save()
    ctx.globalAlpha = opacity
    ctx.font = `${size * this.options.scale}px ${font}`
    ctx.fillStyle = color

    // 生成动态水印文本
    const watermarkText = this.generateDynamicText(watermarkConfig.data, pageNo)

    if (repeat) {
      this.renderRepeatedWatermark(ctx, watermarkText, width, height, gap)
    } else {
      this.renderSingleWatermark(ctx, watermarkText, width, height)
    }

    ctx.restore()
  }

  // 生成动态文本
  private generateDynamicText(template: string, pageNo: number): string {
    return template
      .replace(/{pageNo}/g, (pageNo + 1).toString())
      .replace(/{pageCount}/g, this.draw.getPageCount().toString())
      .replace(/{timestamp}/g, this.watermarkData.timestamp)
      .replace(/{user}/g, this.watermarkData.userInfo.name)
      .replace(/{department}/g, this.watermarkData.userInfo.department)
      .replace(/{documentTitle}/g, this.watermarkData.documentInfo.title)
      .replace(/{documentId}/g, this.watermarkData.documentInfo.id)
  }

  // 渲染重复水印
  private renderRepeatedWatermark(
    ctx: CanvasRenderingContext2D,
    text: string,
    width: number,
    height: number,
    gap: [number, number] = [100, 100]
  ): void {
    const [gapX, gapY] = gap
    const textMetrics = ctx.measureText(text)
    const textWidth = textMetrics.width
    const textHeight = parseInt(ctx.font)

    // 计算旋转角度
    const angle = -Math.PI / 6 // -30度

    for (let x = 0; x < width + textWidth; x += gapX) {
      for (let y = 0; y < height + textHeight; y += gapY) {
        ctx.save()
        ctx.translate(x, y)
        ctx.rotate(angle)
        ctx.fillText(text, 0, 0)
        ctx.restore()
      }
    }
  }

  // 渲染单个水印
  private renderSingleWatermark(
    ctx: CanvasRenderingContext2D,
    text: string,
    width: number,
    height: number
  ): void {
    const textMetrics = ctx.measureText(text)
    const x = (width - textMetrics.width) / 2
    const y = height / 2

    ctx.save()
    ctx.translate(x + textMetrics.width / 2, y)
    ctx.rotate(-Math.PI / 6)
    ctx.fillText(text, -textMetrics.width / 2, 0)
    ctx.restore()
  }

  // 获取当前用户信息
  private getCurrentUserInfo(): UserInfo {
    return {
      name: localStorage.getItem('userName') || '未知用户',
      department: localStorage.getItem('userDepartment') || '未知部门',
      id: localStorage.getItem('userId') || 'anonymous'
    }
  }

  // 获取文档信息
  private getDocumentInfo(): DocumentInfo {
    return {
      title: document.title || '未命名文档',
      id: this.generateDocumentId(),
      createTime: new Date().toISOString(),
      version: '1.0'
    }
  }

  // 生成文档ID
  private generateDocumentId(): string {
    return 'DOC_' + Date.now().toString(36).toUpperCase()
  }

  // 更新水印数据
  public updateWatermarkData(data: Partial<WatermarkData>): void {
    this.watermarkData = { ...this.watermarkData, ...data }
  }
}

// 水印数据接口
interface WatermarkData {
  text: string
  timestamp: string
  userInfo: UserInfo
  documentInfo: DocumentInfo
}

interface UserInfo {
  name: string
  department: string
  id: string
}

interface DocumentInfo {
  title: string
  id: string
  createTime: string
  version: string
}
```

### 2. 页眉页脚扩展

#### 动态页眉页脚管理器
```typescript
export class HeaderFooterManager {
  private draw: Draw
  private command: Command

  constructor(draw: Draw, command: Command) {
    this.draw = draw
    this.command = command
  }

  // 设置动态页眉
  public setDynamicHeader(template: string): void {
    const headerElements = this.generateDynamicElements(template)
    this.command.executeSetHeader(headerElements)
  }

  // 设置动态页脚
  public setDynamicFooter(template: string): void {
    const footerElements = this.generateDynamicElements(template)
    this.command.executeSetFooter(footerElements)
  }

  // 生成动态元素
  private generateDynamicElements(template: string): IElement[] {
    const processedText = this.processTemplate(template)

    return [
      {
        value: processedText,
        size: 12,
        color: '#666666',
        rowFlex: RowFlex.CENTER
      }
    ]
  }

  // 处理模板
  private processTemplate(template: string): string {
    const now = new Date()

    return template
      .replace(/{date}/g, now.toLocaleDateString())
      .replace(/{time}/g, now.toLocaleTimeString())
      .replace(/{year}/g, now.getFullYear().toString())
      .replace(/{month}/g, (now.getMonth() + 1).toString())
      .replace(/{day}/g, now.getDate().toString())
      .replace(/{company}/g, this.getCompanyName())
      .replace(/{author}/g, this.getAuthorName())
      .replace(/{title}/g, this.getDocumentTitle())
  }

  private getCompanyName(): string {
    return localStorage.getItem('companyName') || '公司名称'
  }

  private getAuthorName(): string {
    return localStorage.getItem('authorName') || '作者'
  }

  private getDocumentTitle(): string {
    return document.title || '文档标题'
  }
}
```

### 3. 搜索替换功能扩展

#### 高级搜索替换器
```typescript
export class AdvancedSearchReplace {
  private command: Command
  private searchHistory: string[]
  private replaceHistory: string[]

  constructor(command: Command) {
    this.command = command
    this.searchHistory = this.loadSearchHistory()
    this.replaceHistory = this.loadReplaceHistory()
  }

  // 高级搜索
  public search(options: SearchOptions): SearchResult[] {
    const { text, caseSensitive, wholeWord, useRegex, scope } = options

    // 保存搜索历史
    this.addToSearchHistory(text)

    const data = this.command.getValue()
    let searchPattern: RegExp

    if (useRegex) {
      try {
        searchPattern = new RegExp(text, caseSensitive ? 'g' : 'gi')
      } catch (error) {
        throw new Error('无效的正则表达式')
      }
    } else {
      const escapedText = this.escapeRegExp(text)
      const pattern = wholeWord ? `\\b${escapedText}\\b` : escapedText
      searchPattern = new RegExp(pattern, caseSensitive ? 'g' : 'gi')
    }

    return this.findMatches(data, searchPattern, scope)
  }

  // 替换功能
  public replace(options: ReplaceOptions): ReplaceResult {
    const { searchText, replaceText, replaceAll, caseSensitive, wholeWord, useRegex } = options

    // 保存替换历史
    this.addToReplaceHistory(replaceText)

    const searchResults = this.search({
      text: searchText,
      caseSensitive,
      wholeWord,
      useRegex,
      scope: 'document'
    })

    if (searchResults.length === 0) {
      return { replacedCount: 0, results: [] }
    }

    let replacedCount = 0
    const results: ReplaceResultItem[] = []

    if (replaceAll) {
      // 替换所有
      searchResults.reverse().forEach(result => {
        this.performReplace(result, replaceText)
        replacedCount++
        results.push({
          originalText: result.text,
          newText: replaceText,
          position: result.startIndex
        })
      })
    } else {
      // 替换当前选中的
      const currentSelection = this.command.getRange()
      const currentResult = searchResults.find(result =>
        result.startIndex >= currentSelection.startIndex &&
        result.endIndex <= currentSelection.endIndex
      )

      if (currentResult) {
        this.performReplace(currentResult, replaceText)
        replacedCount = 1
        results.push({
          originalText: currentResult.text,
          newText: replaceText,
          position: currentResult.startIndex
        })
      }
    }

    return { replacedCount, results }
  }

  // 执行替换
  private performReplace(result: SearchResult, replaceText: string): void {
    this.command.executeSetRange(result.startIndex, result.endIndex)
    this.command.executeInsertElementList([{
      value: replaceText
    }])
  }

  // 查找匹配项
  private findMatches(data: IEditorData, pattern: RegExp, scope: string): SearchResult[] {
    const results: SearchResult[] = []
    let currentIndex = 0

    const searchInElements = (elements: IElement[]) => {
      elements.forEach(element => {
        if (element.type === ElementType.TEXT && element.value) {
          const text = element.value
          let match

          while ((match = pattern.exec(text)) !== null) {
            results.push({
              startIndex: currentIndex + match.index,
              endIndex: currentIndex + match.index + match[0].length,
              text: match[0],
              element,
              context: this.getContext(text, match.index, match[0].length)
            })

            // 防止无限循环
            if (!pattern.global) break
          }

          currentIndex += text.length
        } else {
          currentIndex += 1
        }
      })
    }

    switch (scope) {
      case 'document':
        if (data.main) searchInElements(data.main)
        if (data.header) searchInElements(data.header)
        if (data.footer) searchInElements(data.footer)
        break
      case 'main':
        if (data.main) searchInElements(data.main)
        break
      case 'header':
        if (data.header) searchInElements(data.header)
        break
      case 'footer':
        if (data.footer) searchInElements(data.footer)
        break
    }

    return results
  }

  // 获取上下文
  private getContext(text: string, index: number, length: number): string {
    const contextLength = 20
    const start = Math.max(0, index - contextLength)
    const end = Math.min(text.length, index + length + contextLength)

    return text.substring(start, end)
  }

  // 转义正则表达式特殊字符
  private escapeRegExp(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  // 搜索历史管理
  private addToSearchHistory(text: string): void {
    const index = this.searchHistory.indexOf(text)
    if (index > -1) {
      this.searchHistory.splice(index, 1)
    }
    this.searchHistory.unshift(text)

    // 限制历史记录数量
    if (this.searchHistory.length > 20) {
      this.searchHistory = this.searchHistory.slice(0, 20)
    }

    this.saveSearchHistory()
  }

  private addToReplaceHistory(text: string): void {
    const index = this.replaceHistory.indexOf(text)
    if (index > -1) {
      this.replaceHistory.splice(index, 1)
    }
    this.replaceHistory.unshift(text)

    if (this.replaceHistory.length > 20) {
      this.replaceHistory = this.replaceHistory.slice(0, 20)
    }

    this.saveReplaceHistory()
  }

  private loadSearchHistory(): string[] {
    const history = localStorage.getItem('canvas-editor-search-history')
    return history ? JSON.parse(history) : []
  }

  private loadReplaceHistory(): string[] {
    const history = localStorage.getItem('canvas-editor-replace-history')
    return history ? JSON.parse(history) : []
  }

  private saveSearchHistory(): void {
    localStorage.setItem('canvas-editor-search-history', JSON.stringify(this.searchHistory))
  }

  private saveReplaceHistory(): void {
    localStorage.setItem('canvas-editor-replace-history', JSON.stringify(this.replaceHistory))
  }

  // 获取历史记录
  public getSearchHistory(): string[] {
    return [...this.searchHistory]
  }

  public getReplaceHistory(): string[] {
    return [...this.replaceHistory]
  }
}

// 搜索选项接口
interface SearchOptions {
  text: string
  caseSensitive?: boolean
  wholeWord?: boolean
  useRegex?: boolean
  scope?: 'document' | 'main' | 'header' | 'footer'
}

// 替换选项接口
interface ReplaceOptions extends SearchOptions {
  replaceText: string
  replaceAll?: boolean
}

// 搜索结果接口
interface SearchResult {
  startIndex: number
  endIndex: number
  text: string
  element: IElement
  context?: string
}

// 替换结果接口
interface ReplaceResult {
  replacedCount: number
  results: ReplaceResultItem[]
}

interface ReplaceResultItem {
  originalText: string
  newText: string
  position: number
}
```

---

## 🎨 主题和样式定制

### 1. 主题系统

#### 主题管理器
```typescript
export class ThemeManager {
  private currentTheme: Theme
  private themes: Map<string, Theme>
  private editor: Editor

  constructor(editor: Editor) {
    this.editor = editor
    this.themes = new Map()
    this.initializeDefaultThemes()
    this.currentTheme = this.themes.get('default')!
  }

  // 初始化默认主题
  private initializeDefaultThemes(): void {
    // 默认主题
    this.themes.set('default', {
      name: 'default',
      displayName: '默认主题',
      colors: {
        background: '#ffffff',
        text: '#000000',
        selection: '#007ACC',
        cursor: '#000000',
        border: '#DCDFE6',
        toolbar: '#F5F7FA',
        button: '#FFFFFF',
        buttonHover: '#ECF5FF',
        buttonActive: '#409EFF'
      },
      fonts: {
        default: 'Microsoft YaHei',
        monospace: 'Consolas'
      },
      sizes: {
        default: 14,
        toolbar: 12,
        status: 11
      }
    })

    // 暗色主题
    this.themes.set('dark', {
      name: 'dark',
      displayName: '暗色主题',
      colors: {
        background: '#1e1e1e',
        text: '#d4d4d4',
        selection: '#264f78',
        cursor: '#ffffff',
        border: '#3e3e3e',
        toolbar: '#2d2d30',
        button: '#3e3e3e',
        buttonHover: '#464647',
        buttonActive: '#007ACC'
      },
      fonts: {
        default: 'Microsoft YaHei',
        monospace: 'Consolas'
      },
      sizes: {
        default: 14,
        toolbar: 12,
        status: 11
      }
    })

    // 护眼主题
    this.themes.set('eye-care', {
      name: 'eye-care',
      displayName: '护眼主题',
      colors: {
        background: '#f7f3e9',
        text: '#5c4b37',
        selection: '#c9b037',
        cursor: '#5c4b37',
        border: '#d4c5a9',
        toolbar: '#f0ead6',
        button: '#faf8f1',
        buttonHover: '#f5f1e8',
        buttonActive: '#e6d690'
      },
      fonts: {
        default: 'Microsoft YaHei',
        monospace: 'Consolas'
      },
      sizes: {
        default: 14,
        toolbar: 12,
        status: 11
      }
    })
  }

  // 应用主题
  public applyTheme(themeName: string): void {
    const theme = this.themes.get(themeName)
    if (!theme) {
      console.error(`主题 ${themeName} 不存在`)
      return
    }

    this.currentTheme = theme
    this.updateEditorStyles(theme)
    this.updateUIStyles(theme)

    // 保存主题设置
    localStorage.setItem('canvas-editor-theme', themeName)

    // 触发主题变化事件
    this.editor.eventBus.emit('customEvent', {
      type: 'themeChange',
      theme: theme.name
    })
  }

  // 更新编辑器样式
  private updateEditorStyles(theme: Theme): void {
    const container = this.editor.command.getContainer()

    // 更新容器样式
    container.style.backgroundColor = theme.colors.background
    container.style.color = theme.colors.text

    // 更新画布样式
    const canvases = container.querySelectorAll('canvas')
    canvases.forEach(canvas => {
      canvas.style.backgroundColor = theme.colors.background
    })

    // 更新编辑器选项
    const options = this.editor.command.getOptions()
    options.defaultFont = theme.fonts.default
    options.defaultSize = theme.sizes.default
  }

  // 更新UI样式
  private updateUIStyles(theme: Theme): void {
    // 创建或更新主题样式表
    let styleSheet = document.getElementById('canvas-editor-theme-styles') as HTMLStyleElement
    if (!styleSheet) {
      styleSheet = document.createElement('style')
      styleSheet.id = 'canvas-editor-theme-styles'
      document.head.appendChild(styleSheet)
    }

    styleSheet.textContent = this.generateThemeCSS(theme)
  }

  // 生成主题CSS
  private generateThemeCSS(theme: Theme): string {
    return `
      .canvas-editor {
        background-color: ${theme.colors.background};
        color: ${theme.colors.text};
      }

      .canvas-editor .toolbar {
        background-color: ${theme.colors.toolbar};
        border-color: ${theme.colors.border};
      }

      .canvas-editor .toolbar-button {
        background-color: ${theme.colors.button};
        color: ${theme.colors.text};
        border-color: ${theme.colors.border};
      }

      .canvas-editor .toolbar-button:hover {
        background-color: ${theme.colors.buttonHover};
      }

      .canvas-editor .toolbar-button.active {
        background-color: ${theme.colors.buttonActive};
        color: #ffffff;
      }

      .canvas-editor .context-menu {
        background-color: ${theme.colors.background};
        border-color: ${theme.colors.border};
        color: ${theme.colors.text};
      }

      .canvas-editor .context-menu-item:hover {
        background-color: ${theme.colors.buttonHover};
      }

      .canvas-editor .dialog {
        background-color: ${theme.colors.background};
        color: ${theme.colors.text};
        border-color: ${theme.colors.border};
      }

      .canvas-editor .status-bar {
        background-color: ${theme.colors.toolbar};
        border-color: ${theme.colors.border};
        color: ${theme.colors.text};
        font-size: ${theme.sizes.status}px;
      }

      .canvas-editor .search-panel {
        background-color: ${theme.colors.background};
        border-color: ${theme.colors.border};
        color: ${theme.colors.text};
      }

      .canvas-editor input, .canvas-editor select, .canvas-editor textarea {
        background-color: ${theme.colors.button};
        color: ${theme.colors.text};
        border-color: ${theme.colors.border};
      }

      .canvas-editor input:focus, .canvas-editor select:focus, .canvas-editor textarea:focus {
        border-color: ${theme.colors.selection};
        box-shadow: 0 0 0 2px ${theme.colors.selection}33;
      }
    `
  }

  // 注册自定义主题
  public registerTheme(theme: Theme): void {
    this.themes.set(theme.name, theme)
  }

  // 获取当前主题
  public getCurrentTheme(): Theme {
    return this.currentTheme
  }

  // 获取所有主题
  public getAllThemes(): Theme[] {
    return Array.from(this.themes.values())
  }

  // 从本地存储加载主题
  public loadSavedTheme(): void {
    const savedTheme = localStorage.getItem('canvas-editor-theme')
    if (savedTheme && this.themes.has(savedTheme)) {
      this.applyTheme(savedTheme)
    }
  }
}

// 主题接口
interface Theme {
  name: string
  displayName: string
  colors: ThemeColors
  fonts: ThemeFonts
  sizes: ThemeSizes
}

interface ThemeColors {
  background: string
  text: string
  selection: string
  cursor: string
  border: string
  toolbar: string
  button: string
  buttonHover: string
  buttonActive: string
}

interface ThemeFonts {
  default: string
  monospace: string
}

interface ThemeSizes {
  default: number
  toolbar: number
  status: number
}
```

---

## 📋 总结

Canvas Editor 提供了强大而灵活的扩展和自定义功能，主要包括：

### 🎯 核心扩展能力

1. **菜单系统扩展**
   - 自定义右键菜单，支持条件显示、子菜单、图标等
   - 内置丰富的菜单类型：全局菜单、表格菜单、图片菜单、控件菜单、超链接菜单
   - 支持国际化和动态文本替换

2. **控件系统扩展**
   - 内置多种控件：复选框、文本输入、下拉选择、日期选择等
   - 支持自定义控件开发，包括富文本控件、文件上传控件、签名控件等
   - 完整的控件事件处理和渲染机制

3. **插件开发系统**
   - 标准化的插件接口和生命周期管理
   - 丰富的插件示例：自定义工具栏、自动保存、协作编辑等
   - 插件冲突检测和解决机制

### 🛠️ 工具栏和界面定制

4. **工具栏定制**
   - 完整的工具栏按钮实现：格式化、段落、插入、工具等分组
   - 自定义按钮开发和状态管理
   - 工具栏管理器支持动态添加/移除按钮

5. **事件系统扩展**
   - 强大的事件监听器系统，支持内容变化、样式变化、页面变化等
   - EventBus 事件总线，支持自定义事件和事件历史
   - 完整的事件处理器开发示例

### ⚡ 高级功能扩展

6. **快捷键系统**
   - 完整的快捷键注册和管理机制
   - 内置丰富的快捷键：文本格式化、段落操作、编辑操作等
   - 快捷键冲突检测和解决

7. **高级扩展功能**
   - 动态水印系统，支持用户信息、时间戳、文档信息等
   - 动态页眉页脚管理
   - 高级搜索替换功能，支持正则表达式、历史记录等

8. **主题和样式定制**
   - 完整的主题管理系统
   - 内置多种主题：默认、暗色、护眼等
   - 支持自定义主题开发和动态切换

### 🔧 开发建议

1. **扩展开发流程**
   - 先了解核心架构和API接口
   - 使用 codebase-retrieval 工具获取详细的实现信息
   - 参考现有的扩展示例进行开发
   - 注意事件监听和资源清理

2. **最佳实践**
   - 使用插件系统进行功能扩展
   - 遵循事件驱动的开发模式
   - 注意性能优化和内存管理
   - 提供完整的错误处理和用户反馈

3. **调试和测试**
   - 使用浏览器开发者工具进行调试
   - 编写单元测试验证功能正确性
   - 测试不同浏览器和设备的兼容性

Canvas Editor 的扩展系统设计完善，文档详细，为开发者提供了强大的定制能力，可以满足各种复杂的业务需求。
```
```
```
```
```
```
```
