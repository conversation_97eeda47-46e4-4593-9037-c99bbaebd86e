import './DocumentOperationGroup.css'
import { UndoButton } from './UndoButton'
import { RedoButton } from './RedoButton'
import { PainterButton } from './PainterButton'
import { FormatButton } from './FormatButton'

export class DocumentOperationGroup {
  private container: HTMLElement;
  private undoButton: UndoButton;
  private redoButton: RedoButton;
  private painterButton: PainterButton;
  private formatButton: FormatButton;

  constructor(container: HTMLElement, command: any) {
    this.container = container
    
    // 设置容器内容
    this.container.innerHTML = this.render()
    
    // 创建按钮实例
    const undoContainer = this.container.querySelector('.undo-button-container') as HTMLElement
    this.undoButton = new UndoButton(undoContainer, command)
    
    const redoContainer = this.container.querySelector('.redo-button-container') as HTMLElement
    this.redoButton = new RedoButton(redoContainer, command)
    
    const painterContainer = this.container.querySelector('.painter-button-container') as HTMLElement
    this.painterButton = new PainterButton(painterContainer, command)
    
    const formatContainer = this.container.querySelector('.format-button-container') as HTMLElement
    this.formatButton = new FormatButton(formatContainer, command)
  }

  private render(): string {
    return `<div class="document-operation-group">
      <div class="undo-button-container"></div>
      <div class="redo-button-container"></div>
      <div class="painter-button-container"></div>
      <div class="format-button-container"></div>
    </div>`
  }

  // 更新按钮状态
  public updateState(payload: any): void {
    this.undoButton.updateState(payload.undo)
    this.redoButton.updateState(payload.redo)
    this.painterButton.updateState(payload.painter)
  }
} 