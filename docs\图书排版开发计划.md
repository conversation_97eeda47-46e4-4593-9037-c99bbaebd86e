# Canvas Editor 图书排版功能开发计划

## 📋 项目概述

### 开发目标
基于Canvas Editor右侧工具栏的"图书编排"→"排版"tab，开发专业的图书内容排版功能模块，提供图书出版级别的排版工具和样式管理能力。

### 核心原则
1. **避免重复开发**: 不重复主菜单Ribbon工具栏中已有的基础编辑功能
2. **专业化定位**: 专注于图书出版的专业排版需求
3. **样式驱动**: 基于样式配置文件的排版方案
4. **模板化**: 提供预设的图书排版模板
5. **批量操作**: 支持全文档的批量样式应用

## 🎯 功能范围界定

### ✅ 主菜单已有功能（不重复开发）
- **基础文本格式**: 字体、字号、颜色、加粗、斜体、下划线等
- **段落格式**: 对齐方式、行间距、标题级别
- **插入功能**: 表格、图片、公式、代码块
- **页面设置**: 页边距、纸张大小、方向、水印
- **编辑操作**: 撤销重做、复制粘贴、搜索替换

### 🚀 图书排版专有功能（需要开发）
- **版式设计**: 图书版式模板、章节结构
- **样式管理**: 样式集管理、样式导入导出
- **排版规范**: 图书排版标准、版式检查
- **内容组织**: 目录生成、索引管理、脚注尾注
- **版面控制**: 分栏排版、文字环绕、版面平衡
- **出版工具**: 页码设置、版权页、封面设计

## 🏗️ 技术架构设计

### 1. 模块结构
```
fontend/src/components/rightTools/bookLayout/
├── BookLayoutManager.ts          # 图书排版主管理器
├── templates/                    # 排版模板
│   ├── TemplateManager.ts       # 模板管理器
│   ├── BookTemplate.ts          # 图书模板基类
│   ├── NovelTemplate.ts         # 小说模板
│   ├── TextbookTemplate.ts      # 教材模板
│   └── MagazineTemplate.ts      # 杂志模板
├── styles/                       # 样式管理
│   ├── StyleSetManager.ts       # 样式集管理器
│   ├── BookStyleSet.ts          # 图书样式集
│   └── StyleValidator.ts        # 样式验证器
├── layout/                       # 版面布局
│   ├── ColumnLayout.ts          # 分栏布局
│   ├── PageLayout.ts            # 页面布局
│   └── ChapterLayout.ts         # 章节布局
├── content/                      # 内容组织
│   ├── TOCGenerator.ts          # 目录生成器
│   ├── IndexManager.ts          # 索引管理
│   ├── FootnoteManager.ts       # 脚注管理
│   └── ChapterManager.ts        # 章节管理
├── publishing/                   # 出版工具
│   ├── PageNumbering.ts         # 页码管理
│   ├── CopyrightPage.ts         # 版权页
│   └── CoverDesign.ts           # 封面设计
└── ui/                          # 用户界面
    ├── BookLayoutPanel.ts       # 排版面板
    ├── TemplateSelector.ts      # 模板选择器
    ├── StyleEditor.ts           # 样式编辑器
    └── LayoutPreview.ts         # 布局预览
```

### 2. 数据模型
```typescript
// 图书排版配置接口
interface IBookLayoutConfig {
  templateId: string              // 模板ID
  styleSetId: string             // 样式集ID
  layoutSettings: ILayoutSettings // 布局设置
  contentStructure: IContentStructure // 内容结构
  publishingOptions: IPublishingOptions // 出版选项
}

// 图书模板接口
interface IBookTemplate {
  id: string                     // 模板ID
  name: string                   // 模板名称
  category: 'novel' | 'textbook' | 'magazine' | 'academic' // 模板类别
  description: string            // 模板描述
  defaultStyles: IStyleSet       // 默认样式集
  layoutRules: ILayoutRule[]     // 布局规则
  contentStructure: IContentStructure // 内容结构模板
}

// 样式集接口
interface IStyleSet {
  id: string                     // 样式集ID
  name: string                   // 样式集名称
  version: string                // 版本号
  styles: {
    chapter: ITextStyle          // 章标题样式
    section: ITextStyle          // 节标题样式
    subsection: ITextStyle       // 小节标题样式
    body: ITextStyle             // 正文样式
    quote: ITextStyle            // 引用样式
    footnote: ITextStyle         // 脚注样式
    caption: ITextStyle          // 图表标题样式
  }
}
```

## 📚 核心功能模块

### 1. 图书模板系统

#### 1.1 模板管理器 (TemplateManager)
```typescript
/**
 * 图书模板管理器
 * 负责模板的加载、应用、保存和管理
 */
class TemplateManager {
  // 加载预设模板
  loadPresetTemplates(): Promise<IBookTemplate[]>
  
  // 应用模板到文档
  applyTemplate(templateId: string): Promise<void>
  
  // 创建自定义模板
  createCustomTemplate(config: IBookTemplate): Promise<string>
  
  // 导出模板
  exportTemplate(templateId: string): Promise<Blob>
  
  // 导入模板
  importTemplate(file: File): Promise<string>
}
```

#### 1.2 预设模板类型
- **小说模板**: 适用于小说、散文等文学作品
- **教材模板**: 适用于教科书、参考书
- **学术模板**: 适用于学术论文、研究报告
- **杂志模板**: 适用于期刊、杂志排版
- **儿童读物模板**: 适用于儿童图书

### 2. 样式集管理系统

#### 2.1 样式集管理器 (StyleSetManager)
```typescript
/**
 * 样式集管理器
 * 管理图书排版的样式集合
 */
class StyleSetManager {
  // 加载样式集
  loadStyleSet(styleSetId: string): Promise<IStyleSet>
  
  // 应用样式集
  applyStyleSet(styleSetId: string): Promise<void>
  
  // 创建样式集
  createStyleSet(config: IStyleSet): Promise<string>
  
  // 编辑样式集
  editStyleSet(styleSetId: string, changes: Partial<IStyleSet>): Promise<void>
  
  // 导出样式集
  exportStyleSet(styleSetId: string): Promise<Blob>
  
  // 导入样式集
  importStyleSet(file: File): Promise<string>
  
  // 样式集预览
  previewStyleSet(styleSetId: string): Promise<string>
}
```

#### 2.2 增强现有样式下载功能
基于现有的`FontStyleConfigManager`，扩展为完整的样式集管理：

```typescript
/**
 * 扩展现有的字体样式配置管理器
 * 增加图书排版专用的样式管理功能
 */
class EnhancedStyleConfigManager extends FontStyleConfigManager {
  // 批量应用样式到选定内容
  applyStylesToSelection(styleType: string): Promise<void>
  
  // 批量应用样式到全文档
  applyStylesToDocument(styleSetId: string): Promise<void>
  
  // 样式冲突检测
  detectStyleConflicts(): Promise<IStyleConflict[]>
  
  // 样式优化建议
  getStyleOptimizationSuggestions(): Promise<IStyleSuggestion[]>
  
  // 样式使用统计
  getStyleUsageStatistics(): Promise<IStyleUsage[]>
}
```

### 3. 版面布局系统

#### 3.1 分栏布局管理器 (ColumnLayout)
```typescript
/**
 * 分栏布局管理器
 * 处理多栏排版和文字流动
 */
class ColumnLayout {
  // 设置分栏数量
  setColumnCount(count: number): void
  
  // 设置栏间距
  setColumnGap(gap: number): void
  
  // 设置栏宽度
  setColumnWidth(width: number[]): void
  
  // 插入分栏符
  insertColumnBreak(): void
  
  // 平衡栏高度
  balanceColumns(): void
}
```

#### 3.2 章节布局管理器 (ChapterLayout)
```typescript
/**
 * 章节布局管理器
 * 处理章节结构和页面布局
 */
class ChapterLayout {
  // 插入章节分页
  insertChapterBreak(): void
  
  // 设置章节起始页
  setChapterStartPage(pageType: 'odd' | 'even' | 'any'): void
  
  // 章节标题格式化
  formatChapterTitle(level: number, style: ITextStyle): void
  
  // 章节编号管理
  manageChapterNumbering(format: string): void
}
```

### 4. 内容组织系统

#### 4.1 目录生成器 (TOCGenerator)
```typescript
/**
 * 目录生成器
 * 自动生成和管理文档目录
 */
class TOCGenerator {
  // 生成目录
  generateTOC(levels: number[]): Promise<void>
  
  // 更新目录
  updateTOC(): Promise<void>
  
  // 设置目录样式
  setTOCStyle(style: ITOCStyle): void
  
  // 插入目录到指定位置
  insertTOCAtPosition(position: number): void
  
  // 目录页码更新
  updateTOCPageNumbers(): void
}
```

#### 4.2 脚注管理器 (FootnoteManager)
```typescript
/**
 * 脚注管理器
 * 处理脚注和尾注的插入和管理
 */
class FootnoteManager {
  // 插入脚注
  insertFootnote(text: string): void
  
  // 插入尾注
  insertEndnote(text: string): void
  
  // 脚注编号格式
  setFootnoteNumberFormat(format: string): void
  
  // 脚注样式设置
  setFootnoteStyle(style: ITextStyle): void
  
  // 脚注位置调整
  adjustFootnotePosition(): void
}
```

### 5. 出版工具系统

#### 5.1 页码管理器 (PageNumbering)
```typescript
/**
 * 页码管理器
 * 处理复杂的页码编号和格式
 */
class PageNumbering {
  // 设置页码格式
  setPageNumberFormat(format: string): void
  
  // 设置页码起始值
  setPageNumberStart(start: number): void
  
  // 设置页码位置
  setPageNumberPosition(position: 'header' | 'footer', align: string): void
  
  // 章节页码重新编号
  restartPageNumbering(): void
  
  // 奇偶页不同页码
  setDifferentOddEvenPages(enabled: boolean): void
}
```

#### 5.2 版权页生成器 (CopyrightPage)
```typescript
/**
 * 版权页生成器
 * 生成标准的图书版权页
 */
class CopyrightPage {
  // 生成版权页
  generateCopyrightPage(info: ICopyrightInfo): void
  
  // 设置版权页模板
  setCopyrightTemplate(template: ICopyrightTemplate): void
  
  // 插入ISBN信息
  insertISBNInfo(isbn: string): void
  
  // 插入出版信息
  insertPublishingInfo(info: IPublishingInfo): void
}
```

## 🎨 用户界面设计

### 1. 排版工具面板布局
```html
<!-- 图书排版工具面板 -->
<div class="book-layout-panel">
  <!-- 模板选择区域 -->
  <div class="template-section">
    <h3>排版模板</h3>
    <div class="template-selector">
      <select class="template-dropdown">
        <option value="novel">小说模板</option>
        <option value="textbook">教材模板</option>
        <option value="academic">学术模板</option>
        <option value="magazine">杂志模板</option>
      </select>
      <button class="apply-template-btn">应用模板</button>
    </div>
  </div>

  <!-- 样式集管理区域 -->
  <div class="styleset-section">
    <h3>样式集管理</h3>
    <div class="styleset-controls">
      <button class="load-styleset-btn">加载样式集</button>
      <button class="save-styleset-btn">保存样式集</button>
      <button class="export-styleset-btn">导出样式</button>
      <button class="import-styleset-btn">导入样式</button>
    </div>
  </div>

  <!-- 版面布局区域 -->
  <div class="layout-section">
    <h3>版面布局</h3>
    <div class="layout-controls">
      <div class="column-settings">
        <label>分栏数量:</label>
        <input type="number" class="column-count" min="1" max="4" value="1">
      </div>
      <div class="chapter-settings">
        <button class="insert-chapter-break">插入章节分页</button>
        <button class="format-chapter-title">格式化章节标题</button>
      </div>
    </div>
  </div>

  <!-- 内容组织区域 -->
  <div class="content-section">
    <h3>内容组织</h3>
    <div class="content-controls">
      <button class="generate-toc-btn">生成目录</button>
      <button class="insert-footnote-btn">插入脚注</button>
      <button class="manage-index-btn">管理索引</button>
    </div>
  </div>

  <!-- 出版工具区域 -->
  <div class="publishing-section">
    <h3>出版工具</h3>
    <div class="publishing-controls">
      <button class="page-numbering-btn">页码设置</button>
      <button class="copyright-page-btn">版权页</button>
      <button class="cover-design-btn">封面设计</button>
    </div>
  </div>
</div>
```

### 2. 样式编辑器界面
```html
<!-- 样式编辑器对话框 -->
<div class="style-editor-dialog">
  <div class="style-editor-header">
    <h2>样式编辑器</h2>
    <button class="close-btn">×</button>
  </div>
  
  <div class="style-editor-content">
    <!-- 样式类型选择 -->
    <div class="style-type-selector">
      <button class="style-type-btn active" data-type="chapter">章标题</button>
      <button class="style-type-btn" data-type="section">节标题</button>
      <button class="style-type-btn" data-type="body">正文</button>
      <button class="style-type-btn" data-type="quote">引用</button>
      <button class="style-type-btn" data-type="footnote">脚注</button>
    </div>
    
    <!-- 样式属性编辑 -->
    <div class="style-properties">
      <div class="property-group">
        <h4>字体设置</h4>
        <div class="property-row">
          <label>字体:</label>
          <select class="font-family-select"></select>
        </div>
        <div class="property-row">
          <label>字号:</label>
          <input type="number" class="font-size-input">
        </div>
        <div class="property-row">
          <label>颜色:</label>
          <input type="color" class="font-color-input">
        </div>
      </div>
      
      <div class="property-group">
        <h4>段落设置</h4>
        <div class="property-row">
          <label>行高:</label>
          <input type="number" class="line-height-input" step="0.1">
        </div>
        <div class="property-row">
          <label>段前距:</label>
          <input type="number" class="margin-top-input">
        </div>
        <div class="property-row">
          <label>段后距:</label>
          <input type="number" class="margin-bottom-input">
        </div>
      </div>
    </div>
    
    <!-- 预览区域 -->
    <div class="style-preview">
      <h4>样式预览</h4>
      <div class="preview-content">
        <p class="preview-text">这是样式预览文本</p>
      </div>
    </div>
  </div>
  
  <div class="style-editor-footer">
    <button class="apply-btn">应用</button>
    <button class="cancel-btn">取消</button>
    <button class="save-btn">保存</button>
  </div>
</div>
```

## 📋 开发阶段规划

### 第一阶段：基础架构 (2周)
- [x] 创建模块目录结构
- [ ] 实现BookLayoutManager主管理器
- [ ] 扩展现有样式配置管理器
- [ ] 创建基础UI面板框架
- [ ] 集成到右侧工具栏

### 第二阶段：模板系统 (3周)
- [ ] 实现TemplateManager模板管理器
- [ ] 创建预设图书模板（小说、教材、学术、杂志）
- [ ] 实现模板应用和预览功能
- [ ] 开发模板编辑器界面
- [ ] 实现模板导入导出功能

### 第三阶段：样式集系统 (2周)
- [ ] 完善StyleSetManager样式集管理
- [ ] 实现样式集编辑器
- [ ] 开发批量样式应用功能
- [ ] 实现样式冲突检测
- [ ] 完善样式导入导出功能

### 第四阶段：版面布局 (3周)
- [ ] 实现分栏布局功能
- [ ] 开发章节布局管理
- [ ] 实现页面布局控制
- [ ] 开发文字环绕功能
- [ ] 实现版面平衡算法

### 第五阶段：内容组织 (2周)
- [ ] 实现目录自动生成
- [ ] 开发脚注尾注管理
- [ ] 实现索引管理功能
- [ ] 开发章节管理工具
- [ ] 实现交叉引用功能

### 第六阶段：出版工具 (2周)
- [ ] 实现复杂页码管理
- [ ] 开发版权页生成器
- [ ] 实现封面设计工具
- [ ] 开发出版格式导出
- [ ] 实现印刷预检功能

### 第七阶段：测试优化 (1周)
- [ ] 功能测试和Bug修复
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 文档编写
- [ ] 发布准备

## 🔧 技术实现要点

### 1. 与现有系统集成
- 基于现有的`FontStyleConfigManager`扩展样式管理
- 利用现有的Canvas渲染引擎
- 复用现有的命令系统和事件系统
- 与右侧工具栏无缝集成

### 2. 性能优化策略
- 样式应用的批量处理
- 模板预加载和缓存
- 增量渲染优化
- 内存使用优化

### 3. 数据持久化
- 样式集配置文件管理
- 模板文件存储
- 用户自定义设置保存
- 项目配置导入导出

### 4. 扩展性设计
- 插件化的模板系统
- 可配置的样式规则
- 开放的API接口
- 第三方模板支持

## 📊 预期成果

### 功能成果
1. **专业排版能力**: 提供图书出版级别的排版功能
2. **模板化工作流**: 快速应用专业排版模板
3. **样式管理系统**: 完整的样式集管理和应用
4. **内容组织工具**: 自动化的目录、索引、脚注管理
5. **出版工具集**: 页码、版权页、封面等出版工具

### 技术成果
1. **模块化架构**: 高度模块化的排版系统
2. **扩展性框架**: 支持第三方模板和样式
3. **性能优化**: 大文档的高效排版处理
4. **用户体验**: 直观易用的排版界面

### 商业价值
1. **专业定位**: 提升产品的专业化水平
2. **市场差异化**: 区别于普通文本编辑器
3. **用户粘性**: 专业用户的长期使用
4. **扩展空间**: 为未来功能扩展奠定基础

---

*开发计划制定时间: 2025年6月25日*  
*预计开发周期: 15周*  
*开发优先级: 高*
