/**
 * 编辑器API集成组件
 * 将Canvas Editor与后端API服务集成
 */

import { ApiService, DocumentService, UploadService } from '../../api'
import { debounce, formatApiError, isApiError } from '../../api/utils'
import type { Document, EditorData } from '../../api/types'

/**
 * 编辑器API集成类
 */
export class EditorApiIntegration {
  private editor: any // Canvas Editor实例
  private currentDocumentId: string | number | null = null
  private autoSaveEnabled = true
  private autoSaveInterval = 30000 // 30秒自动保存
  private autoSaveTimer: NodeJS.Timeout | null = null
  private isLoading = false
  private isSaving = false

  constructor(editor: any) {
    this.editor = editor
    this.init()
  }

  /**
   * 初始化API集成
   */
  private init(): void {
    // 绑定编辑器事件
    this.bindEditorEvents()

    // 启动自动保存
    if (this.autoSaveEnabled) {
      this.startAutoSave()
    }

    // 测试API连接
    this.testApiConnection()
  }

  /**
   * 绑定编辑器事件
   */
  private bindEditorEvents(): void {
    // 内容变化事件
    if (this.editor.listener) {
      const originalContentChange = this.editor.listener.contentChange

      this.editor.listener.contentChange = debounce(() => {
        // 调用原始的内容变化处理
        if (originalContentChange) {
          originalContentChange()
        }

        // 触发自动保存
        this.handleContentChange()
      }, 1000)
    }
  }

  /**
   * 处理内容变化
   */
  private handleContentChange(): void {
    if (this.autoSaveEnabled && this.currentDocumentId) {
      this.debouncedAutoSave()
    }
  }

  /**
   * 防抖的自动保存
   */
  private debouncedAutoSave = debounce(async () => {
    if (this.currentDocumentId && !this.isSaving) {
      await this.saveCurrentDocument()
    }
  }, 5000)

  /**
   * 测试API连接
   */
  private async testApiConnection(): Promise<void> {
    try {
      const isConnected = await ApiService.testConnection()
      if (isConnected) {
        console.log('✅ 编辑器API连接正常')
        this.showMessage('API连接正常', 'success')
      } else {
        console.warn('⚠️ 编辑器API连接失败')
        this.showMessage('API连接失败，部分功能可能不可用', 'warning')
      }
    } catch (error) {
      console.error('❌ 编辑器API连接测试出错:', error)
      this.showMessage('API连接出错', 'error')
    }
  }

  /**
   * 加载文档
   */
  async loadDocument(documentId: string | number): Promise<void> {
    if (this.isLoading) return

    this.isLoading = true
    this.showMessage('正在加载文档...', 'info')

    try {
      const document = await DocumentService.getDocument(documentId)

      // 设置编辑器内容
      if (document.content) {
        this.editor.command.executeSetValue(document.content)
      }

      // 更新当前文档ID
      this.currentDocumentId = documentId

      // 更新文档标题（如果有标题显示区域）
      this.updateDocumentTitle(document.title)

      this.showMessage('文档加载成功', 'success')

    } catch (error) {
      console.error('加载文档失败:', error)
      const message = isApiError(error) ? formatApiError(error) : '加载文档失败'
      this.showMessage(message, 'error')
    } finally {
      this.isLoading = false
    }
  }

  /**
   * 保存当前文档
   */
  async saveCurrentDocument(): Promise<Document | null> {
    if (this.isSaving || !this.currentDocumentId) return null

    this.isSaving = true
    this.showMessage('正在保存文档...', 'info')

    try {
      // 获取编辑器内容
      const content = this.editor.command.getValue()

      // 获取文档标题
      const title = this.getDocumentTitle() || '未命名文档'

      // 保存文档
      const document = await DocumentService.updateDocument(this.currentDocumentId, {
        title,
        content: content.data
      })

      this.showMessage('文档保存成功', 'success')
      return document

    } catch (error) {
      console.error('保存文档失败:', error)
      const message = isApiError(error) ? formatApiError(error) : '保存文档失败'
      this.showMessage(message, 'error')
      return null
    } finally {
      this.isSaving = false
    }
  }

  /**
   * 创建新文档
   */
  async createNewDocument(title?: string): Promise<Document | null> {
    if (this.isSaving) return null

    this.isSaving = true
    this.showMessage('正在创建文档...', 'info')

    try {
      // 获取编辑器内容
      const content = this.editor.command.getValue()

      // 创建文档
      const document = await DocumentService.createDocument({
        title: title || '新文档',
        content: content.data,
        is_public: false
      })

      // 更新当前文档ID
      this.currentDocumentId = document.id

      // 更新文档标题
      this.updateDocumentTitle(document.title)

      this.showMessage('文档创建成功', 'success')
      return document

    } catch (error) {
      console.error('创建文档失败:', error)
      const message = isApiError(error) ? formatApiError(error) : '创建文档失败'
      this.showMessage(message, 'error')
      return null
    } finally {
      this.isSaving = false
    }
  }

  /**
   * 上传图片
   */
  async uploadImage(file: File): Promise<string | null> {
    this.showMessage('正在上传图片...', 'info')

    try {
      const result = await UploadService.uploadImage(file)
      this.showMessage('图片上传成功', 'success')
      return result.url
    } catch (error) {
      console.error('上传图片失败:', error)
      const message = isApiError(error) ? formatApiError(error) : '上传图片失败'
      this.showMessage(message, 'error')
      return null
    }
  }

  /**
   * 启动自动保存
   */
  startAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
    }

    this.autoSaveTimer = setInterval(async () => {
      if (this.currentDocumentId && !this.isSaving) {
        await this.saveCurrentDocument()
      }
    }, this.autoSaveInterval)

    this.autoSaveEnabled = true
  }

  /**
   * 停止自动保存
   */
  stopAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = null
    }

    this.autoSaveEnabled = false
  }

  /**
   * 设置自动保存间隔
   */
  setAutoSaveInterval(interval: number): void {
    this.autoSaveInterval = interval

    if (this.autoSaveEnabled) {
      this.stopAutoSave()
      this.startAutoSave()
    }
  }

  /**
   * 获取当前文档ID
   */
  getCurrentDocumentId(): string | number | null {
    return this.currentDocumentId
  }

  /**
   * 设置当前文档ID
   */
  setCurrentDocumentId(documentId: string | number | null): void {
    this.currentDocumentId = documentId
  }

  /**
   * 获取文档标题
   */
  private getDocumentTitle(): string {
    // 尝试从页面标题元素获取
    const titleElement = document.querySelector('.document-title') as HTMLInputElement
    if (titleElement) {
      return titleElement.value || titleElement.textContent || ''
    }

    // 尝试从页面标题获取
    return document.title || ''
  }

  /**
   * 更新文档标题
   */
  private updateDocumentTitle(title: string): void {
    // 更新页面标题
    document.title = title

    // 更新标题元素
    const titleElement = document.querySelector('.document-title') as HTMLInputElement
    if (titleElement) {
      if (titleElement.tagName === 'INPUT' || titleElement.tagName === 'TEXTAREA') {
        titleElement.value = title
      } else {
        titleElement.textContent = title
      }
    }
  }

  /**
   * 显示消息
   */
  private showMessage(message: string, type: 'info' | 'success' | 'warning' | 'error'): void {
    // 根据消息类型使用不同的控制台输出方法
    switch (type) {
      case 'success':
        console.log(`✅ ${message}`)
        break
      case 'warning':
        console.warn(`⚠️ ${message}`)
        break
      case 'error':
        console.error(`❌ ${message}`)
        break
      default:
        console.log(`ℹ️ ${message}`)
    }

    // 如果有全局消息组件，可以在这里调用
    // 例如：MessageService.show(message, type)
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    this.stopAutoSave()
    this.currentDocumentId = null
  }
}

// 创建全局实例的工厂函数
export function createEditorApiIntegration(editor: any): EditorApiIntegration {
  return new EditorApiIntegration(editor)
}

// 导出默认实例
export default EditorApiIntegration
