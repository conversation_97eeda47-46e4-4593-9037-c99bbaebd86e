import Editor from '../../editor'
import html from './LayoutFullscreenButton.html'

/**
 * 布局菜单全屏按钮组件
 * 用于在布局菜单中切换全屏显示
 */
export class LayoutFullscreenButton {
  private dom: HTMLDivElement
  private instance: Editor

  constructor(instance: Editor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    this.bindEvents()
  }

  /**
   * 绑定事件处理器
   */
  private bindEvents(): void {
    // 点击按钮切换全屏
    this.dom.onclick = (e) => {
      e.stopPropagation()
      this.toggleFullscreen()
    }

    // 监听F11快捷键
    window.addEventListener('keydown', (evt) => {
      if (evt.key === 'F11') {
        this.toggleFullscreen()
        evt.preventDefault()
      }
    })

    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', () => {
      this.updateFullscreenState()
    })
  }

  /**
   * 切换全屏状态
   */
  private toggleFullscreen(): void {
    try {
      console.log('切换全屏状态')

      if (!document.fullscreenElement) {
        // 进入全屏
        document.documentElement.requestFullscreen().then(() => {
          console.log('已进入全屏模式')
        }).catch((err) => {
          console.error('进入全屏失败:', err)
        })
      } else {
        // 退出全屏
        document.exitFullscreen().then(() => {
          console.log('已退出全屏模式')
        }).catch((err) => {
          console.error('退出全屏失败:', err)
        })
      }
    } catch (error) {
      console.error('全屏切换失败:', error)
    }
  }

  /**
   * 更新全屏状态显示
   */
  private updateFullscreenState(): void {
    if (document.fullscreenElement) {
      // 当前处于全屏状态，显示退出全屏图标
      this.dom.classList.add('exist')
      this.dom.title = '退出全屏'
      console.log('全屏状态：已进入全屏')
    } else {
      // 当前不处于全屏状态，显示进入全屏图标
      this.dom.classList.remove('exist')
      this.dom.title = '全屏显示'
      console.log('全屏状态：已退出全屏')
    }
  }

  /**
   * 获取DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
}
