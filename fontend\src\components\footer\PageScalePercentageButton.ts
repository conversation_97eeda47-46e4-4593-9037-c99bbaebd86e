import { CanvasEditor } from '../../editor'
import html from './PageScalePercentageButton.html'
import './PageScalePercentageButton.css'

export class PageScalePercentageButton {
  private dom: HTMLSpanElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLSpanElement
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executePageScaleRecovery()
    }
  }

  public getElement(): HTMLSpanElement {
    return this.dom
  }

  // 更新按钮文本
  public updatePercentage(percentage: number): void {
    this.dom.innerText = `${Math.floor(percentage * 10 * 10)}%`
  }
} 