.font-button {
  width: 65px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
}

.font-button:hover {
  background: rgba(25, 55, 88, .04);
}

.font-button.active {
  background: rgba(25, 55, 88, .08);
}

.font-button .select {
  width: 100%;
  height: 100%;
  font-size: 12px;
  line-height: 24px;
  user-select: none;
  border: none;
}

.font-button .select::after {
  position: absolute;
  content: "";
  top: 11px;
  width: 0;
  height: 0;
  right: 2px;
  border-color: #767c85 transparent transparent;
  border-style: solid solid none;
  border-width: 3px 3px 0;
}

.font-button .options {
  width: 70px;
  position: absolute;
  left: 0;
  top: 25px;
  padding: 10px;
  background: #fff;
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  border-radius: 2px;
  display: none;
  z-index: 100;
}

.font-button .options.visible {
  display: block;
}

.font-button .options li {
  padding: 5px;
  margin: 5px 0;
  user-select: none;
  transition: all .3s;
  cursor: pointer;
}

.font-button .options li:hover {
  background-color: #ebecef;
}

.font-button .options li.active {
  background-color: #e2e6ed;
} 