import Editor from '../../editor'
import html from './CommentButton.html'
import './CommentButton.css'

/**
 * 批注按钮类
 * 用于控制批注面板的显示和隐藏
 */
export class CommentButton {
  private dom: HTMLDivElement
  private instance: Editor
  private isActive = false

  constructor(instance: Editor) {
    this.instance = instance
    
    // 创建DOM元素
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    // 设置标题
    this.dom.title = '批注'
    
    // 绑定事件
    this.bindEvents()
  }

  /**
   * 绑定点击事件
   */
  private bindEvents(): void {
    this.dom.onclick = (event) => {
      // 阻止事件冒泡
      event.stopPropagation()
      
      this.toggleCommentPanel()
    }
  }

  /**
   * 切换批注面板的显示状态
   */
  private toggleCommentPanel(): void {
    // 尝试首先使用全局函数
    const showCommentPanel = (window as any).showCommentPanel
    const hideCommentPanel = (window as any).hideCommentPanel
    
    // 获取评论面板元素
    const comment = document.querySelector<HTMLDivElement>('.comment')
    if (!comment) {
      console.error('评论面板元素不存在')
      return
    }
    
    if (this.isActive) {
      // 当前处于激活状态，需要隐藏批注面板
      if (typeof hideCommentPanel === 'function') {
        hideCommentPanel()
        console.log('评论面板已隐藏（通过全局函数）')
      } else {
        // 直接操作DOM隐藏
        comment.style.display = 'none'
        comment.style.opacity = '0'
        console.log('评论面板已隐藏（直接操作DOM）')
      }
      this.dom.classList.remove('active')
    } else {
      // 当前处于非激活状态，需要显示批注面板
      if (typeof showCommentPanel === 'function') {
        showCommentPanel()
        console.log('评论面板已显示（通过全局函数）')
      } else {
        // 直接操作DOM显示
        // 确保评论面板中有内容
        if (comment.innerHTML.trim() === '') {
          this.initCommentPanel(comment)
        }
        
        comment.style.display = 'block'
        comment.style.opacity = '1'
        console.log('评论面板已显示（直接操作DOM）')
      }
      this.dom.classList.add('active')
    }
    
    // 更新激活状态
    this.isActive = !this.isActive
    
    // 在控制台中打印事件，方便调试
    console.log('批注按钮点击事件触发', {
      isActive: this.isActive,
      commentDisplayStyle: comment.style.display,
      commentOpacity: comment.style.opacity,
      hasGlobalShowFn: typeof showCommentPanel === 'function',
      hasGlobalHideFn: typeof hideCommentPanel === 'function'
    })
  }
  
  /**
   * 初始化评论面板结构
   */
  private initCommentPanel(commentElement: HTMLDivElement): void {
    // 创建基本结构
    const headerDom = document.createElement('div')
    headerDom.classList.add('comment__header')
    
    const titleSpan = document.createElement('span')
    titleSpan.innerText = '批注'
    
    const closeDom = document.createElement('div')
    closeDom.classList.add('comment__header__close')
    closeDom.title = '关闭'
    
    const closeIcon = document.createElement('i')
    closeDom.appendChild(closeIcon)
    
    // 绑定关闭事件
    closeDom.addEventListener('click', () => {
      commentElement.style.display = 'none'
      commentElement.style.opacity = '0'
      this.isActive = false
      this.dom.classList.remove('active')
    })
    
    headerDom.appendChild(titleSpan)
    headerDom.appendChild(closeDom)
    commentElement.appendChild(headerDom)
    
    // 创建主体区域
    const mainDom = document.createElement('div')
    mainDom.classList.add('comment__main')
    commentElement.appendChild(mainDom)
    
    // 尝试触发更新评论列表
    if (typeof (window as any).updateComment === 'function') {
      (window as any).updateComment()
    }
  }

  /**
   * 获取DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
} 