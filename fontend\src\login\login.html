<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Editor - 用户登录</title>
    <link rel="stylesheet" href="login.css">
</head>
<body>
    <div class="login-container">
        <!-- 背景装饰 -->
        <div class="background-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>

        <!-- 登录卡片 -->
        <div class="login-card">
            <!-- 头部 -->
            <div class="login-header">
                <div class="logo">
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                        <rect width="40" height="40" rx="8" fill="#4F46E5"/>
                        <path d="M12 16h16M12 20h16M12 24h12" stroke="white" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </div>
                <h1 class="login-title">Canvas Editor</h1>
                <p class="login-subtitle">欢迎回来，请登录您的账户</p>
            </div>

            <!-- 登录表单 -->
            <form class="login-form" id="loginForm">
                <!-- 用户名输入 -->
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M10 10C12.7614 10 15 7.76142 15 5C15 2.23858 12.7614 0 10 0C7.23858 0 5 2.23858 5 5C5 7.76142 7.23858 10 10 10Z" fill="#9CA3AF"/>
                            <path d="M10 12.5C4.47715 12.5 0 16.9772 0 22.5H20C20 16.9772 15.5228 12.5 10 12.5Z" fill="#9CA3AF"/>
                        </svg>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            class="form-input"
                            placeholder="请输入用户名"
                            required
                            autocomplete="username"
                        >
                    </div>
                    <div class="error-message" id="usernameError"></div>
                </div>

                <!-- 密码输入 -->
                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M5 10V8C5 5.79086 6.79086 4 9 4H11C13.2091 4 15 5.79086 15 8V10M3 10H17C18.1046 10 19 10.8954 19 12V18C19 19.1046 18.1046 20 17 20H3C1.89543 20 1 19.1046 1 18V12C1 10.8954 1.89543 10 3 10Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="form-input"
                            placeholder="请输入密码"
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" id="passwordToggle">
                            <svg class="eye-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M1 10s4-8 9-8 9 8 9 8-4 8-9 8-9-8-9-8z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="10" cy="10" r="3" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    <div class="error-message" id="passwordError"></div>
                </div>

                <!-- 验证码输入 -->
                <div class="form-group">
                    <label for="captcha" class="form-label">验证码</label>
                    <div class="captcha-wrapper">
                        <div class="input-wrapper captcha-input">
                            <svg class="input-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <input
                                type="text"
                                id="captcha"
                                name="captcha"
                                class="form-input"
                                placeholder="请输入验证码"
                                required
                                maxlength="4"
                            >
                        </div>
                        <div class="captcha-display" id="captchaDisplay">
                            <span class="captcha-code" id="captchaCode">----</span>
                            <button type="button" class="captcha-refresh" id="captchaRefresh" title="刷新验证码">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M1.5 8a6.5 6.5 0 1113 0M1.5 8l3-3M1.5 8l3 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="error-message" id="captchaError"></div>
                </div>

                <!-- 记住我 -->
                <div class="form-group">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" id="rememberMe" name="rememberMe" class="checkbox-input">
                        <span class="checkbox-custom"></span>
                        <span class="checkbox-label">记住我</span>
                    </label>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" class="login-button" id="loginButton">
                    <span class="button-text">登录</span>
                    <div class="button-loading" id="buttonLoading">
                        <div class="loading-spinner"></div>
                    </div>
                </button>

                <!-- 错误提示 -->
                <div class="form-error" id="formError"></div>
            </form>

            <!-- 底部链接 -->
            <div class="login-footer">
                <p class="footer-text">
                    还没有账户？
                    <a href="#" class="footer-link" id="registerLink">立即注册</a>
                </p>
                <p class="footer-text">
                    <a href="#" class="footer-link" id="forgotPasswordLink">忘记密码？</a>
                </p>
            </div>
        </div>

        <!-- 版本信息 -->
        <div class="version-info">
            <p>Canvas Editor v1.0.0</p>
        </div>
    </div>

    <!-- 加载登录脚本 -->
    <script type="module" src="./login.ts"></script>
</body>
</html>
