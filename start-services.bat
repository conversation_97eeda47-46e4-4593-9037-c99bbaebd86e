@echo off
echo ========================================
echo    Canvas Editor 服务启动脚本
echo ========================================
echo.

REM 设置窗口标题
title Canvas Editor Services

REM 检查是否在正确的目录
if not exist "backend" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    echo    当前目录应包含 backend 和 fontend 文件夹
    pause
    exit /b 1
)

if not exist "fontend" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    echo    当前目录应包含 backend 和 fontend 文件夹
    pause
    exit /b 1
)

echo 📁 当前目录: %CD%
echo.

REM 启动后端服务
echo 🚀 启动后端服务...
echo ========================================
start "Canvas Editor Backend" cmd /k "cd backend && python start.py 8000"

REM 等待后端服务启动
echo ⏳ 等待后端服务启动 (5秒)...
timeout /t 5 /nobreak > nul

REM 测试后端连接
echo 🔍 测试后端连接...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/health/' -Method Get -TimeoutSec 10; Write-Host '✅ 后端服务正常运行' -ForegroundColor Green; Write-Host '   状态:' $response.status -ForegroundColor Cyan; Write-Host '   数据库:' $response.database -ForegroundColor Cyan } catch { Write-Host '❌ 后端服务连接失败' -ForegroundColor Red; Write-Host '   请检查后端服务是否正常启动' -ForegroundColor Yellow }"

echo.

REM 启动前端服务
echo 🚀 启动前端服务...
echo ========================================
start "Canvas Editor Frontend" cmd /k "cd fontend && npm run dev"

echo.
echo ✅ 服务启动完成！
echo.
echo 📋 访问地址:
echo    登录页面:     http://localhost:3001/login/
echo    主编辑器:     http://localhost:3001/Book-Editor/
echo    API测试面板:  http://localhost:3001/Book-Editor/API
echo    后端管理:     http://127.0.0.1:8000/admin/
echo    API文档:      http://127.0.0.1:8000/api/docs/
echo.
echo 💡 提示:
echo    - 两个服务窗口将保持打开状态
echo    - 关闭窗口将停止对应的服务
echo    - 如需重启服务，请关闭对应窗口后重新运行此脚本
echo.
echo 🔧 故障排除:
echo    - 如果前端无法连接后端，请确保后端服务正常运行
echo    - 如果端口被占用，请关闭占用端口的程序
echo    - 如有问题，请检查各服务窗口的错误信息
echo.

REM 等待用户确认
echo 按任意键关闭此窗口...
pause > nul
