---
layout: home

title: canvas-editor
titleTemplate: rich text editor by canvas/svg

hero:
  name: canvas-editor
  text: 基于canvas/svg的富文本编辑器
  actions:
    - theme: brand
      text: 开始
      link: /guide/start.html
    - theme: alt
      text: 在 GitHub 上查看
      link: https://github.com/Hufe921/canvas-editor

features:
  - icon: 💡
    title: 所见即所得
    details: 类word可分页，所见即所得
  - icon: ⚡️
    title: 轻量的数据结构
    details: 一段JSON即可呈现复杂样式
  - icon: 🛠️
    title: 丰富的功能
    details: 支持常见富文本操作、表格、水印、控件、公式等
  - icon: 📦
    title: 使用方便
    details: 官方发布核心npm包，菜单栏、工具栏可自行维护
  - icon: 🔩
    title: 灵活的开发机制
    details: 通过接口可获取生命周期、事件回调、自定义右键菜单、快捷键等
  - icon: 🔑
    title: 完全类型化的API
    details: 灵活的 API 和完整的 TypeScript 类型
---

<style>
  .main>p {
    max-width:100% !important;
  }
</style>
