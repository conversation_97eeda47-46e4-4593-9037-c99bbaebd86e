#!/usr/bin/env python
"""
MySQL 远程数据库连接测试脚本
测试远程 MySQL 数据库连接和 Django 配置
"""

import os
import sys
import django
from pathlib import Path

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_editor_backend.settings')
django.setup()

from django.db import connection, connections
from django.core.management import execute_from_command_line
from django.conf import settings
import pymysql

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def print_result(test_name, success, details=""):
    """打印测试结果"""
    status = "✅" if success else "❌"
    print(f"{status} {test_name}")
    if details:
        print(f"   {details}")

def test_mysql_client():
    """测试 MySQL 客户端"""
    print_header("MySQL 客户端测试")
    
    try:
        import pymysql
        print_result("PyMySQL 导入", True, f"版本: {pymysql.__version__}")
        return True
    except ImportError:
        print_result("PyMySQL 导入", False, "PyMySQL 未安装")
        return False

def test_direct_mysql_connection():
    """直接测试 MySQL 连接"""
    print_header("直接 MySQL 连接测试")
    
    try:
        connection = pymysql.connect(
            host='***********',
            port=3306,
            user='book_editor',
            password='eN2eB5mFKpA2PDmB',
            database='book_editor',
            charset='utf8mb4'
        )
        
        print_result("MySQL 连接", True, "连接成功")
        
        # 测试查询
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print_result("MySQL 版本", True, f"MySQL {version}")
            
            cursor.execute("SELECT DATABASE()")
            database = cursor.fetchone()[0]
            print_result("当前数据库", True, f"数据库: {database}")
            
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print_result("数据库表", True, f"共 {len(tables)} 个表")
        
        connection.close()
        return True
        
    except Exception as e:
        print_result("MySQL 连接", False, f"连接失败: {e}")
        return False

def test_django_database_config():
    """测试 Django 数据库配置"""
    print_header("Django 数据库配置测试")
    
    try:
        # 检查数据库配置
        db_config = settings.DATABASES['default']
        print_result("数据库引擎", True, db_config['ENGINE'])
        print_result("数据库名称", True, db_config['NAME'])
        print_result("数据库主机", True, f"{db_config['HOST']}:{db_config['PORT']}")
        print_result("数据库用户", True, db_config['USER'])
        
        # 检查数据库类型设置
        db_type = getattr(settings, 'DATABASE_TYPE', 'unknown')
        print_result("数据库类型配置", db_type == 'mysql', f"配置为: {db_type}")
        
        return db_type == 'mysql'
        
    except Exception as e:
        print_result("Django 配置", False, f"配置错误: {e}")
        return False

def test_django_database_connection():
    """测试 Django 数据库连接"""
    print_header("Django 数据库连接测试")
    
    try:
        # 测试数据库连接
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print_result("Django 数据库连接", True, "连接成功")
            
            # 获取数据库信息
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print_result("通过 Django 获取 MySQL 版本", True, f"MySQL {version}")
            
            cursor.execute("SELECT DATABASE()")
            database = cursor.fetchone()[0]
            print_result("当前数据库", True, f"数据库: {database}")
        
        return True
        
    except Exception as e:
        print_result("Django 数据库连接", False, f"连接失败: {e}")
        return False

def test_database_migrations():
    """测试数据库迁移"""
    print_header("数据库迁移测试")
    
    try:
        from django.core.management import call_command
        from io import StringIO
        
        # 检查迁移状态
        out = StringIO()
        call_command('showmigrations', stdout=out)
        migrations_output = out.getvalue()
        
        print_result("迁移状态检查", True, "迁移状态获取成功")
        
        # 统计迁移信息
        lines = migrations_output.strip().split('\n')
        applied_count = sum(1 for line in lines if '[X]' in line)
        unapplied_count = sum(1 for line in lines if '[ ]' in line)
        
        print_result("已应用迁移", True, f"{applied_count} 个")
        print_result("未应用迁移", unapplied_count == 0, f"{unapplied_count} 个")
        
        return True
        
    except Exception as e:
        print_result("迁移检查", False, f"检查失败: {e}")
        return False

def test_create_superuser_on_mysql():
    """测试在 MySQL 上创建超级用户"""
    print_header("MySQL 超级用户测试")
    
    try:
        from django.contrib.auth.models import User
        
        # 检查是否已有超级用户
        superusers = User.objects.filter(is_superuser=True)
        print_result("现有超级用户", True, f"共 {superusers.count()} 个")
        
        # 如果没有超级用户，创建一个
        if superusers.count() == 0:
            admin_user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123'
            )
            print_result("创建超级用户", True, f"用户名: {admin_user.username}")
        else:
            print_result("超级用户存在", True, f"用户: {', '.join([u.username for u in superusers])}")
        
        return True
        
    except Exception as e:
        print_result("超级用户操作", False, f"操作失败: {e}")
        return False

def test_api_models_on_mysql():
    """测试 API 模型在 MySQL 上的操作"""
    print_header("API 模型 MySQL 测试")
    
    try:
        from api.models import Document, DocumentVersion
        from django.contrib.auth.models import User
        
        # 获取或创建测试用户
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        
        if created:
            user.set_password('testpass123')
            user.save()
            print_result("创建测试用户", True, f"用户: {user.username}")
        else:
            print_result("测试用户存在", True, f"用户: {user.username}")
        
        # 创建测试文档
        doc, created = Document.objects.get_or_create(
            title='MySQL 测试文档',
            defaults={
                'content': {'type': 'doc', 'content': [{'type': 'paragraph', 'content': [{'type': 'text', 'text': '这是一个在 MySQL 数据库中的测试文档'}]}]},
                'author': user,
                'is_public': True,
                'tags': 'mysql,测试,远程数据库'
            }
        )
        
        if created:
            print_result("创建测试文档", True, f"文档: {doc.title}")
        else:
            print_result("测试文档存在", True, f"文档: {doc.title}")
        
        # 创建文档版本
        version, created = DocumentVersion.objects.get_or_create(
            document=doc,
            version_number=1,
            defaults={
                'content': doc.content,
                'created_by': user,
                'comment': 'MySQL 数据库测试版本'
            }
        )
        
        if created:
            print_result("创建文档版本", True, f"版本: v{version.version_number}")
        else:
            print_result("文档版本存在", True, f"版本: v{version.version_number}")
        
        # 统计数据
        doc_count = Document.objects.count()
        version_count = DocumentVersion.objects.count()
        user_count = User.objects.count()
        
        print_result("数据统计", True, f"文档: {doc_count}, 版本: {version_count}, 用户: {user_count}")
        
        return True
        
    except Exception as e:
        print_result("API 模型测试", False, f"操作失败: {e}")
        return False

def run_mysql_comprehensive_test():
    """运行 MySQL 综合测试"""
    print("🗄️ Canvas Editor Backend MySQL 远程数据库测试")
    print("="*70)
    
    # 检查当前目录
    if not Path("manage.py").exists():
        print("❌ 错误: 请在 backend 目录下运行此脚本")
        return False
    
    print(f"📁 测试目录: {os.getcwd()}")
    print(f"🔧 数据库类型: {getattr(settings, 'DATABASE_TYPE', 'unknown')}")
    print(f"🌐 MySQL 主机: {settings.DATABASES['default']['HOST']}")
    
    # 运行所有测试
    tests = [
        ("MySQL 客户端", test_mysql_client),
        ("直接 MySQL 连接", test_direct_mysql_connection),
        ("Django 数据库配置", test_django_database_config),
        ("Django 数据库连接", test_django_database_connection),
        ("数据库迁移", test_database_migrations),
        ("MySQL 超级用户", test_create_superuser_on_mysql),
        ("API 模型操作", test_api_models_on_mysql),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results.append((test_name, False))
    
    # 显示总结
    print_header("MySQL 测试总结")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 恭喜！MySQL 远程数据库配置完美！")
        print("✨ 所有数据现在都存储在远程 MySQL 数据库中！")
        print("\n🚀 启动项目:")
        print("   python start.py 8000")
        print("\n🔗 访问地址:")
        print("   管理后台: http://127.0.0.1:8000/admin/")
        print("   API 文档: http://127.0.0.1:8000/api/docs/")
    else:
        print("\n⚠️  MySQL 数据库配置需要进一步调整")
        print("\n💡 建议:")
        print("   1. 检查网络连接")
        print("   2. 验证 MySQL 服务器配置")
        print("   3. 确认数据库用户权限")
        print("   4. 运行数据库迁移: python manage.py migrate")
    
    return passed == total

if __name__ == "__main__":
    run_mysql_comprehensive_test()
