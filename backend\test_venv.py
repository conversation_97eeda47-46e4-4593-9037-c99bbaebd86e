#!/usr/bin/env python
"""
虚拟环境测试脚本
全面测试虚拟环境的配置和功能
"""

import sys
import os
import subprocess
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "="*50)
    print(f"🔍 {title}")
    print("="*50)

def print_result(test_name, success, details=""):
    """打印测试结果"""
    status = "✅" if success else "❌"
    print(f"{status} {test_name}")
    if details:
        print(f"   {details}")

def test_python_version():
    """测试 Python 版本"""
    print_header("Python 版本测试")
    
    version = sys.version_info
    version_str = f"{version.major}.{version.minor}.{version.micro}"
    
    print_result("Python 版本", True, f"Python {version_str}")
    print_result("Python 路径", True, sys.executable)
    
    # 检查是否在虚拟环境中
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    print_result("虚拟环境状态", in_venv, "在虚拟环境中" if in_venv else "不在虚拟环境中")
    
    return in_venv

def test_pip_functionality():
    """测试 pip 功能"""
    print_header("Pip 功能测试")
    
    try:
        # 测试 pip 版本
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            pip_version = result.stdout.strip()
            print_result("Pip 可用性", True, pip_version)
        else:
            print_result("Pip 可用性", False, "pip 命令失败")
            return False
        
        # 测试 pip list
        result = subprocess.run([sys.executable, "-m", "pip", "list"], 
                              capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            packages = result.stdout.strip().split('\n')
            package_count = len([p for p in packages if p and not p.startswith('-')])
            print_result("包列表获取", True, f"已安装 {package_count} 个包")
            return True
        else:
            print_result("包列表获取", False, "无法获取包列表")
            return False
            
    except subprocess.TimeoutExpired:
        print_result("Pip 功能", False, "pip 命令超时")
        return False
    except Exception as e:
        print_result("Pip 功能", False, f"错误: {e}")
        return False

def test_django_installation():
    """测试 Django 安装"""
    print_header("Django 安装测试")
    
    try:
        import django
        print_result("Django 导入", True, f"Django {django.get_version()}")
        
        # 测试 Django 命令
        result = subprocess.run([sys.executable, "-m", "django", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            django_version = result.stdout.strip()
            print_result("Django 命令", True, f"版本: {django_version}")
        else:
            print_result("Django 命令", False, "django 命令不可用")
        
        return True
    except ImportError:
        print_result("Django 导入", False, "Django 未安装")
        return False
    except Exception as e:
        print_result("Django 测试", False, f"错误: {e}")
        return False

def test_project_dependencies():
    """测试项目依赖"""
    print_header("项目依赖测试")
    
    dependencies = [
        ('rest_framework', 'Django REST Framework'),
        ('corsheaders', 'Django CORS Headers'),
        ('decouple', 'Python Decouple'),
        ('django_filters', 'Django Filter'),
        ('drf_spectacular', 'DRF Spectacular'),
        ('pymysql', 'PyMySQL'),
    ]
    
    success_count = 0
    for module, name in dependencies:
        try:
            __import__(module)
            print_result(name, True, "已安装")
            success_count += 1
        except ImportError:
            print_result(name, False, "未安装")
    
    print(f"\n📊 依赖安装情况: {success_count}/{len(dependencies)} 个包已安装")
    return success_count == len(dependencies)

def test_django_project():
    """测试 Django 项目"""
    print_header("Django 项目测试")
    
    # 检查 manage.py
    manage_py = Path("manage.py")
    print_result("manage.py 存在", manage_py.exists())
    
    if not manage_py.exists():
        return False
    
    try:
        # 测试 Django 检查
        result = subprocess.run([sys.executable, "manage.py", "check"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print_result("Django 项目检查", True, "项目配置正确")
        else:
            print_result("Django 项目检查", False, f"检查失败: {result.stderr}")
            return False
        
        # 测试数据库连接
        result = subprocess.run([sys.executable, "manage.py", "showmigrations"], 
                              capture_output=True, text=True, timeout=20)
        
        if result.returncode == 0:
            print_result("数据库连接", True, "数据库连接正常")
        else:
            print_result("数据库连接", False, "数据库连接失败")
        
        return True
        
    except subprocess.TimeoutExpired:
        print_result("Django 项目测试", False, "命令超时")
        return False
    except Exception as e:
        print_result("Django 项目测试", False, f"错误: {e}")
        return False

def test_api_functionality():
    """测试 API 功能"""
    print_header("API 功能测试")
    
    try:
        # 测试健康检查端点（需要启动服务器）
        print_result("API 模块", True, "API 应用已配置")
        
        # 检查 API 文件
        api_files = ['api/models.py', 'api/views.py', 'api/urls.py', 'api/serializers.py']
        all_exist = True
        for file_path in api_files:
            exists = Path(file_path).exists()
            print_result(f"API 文件 {file_path}", exists)
            if not exists:
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print_result("API 功能测试", False, f"错误: {e}")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 Canvas Editor Backend 虚拟环境综合测试")
    print("="*60)
    
    # 检查当前目录
    if not Path("manage.py").exists():
        print("❌ 错误: 请在 backend 目录下运行此脚本")
        return False
    
    print(f"📁 测试目录: {os.getcwd()}")
    print(f"🐍 Python 路径: {sys.executable}")
    
    # 运行所有测试
    tests = [
        ("Python 环境", test_python_version),
        ("Pip 功能", test_pip_functionality),
        ("Django 安装", test_django_installation),
        ("项目依赖", test_project_dependencies),
        ("Django 项目", test_django_project),
        ("API 功能", test_api_functionality),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results.append((test_name, False))
    
    # 显示总结
    print_header("测试总结")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 恭喜！虚拟环境配置完美！")
        print("✨ 您可以开始开发了！")
        print("\n🚀 启动项目:")
        print("   python start.py 8000")
    else:
        print("\n⚠️  虚拟环境需要进一步配置")
        print("\n💡 建议:")
        print("   1. 确保虚拟环境已激活")
        print("   2. 安装依赖: pip install -r requirements.txt")
        print("   3. 运行数据库迁移: python manage.py migrate")
    
    return passed == total

if __name__ == "__main__":
    run_comprehensive_test()
