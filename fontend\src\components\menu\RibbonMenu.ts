/**
 * Ribbon菜单控制器
 * 负责处理选项卡切换和菜单交互
 */
export class RibbonMenu {
  private container: HTMLElement
  private tabs: NodeListOf<HTMLElement>
  private panels: NodeListOf<HTMLElement>
  private currentTab = 'home'

  constructor(container: HTMLElement) {
    this.container = container
    this.tabs = container.querySelectorAll('.ribbon-tab')
    this.panels = container.querySelectorAll('.ribbon-panel')
    
    this.init()
  }

  /**
   * 初始化Ribbon菜单
   */
  private init(): void {
    this.bindEvents()
    this.showTab(this.currentTab)
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 选项卡点击事件
    this.tabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        const target = e.target as HTMLElement
        const tabName = target.dataset.tab
        if (tabName) {
          this.showTab(tabName)
        }
      })
    })

    // 键盘快捷键支持
    document.addEventListener('keydown', (e) => {
      if (e.altKey) {
        switch (e.key) {
          case 'h':
          case 'H':
            e.preventDefault()
            this.showTab('home')
            break
          case 'f':
          case 'F':
            e.preventDefault()
            this.showTab('font')
            break
          case 'p':
          case 'P':
            e.preventDefault()
            this.showTab('paragraph')
            break
          case 'i':
          case 'I':
            e.preventDefault()
            this.showTab('insert')
            break
          case 'l':
          case 'L':
            e.preventDefault()
            this.showTab('layout')
            break
          case 'r':
          case 'R':
            e.preventDefault()
            this.showTab('review')
            break
          case 'v':
          case 'V':
            e.preventDefault()
            this.showTab('view')
            break
        }
      }
    })
  }

  /**
   * 显示指定的选项卡
   * @param tabName 选项卡名称
   */
  public showTab(tabName: string): void {
    // 移除所有活动状态
    this.tabs.forEach(tab => {
      tab.classList.remove('active')
    })
    this.panels.forEach(panel => {
      panel.classList.remove('active')
    })

    // 激活指定的选项卡和面板
    const targetTab = this.container.querySelector(`[data-tab="${tabName}"]`) as HTMLElement
    const targetPanel = this.container.querySelector(`[data-panel="${tabName}"]`) as HTMLElement

    if (targetTab && targetPanel) {
      targetTab.classList.add('active')
      targetPanel.classList.add('active')
      this.currentTab = tabName
    }
  }

  /**
   * 获取当前活动的选项卡
   */
  public getCurrentTab(): string {
    return this.currentTab
  }

  /**
   * 添加选项卡
   * @param tabName 选项卡名称
   * @param tabLabel 选项卡标签
   * @param content 选项卡内容HTML
   */
  public addTab(tabName: string, tabLabel: string, content: string): void {
    // 创建选项卡标题
    const tabElement = document.createElement('div')
    tabElement.className = 'ribbon-tab'
    tabElement.dataset.tab = tabName
    tabElement.textContent = tabLabel

    // 创建选项卡面板
    const panelElement = document.createElement('div')
    panelElement.className = 'ribbon-panel'
    panelElement.dataset.panel = tabName
    panelElement.innerHTML = content

    // 添加到DOM
    const tabsContainer = this.container.querySelector('.ribbon-tabs')
    const contentContainer = this.container.querySelector('.ribbon-content')

    if (tabsContainer && contentContainer) {
      tabsContainer.appendChild(tabElement)
      contentContainer.appendChild(panelElement)

      // 重新绑定事件
      this.tabs = this.container.querySelectorAll('.ribbon-tab')
      this.panels = this.container.querySelectorAll('.ribbon-panel')
      this.bindEvents()
    }
  }

  /**
   * 移除选项卡
   * @param tabName 选项卡名称
   */
  public removeTab(tabName: string): void {
    const tab = this.container.querySelector(`[data-tab="${tabName}"]`)
    const panel = this.container.querySelector(`[data-panel="${tabName}"]`)

    if (tab && panel) {
      tab.remove()
      panel.remove()

      // 如果移除的是当前活动选项卡，切换到第一个选项卡
      if (this.currentTab === tabName) {
        const firstTab = this.container.querySelector('.ribbon-tab') as HTMLElement
        if (firstTab && firstTab.dataset.tab) {
          this.showTab(firstTab.dataset.tab)
        }
      }

      // 重新绑定事件
      this.tabs = this.container.querySelectorAll('.ribbon-tab')
      this.panels = this.container.querySelectorAll('.ribbon-panel')
    }
  }

  /**
   * 设置选项卡是否可见
   * @param tabName 选项卡名称
   * @param visible 是否可见
   */
  public setTabVisible(tabName: string, visible: boolean): void {
    const tab = this.container.querySelector(`[data-tab="${tabName}"]`) as HTMLElement
    const panel = this.container.querySelector(`[data-panel="${tabName}"]`) as HTMLElement

    if (tab && panel) {
      tab.style.display = visible ? 'block' : 'none'
      
      // 如果隐藏的是当前活动选项卡，切换到第一个可见选项卡
      if (!visible && this.currentTab === tabName) {
        const visibleTab = Array.from(this.tabs).find(t => 
          (t as HTMLElement).style.display !== 'none'
        ) as HTMLElement
        
        if (visibleTab && visibleTab.dataset.tab) {
          this.showTab(visibleTab.dataset.tab)
        }
      }
    }
  }

  /**
   * 获取所有选项卡名称
   */
  public getTabNames(): string[] {
    return Array.from(this.tabs).map(tab => 
      (tab as HTMLElement).dataset.tab || ''
    ).filter(name => name !== '')
  }

  /**
   * 销毁Ribbon菜单
   */
  public destroy(): void {
    // 移除事件监听器
    this.tabs.forEach(tab => {
      tab.removeEventListener('click', this.handleTabClick)
    })

    document.removeEventListener('keydown', this.handleKeydown)
  }

  /**
   * 更新选项卡内容
   * @param tabName 选项卡名称
   * @param content 新的内容HTML
   */
  public updateTabContent(tabName: string, content: string): void {
    const panel = this.container.querySelector(`[data-panel="${tabName}"]`) as HTMLElement
    if (panel) {
      panel.innerHTML = content
    }
  }

  /**
   * 设置选项卡标签
   * @param tabName 选项卡名称
   * @param label 新的标签文本
   */
  public setTabLabel(tabName: string, label: string): void {
    const tab = this.container.querySelector(`[data-tab="${tabName}"]`) as HTMLElement
    if (tab) {
      tab.textContent = label
    }
  }

  /**
   * 获取选项卡元素
   * @param tabName 选项卡名称
   */
  public getTabElement(tabName: string): HTMLElement | null {
    return this.container.querySelector(`[data-tab="${tabName}"]`)
  }

  /**
   * 获取面板元素
   * @param tabName 选项卡名称
   */
  public getPanelElement(tabName: string): HTMLElement | null {
    return this.container.querySelector(`[data-panel="${tabName}"]`)
  }
}
