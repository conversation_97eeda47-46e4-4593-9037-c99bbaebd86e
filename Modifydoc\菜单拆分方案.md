# 菜单拆分方案

## 1. 项目结构准备
1. 创建组件目录结构
   - 创建 `components/menu` 目录用于存放顶部菜单组件
   - 创建 `components/footer` 目录用于存放底部菜单组件

## 2. 顶部菜单组件拆分
按照功能区块划分组件，每个按钮独立成组件：

### 2.1 文档操作组
1. UndoButton - 撤销按钮
2. RedoButton - 重做按钮
3. PainterButton - 格式刷按钮
4. FormatButton - 清除格式按钮

### 2.2 字体样式组
1. FontButton - 字体选择按钮
2. FontSizeButton - 字号选择按钮
3. FontSizeAddButton - 增大字号按钮
4. FontSizeMinusButton - 减小字号按钮
5. BoldButton - 加粗按钮
6. ItalicButton - 斜体按钮
7. UnderlineButton - 下划线按钮
8. StrikeoutButton - 删除线按钮
9. SuperscriptButton - 上标按钮
10. SubscriptButton - 下标按钮
11. ColorButton - 字体颜色按钮
12. HighlightButton - 高亮按钮

### 2.3 段落样式组
1. TitleButton - 标题样式按钮
2. AlignLeftButton - 左对齐按钮
3. AlignCenterButton - 居中对齐按钮
4. AlignRightButton - 右对齐按钮
5. AlignmentButton - 两端对齐按钮
6. JustifyButton - 分散对齐按钮
7. RowMarginButton - 行间距按钮
8. ListButton - 列表按钮

### 2.4 插入元素组
1. TableButton - 表格按钮
2. ImageButton - 图片按钮
3. HyperlinkButton - 超链接按钮
4. SeparatorButton - 分割线按钮
5. WatermarkButton - 水印按钮
6. CodeBlockButton - 代码块按钮
7. PageBreakButton - 分页符按钮
8. ControlButton - 控件按钮
9. CheckboxButton - 复选框按钮
10. RadioButton - 单选框按钮
11. LatexButton - LaTeX按钮
12. DateButton - 日期按钮
13. BlockButton - 内容块按钮

### 2.5 功能操作组
1. SearchButton - 搜索按钮
2. PrintButton - 打印按钮

## 3. 底部菜单组件拆分

1. CatalogModeButton - 目录按钮
2. PageModeButton - 页面模式按钮
3. PageScaleMinusButton - 缩小按钮
4. PageScalePercentageButton - 显示比例按钮
5. PageScaleAddButton - 放大按钮
6. PaperSizeButton - 纸张类型按钮
7. PaperDirectionButton - 纸张方向按钮
8. PaperMarginButton - 页边距按钮
9. FullscreenButton - 全屏显示按钮
10. EditorOptionButton - 编辑器设置按钮
11. EditorModeButton - 编辑模式按钮

## 4. 组件实现步骤

每个组件的实现遵循以下步骤：
1. 从index.html提取对应按钮的HTML结构到独立的.html文件
2. 从style.css提取对应按钮的CSS样式到独立的.css文件
3. 从main.ts提取对应按钮的交互逻辑到独立的.ts文件
4. 修改main.ts以导入和使用新组件

## 5. 入口文件修改
1. 修改index.html，引用新的组件
2. 修改main.ts，使用新组件替代原来的直接操作DOM
3. 清理style.css中已迁移的样式

## 6. 测试与确认
1. 确保每个功能按钮正常工作
2. 确保样式表现一致
3. 确保没有功能丢失或变更 