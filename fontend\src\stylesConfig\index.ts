/**
 * 样式配置模块导出文件
 * 统一管理所有样式配置相关的导出
 */

export { FontStyleConfigManager, fontStyleConfigManager } from './FontStyleConfigManager'
export type { ITextStyle, IFontStyleConfig } from './FontStyleConfigManager'

/**
 * 初始化样式配置模块
 * 确保配置管理器正确初始化
 */
export async function initStylesConfigModule(): Promise<void> {
  try {
    const { fontStyleConfigManager } = await import('./FontStyleConfigManager')
    // 等待配置加载完成
    await fontStyleConfigManager.reloadConfig()
    console.log('✅ 样式配置模块初始化完成')
  } catch (error) {
    console.error('❌ 样式配置模块初始化失败:', error)
  }
}
