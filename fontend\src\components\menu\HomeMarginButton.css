/* 开始菜单页边距按钮容器 */
.menu-item .menu-item__home-margin {
  width: 60px;
  position: relative;
}

/* 开始菜单页边距选择显示区域 */
.menu-item__home-margin .select {
  width: 40px;
  height: 100%;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-top: 10px;
}

/* 开始菜单页边距按钮图标 */
.menu-item__home-margin i {
  transform: translateX(-5px);
  background-image: url('../../assets/images/margin.svg');
  padding-top: 10px;
}

/* 开始菜单页边距选择下拉框 */
.menu-item__home-margin .options {
  width: 140px;
  position: fixed !important;
  z-index: 999999 !important;
  background: #fff;
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  pointer-events: none;
}

/* 下拉框显示状态 */
.menu-item__home-margin .options.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

/* 开始菜单页边距选项样式 */
.menu-item__home-margin .options li {
  padding: 8px 12px;
  font-size: 12px;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 开始菜单页边距选项悬停效果 */
.menu-item__home-margin .options li:hover {
  background: #f5f7fa;
  color: #409eff;
}
