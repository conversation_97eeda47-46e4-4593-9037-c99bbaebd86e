import { CanvasEditor } from '../../editor'
import html from './PageScaleAddButton.html'
import './PageScaleAddButton.css'

export class PageScaleAddButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executePageScaleAdd()
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 