/* 新字体按钮样式 */
.new-font-button {
  width: 120px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
  border-radius: 4px;
  transition: all 0.2s ease;
  background: #ffffff; /* 改为纯白背景，提高对比度 */
  border: 1px solid #d4d7de; /* 稍微加深边框颜色 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 添加轻微阴影 */
}

.new-font-button:hover {
  background: rgba(25, 55, 88, .04);
  border-color: #c0c4cc;
}

.new-font-button.active {
  background: rgba(25, 55, 88, .08);
  border-color: #409eff;
}

/* 字体选择显示区域 */
.new-font-button .select {
  width: 100%;
  height: 32px;
  font-size: 13px;
  line-height: 32px;
  user-select: none;
  border: none;
  padding: 0 20px 0 8px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #303133; /* 改为更深的颜色，提高可读性 */
  background: transparent;
  font-weight: 500; /* 增加字体粗细 */
}

/* 下拉箭头 */
.new-font-button .select::after {
  position: absolute;
  content: "";
  top: 50%;
  right: 6px;
  width: 0;
  height: 0;
  margin-top: -2px;
  border-color: #606266 transparent transparent; /* 加深箭头颜色 */
  border-style: solid solid none;
  border-width: 4px 4px 0;
  transition: transform 0.2s ease;
}

/* 下拉框展开时箭头旋转 */
.new-font-button .options.visible + .select::after,
.new-font-button.active .select::after {
  transform: rotate(180deg);
}

/* 字体选择下拉框 */
.new-font-button .options {
  position: fixed;
  top: 100%;
  left: 0;
  width: 150px;
  max-height: 300px;
  background: #fff;
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 99999; /* 确保浮于最上层 */
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  /* 移除移动动画效果：取消 transform: translateY(-10px) */
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

/* 下拉框显示状态 */
.new-font-button .options.visible {
  opacity: 1;
  visibility: visible;
  /* 移除移动动画效果：取消 transform: translateY(0) */
}

/* 字体选项列表 */
.new-font-button .options ul {
  list-style: none;
  margin: 0;
  padding: 4px 0;
  max-height: 296px;
  overflow-y: auto;
}

/* 滚动条样式 */
.new-font-button .options ul::-webkit-scrollbar {
  width: 6px;
}

.new-font-button .options ul::-webkit-scrollbar-track {
  background: #f5f7fa;
}

.new-font-button .options ul::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.new-font-button .options ul::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

/* 字体选项项目 */
.new-font-button .options li {
  padding: 8px 12px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid transparent;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 字体选项悬停效果 */
.new-font-button .options li:hover {
  background: #f5f7fa;
  color: #409eff;
}

/* 字体选项激活状态 */
.new-font-button .options li.active {
  background: #ecf5ff;
  color: #409eff;
  font-weight: 600;
}

.new-font-button .options li.active::after {
  content: "✓";
  float: right;
  color: #409eff;
  font-weight: bold;
}

/* 字体分组分隔线 */
.new-font-button .options li:nth-child(5)::after,
.new-font-button .options li:nth-child(15)::after {
  content: "";
  position: absolute;
  left: 12px;
  right: 12px;
  bottom: 0;
  height: 1px;
  background: #e2e6ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .new-font-button {
    width: 100px;
  }
  
  .new-font-button .options {
    width: 140px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .new-font-button {
    border-width: 2px;
  }
  
  .new-font-button .options {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .new-font-button,
  .new-font-button .select::after,
  .new-font-button .options,
  .new-font-button .options li {
    transition: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .new-font-button {
    background: rgba(255, 255, 255, 0.1);
    border-color: #4c4d4f;
  }
  
  .new-font-button:hover {
    background: rgba(255, 255, 255, 0.15);
  }
  
  .new-font-button .select {
    color: #3b3b3b;
  }
  
  .new-font-button .options {
    background: #ffffff;
    border-color: #e0e0e0;
  }
  
  .new-font-button .options li {
    color: #414141;
  }
  
  .new-font-button .options li:hover {
    background: #3a3d41;
  }
  
  .new-font-button .options li.active {
    background: #1e3a5f;
    color: #66b3ff;
  }
}
