{"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "parserOptions": {"ecmaVersion": 6, "sourceType": "module"}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "env": {"browser": true}, "globals": {"process": true}, "rules": {"linebreak-style": 0, "no-console": 0, "no-debugger": 0, "no-useless-escape": "off", "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/no-empty-interface": 0, "@typescript-eslint/no-this-alias": 0, "@typescript-eslint/ban-ts-comment": 0, "@typescript-eslint/explicit-module-boundary-types": 0, "@typescript-eslint/no-non-null-assertion": 0, "@typescript-eslint/ban-types": [1, {"types": {"Function": false, "{}": false}, "extendDefaults": true}], "no-constant-condition": ["error", {"checkLoops": false}], "semi": [1, "never"], "quotes": [1, "single", {"allowTemplateLiterals": true}]}, "ignorePatterns": ["node_modules", "dist", "index.html"]}