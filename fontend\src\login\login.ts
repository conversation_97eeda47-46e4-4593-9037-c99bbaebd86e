/**
 * 登录页面逻辑
 */

// 导入API相关模块
import { AuthService } from './AuthService'

// 接口定义
interface CaptchaResponse {
  captcha_key: string
  captcha_code: string
  expires_in: number
}

interface LoginRequest {
  username: string
  password: string
  captcha_key: string
  captcha_code: string
}

interface LoginResponse {
  message: string
  user: {
    id: number
    username: string
    email: string
    first_name: string
    last_name: string
  }
  session_token: string
  expires_in: number
}

// 登录页面类
class LoginPage {
  private form: HTMLFormElement
  private usernameInput: HTMLInputElement
  private passwordInput: HTMLInputElement
  private captchaInput: HTMLInputElement
  private captchaDisplay: HTMLElement
  private captchaCode: HTMLElement
  private captchaRefresh: HTMLButtonElement
  private passwordToggle: HTMLButtonElement
  private loginButton: HTMLButtonElement
  private rememberMeCheckbox: HTMLInputElement
  private formError: HTMLElement

  private currentCaptchaKey = ''
  private isLoading = false

  constructor() {
    this.initializeElements()
    this.bindEvents()
    this.loadCaptcha()
  }

  /**
   * 初始化DOM元素
   */
  private initializeElements(): void {
    this.form = document.getElementById('loginForm') as HTMLFormElement
    this.usernameInput = document.getElementById('username') as HTMLInputElement
    this.passwordInput = document.getElementById('password') as HTMLInputElement
    this.captchaInput = document.getElementById('captcha') as HTMLInputElement
    this.captchaDisplay = document.getElementById('captchaDisplay') as HTMLElement
    this.captchaCode = document.getElementById('captchaCode') as HTMLElement
    this.captchaRefresh = document.getElementById('captchaRefresh') as HTMLButtonElement
    this.passwordToggle = document.getElementById('passwordToggle') as HTMLButtonElement
    this.loginButton = document.getElementById('loginButton') as HTMLButtonElement
    this.rememberMeCheckbox = document.getElementById('rememberMe') as HTMLInputElement
    this.formError = document.getElementById('formError') as HTMLElement

    // 检查必要元素是否存在
    if (!this.form || !this.usernameInput || !this.passwordInput) {
      console.error('登录页面关键元素未找到')
      return
    }
  }

  /**
   * 绑定事件监听器
   */
  private bindEvents(): void {
    // 表单提交事件
    this.form.addEventListener('submit', this.handleSubmit.bind(this))

    // 密码显示切换
    this.passwordToggle?.addEventListener('click', this.togglePasswordVisibility.bind(this))

    // 验证码刷新
    this.captchaRefresh?.addEventListener('click', this.loadCaptcha.bind(this))

    // 输入验证
    this.usernameInput.addEventListener('blur', () => this.validateField('username'))
    this.passwordInput.addEventListener('blur', () => this.validateField('password'))
    this.captchaInput.addEventListener('blur', () => this.validateField('captcha'))

    // 清除错误状态
    this.usernameInput.addEventListener('input', () => this.clearFieldError('username'))
    this.passwordInput.addEventListener('input', () => this.clearFieldError('password'))
    this.captchaInput.addEventListener('input', () => this.clearFieldError('captcha'))

    // 注册和忘记密码链接（暂时显示提示）
    document.getElementById('registerLink')?.addEventListener('click', (e) => {
      e.preventDefault()
      alert('注册功能正在开发中...')
    })

    document.getElementById('forgotPasswordLink')?.addEventListener('click', (e) => {
      e.preventDefault()
      alert('忘记密码功能正在开发中...')
    })
  }

  /**
   * 加载验证码
   */
  private async loadCaptcha(): Promise<void> {
    try {
      this.captchaRefresh.disabled = true

      const response = await fetch('/api/auth/captcha/')
      if (!response.ok) {
        throw new Error('获取验证码失败')
      }

      const data: CaptchaResponse = await response.json()
      this.currentCaptchaKey = data.captcha_key
      this.captchaCode.textContent = data.captcha_code

      // 清空验证码输入
      this.captchaInput.value = ''
      this.clearFieldError('captcha')

    } catch (error) {
      console.error('加载验证码失败:', error)
      this.captchaCode.textContent = '----'
      this.showError('获取验证码失败，请刷新页面重试')
    } finally {
      this.captchaRefresh.disabled = false
    }
  }

  /**
   * 切换密码可见性
   */
  private togglePasswordVisibility(): void {
    const isPassword = this.passwordInput.type === 'password'
    this.passwordInput.type = isPassword ? 'text' : 'password'

    // 更新图标（这里可以添加图标切换逻辑）
    const eyeIcon = this.passwordToggle.querySelector('.eye-icon')
    if (eyeIcon) {
      eyeIcon.style.opacity = isPassword ? '0.7' : '1'
    }
  }

  /**
   * 处理表单提交
   */
  private async handleSubmit(event: Event): Promise<void> {
    event.preventDefault()

    if (this.isLoading) return

    // 验证表单
    if (!this.validateForm()) {
      return
    }

    this.setLoading(true)
    this.hideError()

    try {
      const loginData: LoginRequest = {
        username: this.usernameInput.value.trim(),
        password: this.passwordInput.value,
        captcha_key: this.currentCaptchaKey,
        captcha_code: this.captchaInput.value.trim()
      }

      const response = await fetch('/api/auth/login/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || '登录失败')
      }

      // 登录成功
      await this.handleLoginSuccess(data as LoginResponse)

    } catch (error) {
      console.error('登录失败:', error)
      this.handleLoginError(error as Error)
      // 刷新验证码
      await this.loadCaptcha()
    } finally {
      this.setLoading(false)
    }
  }

  /**
   * 处理登录成功
   */
  private async handleLoginSuccess(data: LoginResponse): Promise<void> {
    // 保存用户信息和令牌
    AuthService.saveUserSession(data.user, data.session_token, data.expires_in)

    // 如果选择了记住我，保存到localStorage
    if (this.rememberMeCheckbox.checked) {
      AuthService.setRememberMe(true)
    }

    // 显示成功消息
    this.showSuccess('登录成功，正在跳转...')

    // 延迟跳转到主页面
    setTimeout(() => {
      window.location.href = '/Book-Editor/'
    }, 1000)
  }

  /**
   * 处理登录错误
   */
  private handleLoginError(error: Error): void {
    let errorMessage = '登录失败，请重试'

    // 根据错误类型显示不同消息
    if (error.message.includes('验证码')) {
      errorMessage = '验证码错误，请重新输入'
      this.setFieldError('captcha', '验证码错误')
    } else if (error.message.includes('用户名') || error.message.includes('密码')) {
      errorMessage = '用户名或密码错误'
      this.setFieldError('username', '')
      this.setFieldError('password', '用户名或密码错误')
    } else if (error.message.includes('禁用')) {
      errorMessage = '账户已被禁用，请联系管理员'
    }

    this.showError(errorMessage)
  }

  /**
   * 验证表单
   */
  private validateForm(): boolean {
    let isValid = true

    // 验证用户名
    if (!this.validateField('username')) {
      isValid = false
    }

    // 验证密码
    if (!this.validateField('password')) {
      isValid = false
    }

    // 验证验证码
    if (!this.validateField('captcha')) {
      isValid = false
    }

    return isValid
  }

  /**
   * 验证单个字段
   */
  private validateField(fieldName: string): boolean {
    const field = this[`${fieldName}Input` as keyof this] as HTMLInputElement
    const value = field.value.trim()

    let errorMessage = ''

    switch (fieldName) {
      case 'username':
        if (!value) {
          errorMessage = '请输入用户名'
        } else if (value.length < 3) {
          errorMessage = '用户名至少3个字符'
        }
        break

      case 'password':
        if (!value) {
          errorMessage = '请输入密码'
        } else if (value.length < 6) {
          errorMessage = '密码至少6个字符'
        }
        break

      case 'captcha':
        if (!value) {
          errorMessage = '请输入验证码'
        } else if (value.length !== 4) {
          errorMessage = '验证码为4位数字'
        } else if (!/^\d{4}$/.test(value)) {
          errorMessage = '验证码只能包含数字'
        }
        break
    }

    if (errorMessage) {
      this.setFieldError(fieldName, errorMessage)
      return false
    } else {
      this.clearFieldError(fieldName)
      return true
    }
  }

  /**
   * 设置字段错误
   */
  private setFieldError(fieldName: string, message: string): void {
    const field = this[`${fieldName}Input` as keyof this] as HTMLInputElement
    const errorElement = document.getElementById(`${fieldName}Error`)

    field.classList.add('error')
    field.classList.remove('success')

    if (errorElement) {
      errorElement.textContent = message
    }
  }

  /**
   * 清除字段错误
   */
  private clearFieldError(fieldName: string): void {
    const field = this[`${fieldName}Input` as keyof this] as HTMLInputElement
    const errorElement = document.getElementById(`${fieldName}Error`)

    field.classList.remove('error')
    field.classList.add('success')

    if (errorElement) {
      errorElement.textContent = ''
    }
  }

  /**
   * 设置加载状态
   */
  private setLoading(loading: boolean): void {
    this.isLoading = loading
    this.loginButton.disabled = loading

    if (loading) {
      this.loginButton.classList.add('loading')
    } else {
      this.loginButton.classList.remove('loading')
    }
  }

  /**
   * 显示错误消息
   */
  private showError(message: string): void {
    this.formError.textContent = message
    this.formError.classList.add('show')
  }

  /**
   * 隐藏错误消息
   */
  private hideError(): void {
    this.formError.classList.remove('show')
  }

  /**
   * 显示成功消息
   */
  private showSuccess(message: string): void {
    this.formError.textContent = message
    this.formError.style.background = '#f0f9ff'
    this.formError.style.borderColor = '#0ea5e9'
    this.formError.style.color = '#0369a1'
    this.formError.classList.add('show')
  }
}

// 页面加载完成后初始化登录页面
document.addEventListener('DOMContentLoaded', () => {
  new LoginPage()
})
