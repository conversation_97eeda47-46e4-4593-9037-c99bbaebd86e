.menu-item__separator {
  position: relative;
}

.menu-item__separator>i {
  background-image: url('../../assets/images/separator.svg');
}

/* 分隔符下拉框 - 智能定位样式 */
.menu-item .menu-item__separator .options {
  width: 150px; /* 增加宽度 */
  position: fixed !important; /* 使用fixed定位确保浮于最上层 */
  z-index: 999999 !important; /* 确保最高层级 */
  background: #fff;
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  /* 直接显示，不要动画效果 */
  opacity: 1;
  visibility: visible;
  transform: none;
  transition: none;
  pointer-events: auto;
}

/* 隐藏状态 */
.menu-item .menu-item__separator .options:not(.visible) {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.menu-item .menu-item__separator li {
  padding: 1px 5px;
}

.menu-item__separator li i {
  pointer-events: none;
}

.menu-item__separator li[data-separator="0,0"] {
  background-image: url('../../assets/images/line-single.svg');
}

.menu-item__separator li[data-separator="1,1"] {
  background-image: url('../../assets/images/line-dot.svg');
}

.menu-item__separator li[data-separator="3,1"] {
  background-image: url('../../assets/images/line-dash-small-gap.svg');
}

.menu-item__separator li[data-separator="4,4"] {
  background-image: url('../../assets/images/line-dash-large-gap.svg');
}

.menu-item__separator li[data-separator="7,3,3,3"] {
  background-image: url('../../assets/images/line-dash-dot.svg');
}

.menu-item__separator li[data-separator="6,2,2,2,2,2"] {
  background-image: url('../../assets/images/line-dash-dot-dot.svg');
} 