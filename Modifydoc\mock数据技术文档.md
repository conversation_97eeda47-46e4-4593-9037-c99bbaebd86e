# Mock数据技术文档

## 📋 文件概述

`src/mock.ts` 是Canvas Editor的模拟数据文件，包含了完整的医疗病历文档示例和编辑器配置。该文件展示了编辑器支持的所有元素类型和功能特性。

**文件规模**: 506行
**主要用途**: 开发测试、功能演示、类型展示
**数据类型**: 医疗文档模拟数据

## 🏗️ 架构设计

### 整体结构
```
mock.ts
├── 导入类型定义 (1-6行)
├── 原始文本数据 (8-10行)
├── 数据处理模块 (12-45行)
├── 元素生成模块 (47-500行)
├── 评论数据模块 (502-514行)
└── 配置选项模块 (516-525行)
```

## 📊 数据模块分析

### 1. 原始文本数据模块

#### 1.1 主文本内容
```typescript
const text = `主诉：\n发热三天，咳嗽五天。\n现病史：\n...`
```

**特征分析**:
- **内容类型**: 医疗病历文档
- **字符数量**: ~800字符
- **结构化程度**: 高（包含明确的章节标题）
- **语言**: 中文简体
- **领域**: 医疗健康

**包含章节**:
1. 主诉
2. 现病史
3. 既往史
4. 流行病史
5. 体格检查
6. 辅助检查
7. 门诊诊断
8. 处置治疗
9. 电子签名
10. 其他记录

#### 1.2 标题数据结构
```typescript
const titleText = [
  '主诉：', '现病史：', '既往史：', '流行病史：',
  '体格检查：', '辅助检查：', '门诊诊断：',
  '处置治疗：', '电子签名：', '其他记录：'
]
```

**技术特点**:
- **数据类型**: `string[]`
- **元素数量**: 10个标题
- **命名规范**: 医疗标准术语
- **分隔符**: 统一使用冒号结尾

#### 1.3 语义标记数据
```typescript
// 颜色标记文本
const colorText = ['传染性疾病']

// 高亮标记文本
const highlightText = ['血细胞比容']
```

**设计目的**:
- **颜色文本**: 标记重要医学术语
- **高亮文本**: 突出检查项目，支持批注功能

### 2. 数据处理模块

#### 2.1 标题位置映射算法
```typescript
const titleMap: Map<number, string> = new Map()
for (let t = 0; t < titleText.length; t++) {
  const value = titleText[t]
  const i = text.indexOf(value)
  if (~i) {
    titleMap.set(i, value)
  }
}
```

**算法分析**:
- **时间复杂度**: O(n×m)，n为标题数，m为文本长度
- **空间复杂度**: O(n)
- **数据结构**: `Map<number, string>`
- **返回值**: 字符位置 → 标题文本的映射

**技术亮点**:
- 使用位运算符 `~i` 检查 `indexOf` 结果
- Map结构便于快速位置查找
- 支持标题的精确定位

#### 2.2 文本索引计算算法
```typescript
const colorIndex: number[] = colorText
  .map(b => {
    const i = text.indexOf(b)
    return ~i
      ? Array(b.length)
          .fill(i)
          .map((_, j) => i + j)
      : []
  })
  .flat()
```

**算法特征**:
- **函数式编程**: 使用map、flat等高阶函数
- **索引生成**: 为每个字符生成独立索引
- **数组扁平化**: 合并多个标记文本的索引
- **边界处理**: 自动处理未找到的情况

**性能优化**:
- 一次遍历生成所有索引
- 避免重复计算
- 内存效率高

## 🔧 功能模块分析

### 3. 元素生成模块

#### 3.1 文本元素生成器
```typescript
const elementList: IElement[] = []
const textList = text.split('')
let index = 0
while (index < textList.length) {
  const value = textList[index]
  const title = titleMap.get(index)

  if (title) {
    // 生成标题元素
    elementList.push({
      value: '',
      type: ElementType.TITLE,
      level: TitleLevel.FIRST,
      valueList: [{ value: title, size: 18 }]
    })
    index += title.length - 1
  } else if (colorIndex.includes(index)) {
    // 生成彩色文本元素
    elementList.push({
      value,
      color: '#FF0000',
      size: 16
    })
  } else if (highlightIndex.includes(index)) {
    // 生成高亮文本元素
    elementList.push({
      value,
      highlight: '#F2F27F',
      groupIds: ['1'] // 关联批注
    })
  } else {
    // 生成普通文本元素
    elementList.push({
      value,
      size: 16
    })
  }
  index++
}
```

**核心机制**:
- **逐字符处理**: 确保精确的格式控制
- **条件分支**: 根据位置应用不同样式
- **索引管理**: 正确处理多字符标题的跳跃
- **元素类型**: 支持标题、彩色、高亮、普通文本

#### 3.2 控件元素生成器

##### 文本控件
```typescript
elementList.splice(12, 0, {
  type: ElementType.CONTROL,
  value: '',
  control: {
    conceptId: '1',
    type: ControlType.TEXT,
    value: null,
    placeholder: '其他补充',
    prefix: '{',
    postfix: '}'
  }
})
```

##### 下拉选择控件
```typescript
elementList.splice(94, 0, {
  type: ElementType.CONTROL,
  value: '',
  control: {
    conceptId: '2',
    type: ControlType.SELECT,
    value: null,
    code: null,
    placeholder: '有无',
    prefix: '{',
    postfix: '}',
    valueSets: [
      { value: '有', code: '98175' },
      { value: '无', code: '98176' },
      { value: '不详', code: '98177' }
    ]
  }
})
```

##### 复选框控件
```typescript
{
  type: ElementType.CONTROL,
  control: {
    conceptId: '3',
    type: ControlType.CHECKBOX,
    code: '98175',
    value: '',
    valueSets: [
      { value: '同意', code: '98175' },
      { value: '否定', code: '98176' }
    ]
  },
  value: ''
}
```

##### 日期控件
```typescript
{
  type: ElementType.CONTROL,
  value: '',
  control: {
    conceptId: '5',
    type: ControlType.DATE,
    value: [{ value: `2022-08-10 17:30:01` }],
    placeholder: '签署日期'
  }
}
```

**控件设计特点**:
- **概念ID**: 每个控件有唯一标识符
- **类型系统**: 支持文本、选择、复选、日期等
- **值集合**: 下拉和复选框支持预定义选项
- **编码标准**: 使用医疗行业标准编码
- **样式控制**: 支持前缀、后缀、占位符

#### 3.3 复杂元素生成器

##### 超链接元素
```typescript
{
  type: ElementType.HYPERLINK,
  value: '',
  valueList: [
    { value: '新', size: 16 },
    { value: '冠', size: 16 },
    { value: '肺', size: 16 },
    { value: '炎', size: 16 }
  ],
  url: 'https://hufe.club/canvas-editor'
}
```

##### 上下标元素
```typescript
// 下标
{
  value: '∆',
  color: '#FF0000',
  type: ElementType.SUBSCRIPT
}

// 上标
{
  value: '9',
  type: ElementType.SUPERSCRIPT
}
```

##### 列表元素
```typescript
{
  value: '',
  type: ElementType.LIST,
  listType: ListType.OL,
  valueList: [
    { value: '高血压\n糖尿病\n病毒性感冒\n过敏性鼻炎\n过敏性鼻息肉' }
  ]
}
```

##### LaTeX数学公式
```typescript
{
  value: `{E_k} = hv - {W_0}`,
  type: ElementType.LATEX
}
```

##### Base64图片元素
```typescript
{
  value: `data:image/png;base64,iVBORw0KGgo...`,
  width: 89,
  height: 32,
  id: 'signature',
  type: ElementType.IMAGE
}
```

#### 3.4 表格元素生成器
```typescript
{
  type: ElementType.TABLE,
  value: '',
  colgroup: [
    { width: 180 },
    { width: 80 },
    { width: 130 },
    { width: 130 }
  ],
  trList: [
    {
      height: 40,
      tdList: [
        {
          colspan: 1,
          rowspan: 2,
          value: [
            { value: `1`, size: 16 },
            { value: '.', size: 16 }
          ]
        },
        // ... 更多单元格
      ]
    }
    // ... 更多行
  ]
}
```

**表格特性**:
- **列组定义**: 精确控制列宽度
- **行高设置**: 支持自定义行高
- **单元格合并**: 支持colspan和rowspan
- **内容格式**: 单元格内支持富文本
- **嵌套结构**: 三层嵌套（表格→行→单元格）

## 💬 评论数据模块

### 4. 批注系统数据
```typescript
interface IComment {
  id: string
  content: string
  userName: string
  rangeText: string
  createdDate: string
}

export const commentList: IComment[] = [
  {
    id: '1',
    content: '红细胞比容（HCT）是指每单位容积中红细胞所占全血容积的比值，用于反映红细胞和血浆的比例。',
    userName: 'Hufe',
    rangeText: '血细胞比容',
    createdDate: '2023-08-20 23:10:55'
  }
]
```

**数据结构特点**:
- **唯一标识**: id字段确保评论唯一性
- **内容丰富**: 支持长文本专业解释
- **用户信息**: 记录评论作者
- **关联文本**: rangeText关联文档中的具体文字
- **时间戳**: 精确到秒的创建时间

## ⚙️ 配置模块

### 5. 编辑器选项配置
```typescript
export const options: IEditorOption = {
  margins: [100, 120, 100, 120], // 上右下左边距
  watermark: {
    data: 'CANVAS-EDITOR',
    size: 120
  },
  pageNumber: {
    format: '第{pageNo}页/共{pageCount}页'
  },
  placeholder: {
    data: '请输入正文'
  },
  zone: {
    tipDisabled: false
  },
  maskMargin: [60, 0, 30, 0] // 菜单栏高度60，底部工具栏30
}
```

**配置项分析**:
- **页面边距**: CSS盒模型，支持精确像素控制
- **水印设置**: 文字水印，可调整大小
- **页码格式**: 模板字符串，支持动态变量
- **占位符**: 空文档时的提示文字
- **区域控制**: UI提示和遮罩层设置

## 🔨 技术实现细节

### 6. 算法优化策略

#### 6.1 字符串处理优化
```typescript
// 高效的位运算检查
if (~i) { } // 等价于 if (i !== -1) 但性能更优

// 函数式编程链式调用
const colorIndex: number[] = colorText
  .map(/* 映射函数 */)
  .flat() // 扁平化数组
```

#### 6.2 内存管理策略
- **懒加载**: 大文本分块处理
- **索引缓存**: Map结构提高查找效率
- **对象复用**: 减少临时对象创建

#### 6.3 数据结构选择
- **Map**: 位置索引映射（O(1)查找）
- **Array**: 线性数据存储
- **Set**: 去重操作（如果需要）

### 7. 扩展性设计

#### 7.1 模块化拆分建议
```typescript
// 数据模块
export class MockDataProvider {
  static getText(): string { }
  static getTitles(): string[] { }
  static getColorTexts(): string[] { }
  static getHighlightTexts(): string[] { }
}

// 处理模块
export class TextProcessor {
  static createTitleMap(text: string, titles: string[]): Map<number, string> { }
  static createColorIndex(text: string, colorTexts: string[]): number[] { }
  static createHighlightIndex(text: string, highlightTexts: string[]): number[] { }
}

// 生成模块
export class ElementGenerator {
  static createTextElements(text: string, titleMap: Map<number, string>): IElement[] { }
  static createControlElements(): IElement[] { }
  static createTableElements(): IElement[] { }
}

// 配置模块
export class ConfigProvider {
  static getEditorOptions(): IEditorOption { }
  static getCommentList(): IComment[] { }
}
```

#### 7.2 配置驱动设计
```typescript
interface IMockConfig {
  textSource: 'medical' | 'legal' | 'academic'
  includeControls: boolean
  includeTables: boolean
  includeImages: boolean
  language: 'zh-CN' | 'en-US'
}

export class MockGenerator {
  constructor(private config: IMockConfig) {}

  generate(): IElement[] {
    // 根据配置生成不同类型的mock数据
  }
}
```

## 📊 性能分析

### 8. 性能指标

#### 8.1 时间复杂度
- **文本处理**: O(n)，n为文本长度
- **标题映射**: O(m×n)，m为标题数，n为平均标题长度
- **索引生成**: O(k×n)，k为标记文本数
- **元素创建**: O(n)，线性时间

#### 8.2 空间复杂度
- **原始数据**: ~2KB（文本字符串）
- **处理数据**: ~1KB（索引数组）
- **元素数组**: ~50KB（完整元素列表）
- **总内存**: ~53KB

#### 8.3 优化建议
1. **延迟计算**: 按需生成元素
2. **数据分页**: 大文档分块加载
3. **索引优化**: 使用更高效的查找算法
4. **缓存机制**: 缓存计算结果

## 🧪 测试用例设计

### 9. 测试覆盖

#### 9.1 单元测试
```typescript
describe('MockDataProvider', () => {
  test('should generate correct title map', () => {
    const titleMap = TextProcessor.createTitleMap(text, titleText)
    expect(titleMap.size).toBe(10)
    expect(titleMap.get(0)).toBe('主诉：')
  })

  test('should generate color index correctly', () => {
    const colorIndex = TextProcessor.createColorIndex(text, colorText)
    expect(colorIndex.length).toBeGreaterThan(0)
    expect(colorIndex).toContain(text.indexOf('传染性疾病'))
  })
})
```

#### 9.2 集成测试
```typescript
describe('ElementGenerator', () => {
  test('should generate complete element list', () => {
    const elements = ElementGenerator.generate()
    expect(elements.length).toBeGreaterThan(100)

    const titleElements = elements.filter(e => e.type === ElementType.TITLE)
    expect(titleElements.length).toBe(10)

    const controlElements = elements.filter(e => e.type === ElementType.CONTROL)
    expect(controlElements.length).toBeGreaterThan(0)
  })
})
```

## 🚀 使用示例

### 10. 实际应用

#### 10.1 基本使用
```typescript
import { data, commentList, options } from './mock'

// 初始化编辑器
const editor = new Editor(container, {
  main: data,
  footer: []
}, options)

// 加载批注
editor.command.setCommentList(commentList)
```

#### 10.2 自定义mock数据
```typescript
// 生成简化版本
const simpleData = data.filter(element =>
  element.type !== ElementType.CONTROL &&
  element.type !== ElementType.TABLE
)

// 生成特定类型元素
const onlyTextElements = data.filter(element =>
  !element.type || element.type === ElementType.TEXT
)
```

## 📈 统计信息

- **总行数**: 506行
- **文本字符数**: ~800字符
- **元素总数**: ~200个元素
- **控件数量**: 6个不同类型控件
- **表格数量**: 1个复杂表格
- **图片数量**: 1个Base64图片
- **标题级别**: 10个一级标题
- **批注数量**: 1个医学解释批注

## 🔮 扩展方向

### 11. 未来改进

#### 11.1 数据来源多样化
- **API数据**: 从后端API获取真实数据
- **模板系统**: 支持多种文档模板
- **随机生成**: 程序化生成mock数据

#### 11.2 功能增强
- **国际化**: 支持多语言mock数据
- **主题切换**: 不同行业的文档样式
- **动态配置**: 运行时调整mock参数

#### 11.3 性能优化
- **流式处理**: 大文档流式加载
- **Web Worker**: 后台处理大量数据
- **虚拟化**: 长列表虚拟滚动

---

**文档版本**: v1.0
**创建时间**: 2025年1月8日
**技术栈**: TypeScript, Canvas Editor
**维护状态**: 活跃开发