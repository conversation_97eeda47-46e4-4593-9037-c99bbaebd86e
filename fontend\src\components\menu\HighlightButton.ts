import './HighlightButton.css'

export class HighlightButton {
  private element: HTMLDivElement;
  private highlightControlDom: HTMLInputElement;
  private highlightSpanDom: HTMLSpanElement;
  private command: any;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.highlight-button') as HTMLDivElement
    this.highlightControlDom = this.element.querySelector('#highlight') as HTMLInputElement
    this.highlightSpanDom = this.element.querySelector('span') as HTMLSpanElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="highlight-button" title="高亮">
      <i></i>
      <span></span>
      <input type="color" id="highlight">
    </div>`
  }

  private bindEvents(): void {
    this.highlightControlDom.oninput = () => {
      this.command.executeHighlight(this.highlightControlDom.value)
    }
    
    this.element.onclick = () => {
      console.log('highlight')
      this.highlightControlDom.click()
    }
  }

  // 更新按钮状态
  public updateState(highlight: string | null): void {
    if (highlight) {
      this.element.classList.add('active')
      this.highlightControlDom.value = highlight
      this.highlightSpanDom.style.backgroundColor = highlight
    } else {
      this.element.classList.remove('active')
      this.highlightControlDom.value = '#ffff00'
      this.highlightSpanDom.style.backgroundColor = '#ffff00'
    }
  }
} 