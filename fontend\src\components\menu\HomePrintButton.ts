import { Editor } from '../../editor'
import html from './HomePrintButton.html'
import './HomePrintButton.css'

/**
 * 开始菜单打印按钮组件
 * 用于执行文档打印功能
 * 与视图菜单中的PrintButton功能完全一致
 */
export class HomePrintButton {
  private dom: HTMLDivElement
  private isApple: boolean
  private instance: Editor

  constructor(instance: Editor) {
    this.instance = instance
    this.isApple = /Mac OS X/i.test(navigator.userAgent)

    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    // 设置标题
    this.dom.title = `打印(${this.isApple ? '⌘' : 'Ctrl'}+P)`

    this.bindEvents()
  }

  /**
   * 绑定事件处理器
   */
  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 执行打印命令
      this.instance.command.executePrint()
    }

    // 添加工具提示更新
    this.dom.addEventListener('mouseenter', () => {
      this.updateTooltip()
    })
  }

  /**
   * 更新工具提示
   */
  private updateTooltip(): void {
    this.dom.title = `打印(${this.isApple ? '⌘' : 'Ctrl'}+P)`
  }

  /**
   * 获取DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
}