import './BoldButton.css'

export class BoldButton {
  private element: HTMLDivElement;
  private command: any;
  private isApple: boolean;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    this.isApple = typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent)
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.bold-button') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="bold-button" title="加粗(${this.isApple ? '⌘' : 'Ctrl'}+B)">
      <i></i>
    </div>`
  }

  private bindEvents(): void {
    this.element.onclick = () => {
      console.log('bold')
      this.command.executeBold()
    }
  }

  // 更新按钮状态
  public updateState(isBold: boolean): void {
    if (isBold) {
      this.element.classList.add('active')
    } else {
      this.element.classList.remove('active')
    }
  }
}