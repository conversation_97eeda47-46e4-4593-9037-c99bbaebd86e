<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/png" href="favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Book-Editor</title>
</head>

<body>
  <div id="app">
    <div class="editor"></div>
    <!-- 菜单容器，内容将由JavaScript动态加载src/components/menu/menu-index.html -->
    <div class="menu" editor-component="menu"></div>
    <!-- 目录容器，内容将由Catalog组件动态生成 -->
    <div class="catalog-container" editor-component="catalog"></div>
    <!-- 右侧工具栏容器，内容将由RightTools组件动态生成 -->
    <div class="right-tools-container" editor-component="right-tools"></div>

    <div class="comment" editor-component="comment"></div>
    <!-- 底部菜单容器，内容将由Footer组件动态生成 -->
    <div class="footer-container" editor-component="footer"></div>
  </div>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>
