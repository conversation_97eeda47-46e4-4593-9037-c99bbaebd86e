@echo off
REM Canvas Editor 开发环境启动脚本
REM 同时启动前端和后端开发服务器

echo ========================================
echo 🚀 Canvas Editor 开发环境启动脚本
echo ========================================
echo.

REM 检查是否在正确的目录
if not exist "fontend" (
    echo ❌ 错误：请在 canvas-editor 根目录下运行此脚本
    pause
    exit /b 1
)

if not exist "backend" (
    echo ❌ 错误：未找到 backend 目录
    pause
    exit /b 1
)

echo 📡 启动后端服务 (Django)...
start "Django Backend" cmd /k "cd backend && python manage.py runserver 127.0.0.1:8000"

echo ⏳ 等待后端服务启动...
timeout /t 3 /nobreak >nul

echo 🌐 启动前端服务 (Vite)...
start "Vite Frontend" cmd /k "cd fontend && npm run dev"

echo.
echo ✅ 开发服务器启动完成！
echo.
echo 📋 服务信息：
echo   - 后端服务: http://127.0.0.1:8000
echo   - 前端服务: http://localhost:3001/Book-Editor/
echo   - API健康检查: http://127.0.0.1:8000/api/health/
echo.
echo 💡 提示：
echo   - 两个服务将在新的命令行窗口中运行
echo   - 关闭对应的命令行窗口即可停止服务
echo   - 如果端口被占用，服务会自动选择其他可用端口
echo.
pause
