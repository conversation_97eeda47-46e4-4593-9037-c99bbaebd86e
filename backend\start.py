#!/usr/bin/env python
"""
Django 后端启动脚本
提供便捷的启动选项和环境检查
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """检查依赖是否已安装"""
    try:
        import django
        import rest_framework
        import corsheaders
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_database():
    """检查数据库连接"""
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_editor_backend.settings')
        import django
        django.setup()
        
        from django.db import connection
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        print("✅ 数据库连接正常")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def run_migrations():
    """运行数据库迁移"""
    print("🔄 运行数据库迁移...")
    try:
        subprocess.run([sys.executable, "manage.py", "migrate"], check=True)
        print("✅ 数据库迁移完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 数据库迁移失败")
        return False

def start_server(port=8000):
    """启动开发服务器"""
    print(f"🚀 启动开发服务器 (端口: {port})...")
    try:
        subprocess.run([sys.executable, "manage.py", "runserver", str(port)])
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

def main():
    """主函数"""
    print("🎯 Canvas Editor Backend 启动器")
    print("=" * 40)
    
    # 检查当前目录
    if not Path("manage.py").exists():
        print("❌ 请在 backend 目录下运行此脚本")
        sys.exit(1)
    
    # 检查依赖
    if not check_requirements():
        sys.exit(1)
    
    # 检查数据库
    if not check_database():
        print("⚠️  数据库连接失败，但继续启动...")
    
    # 运行迁移
    if not run_migrations():
        print("⚠️  迁移失败，但继续启动...")
    
    # 启动服务器
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8000
    start_server(port)

if __name__ == "__main__":
    main()
