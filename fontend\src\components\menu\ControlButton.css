.menu-item__control {
  position: relative;
}

.menu-item__control i {
  background-image: url('../../assets/images/control.svg');
}

/* 控件下拉框 - 智能定位样式 */
.menu-item .menu-item__control .options {
  width: 80px; /* 增加宽度 */
  position: fixed !important; /* 使用fixed定位确保浮于最上层 */
  z-index: 999999 !important; /* 确保最高层级 */
  background: #fff;
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  /* 直接显示，不要动画效果 */
  opacity: 1;
  visibility: visible;
  transform: none;
  transition: none;
  pointer-events: auto;
}

/* 隐藏状态 */
.menu-item .menu-item__control .options:not(.visible) {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

/* 控件选项样式 */
.menu-item .menu-item__control .options li {
  padding: 8px 12px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 控件选项悬停效果 */
.menu-item .menu-item__control .options li:hover {
  background: #f5f7fa;
  color: #409eff;
}