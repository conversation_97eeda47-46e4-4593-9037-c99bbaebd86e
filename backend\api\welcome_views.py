"""
欢迎页面视图
"""
from django.http import JsonResponse
from django.shortcuts import render
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
import datetime

@api_view(['GET'])
def api_root(request):
    """
    API 根路径 - 显示可用的 API 端点
    """
    return Response({
        'message': 'Canvas Editor Backend API',
        'version': '1.0.0',
        'timestamp': datetime.datetime.now().isoformat(),
        'database': getattr(settings, 'DATABASE_TYPE', 'unknown'),
        'endpoints': {
            'admin': '/admin/',
            'api_docs': '/api/docs/',
            'api_redoc': '/api/redoc/',
            'health_check': '/api/health/',
            'documents': '/api/documents/',
            'schema': '/api/schema/',
        },
        'description': 'Canvas Editor 后端 API 服务'
    })

def home_view(request):
    """
    主页视图 - 显示欢迎页面
    """
    context = {
        'title': 'Canvas Editor Backend',
        'version': '1.0.0',
        'database_type': getattr(settings, 'DATABASE_TYPE', 'unknown'),
        'debug': settings.DEBUG,
        'endpoints': [
            {'name': '管理后台', 'url': '/admin/', 'description': 'Django 管理界面'},
            {'name': 'API 文档', 'url': '/api/docs/', 'description': 'Swagger API 文档'},
            {'name': 'ReDoc 文档', 'url': '/api/redoc/', 'description': 'ReDoc API 文档'},
            {'name': '健康检查', 'url': '/api/health/', 'description': 'API 健康状态'},
            {'name': '文档 API', 'url': '/api/documents/', 'description': '文档管理 API'},
            {'name': 'API Schema', 'url': '/api/schema/', 'description': 'OpenAPI Schema'},
        ]
    }
    
    # 如果是 API 请求，返回 JSON
    if request.headers.get('Accept') == 'application/json' or 'api' in request.path:
        return JsonResponse(context)
    
    # 否则返回 HTML 页面
    return render(request, 'welcome.html', context)
