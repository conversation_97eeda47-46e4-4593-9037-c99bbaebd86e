import Editor from '../../editor'
import { Dialog } from '../dialog/Dialog'
import html from './LayoutPaperMarginButton.html'

/**
 * 布局菜单页边距按钮组件
 * 用于在布局菜单中设置页边距
 */
export class LayoutPaperMarginButton {
  private dom: HTMLDivElement
  private instance: Editor

  constructor(instance: Editor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    this.bindEvents()
  }

  /**
   * 绑定事件处理器
   */
  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation()
      this.openPaperMarginDialog()
    }
  }

  /**
   * 打开页边距设置对话框
   */
  private openPaperMarginDialog(): void {
    try {
      // 获取当前页边距设置
      const [topMargin, rightMargin, bottomMargin, leftMargin] = this.instance.command.getPaperMargin()

      console.log('当前页边距:', { topMargin, rightMargin, bottomMargin, leftMargin })

      // 创建页边距设置对话框
      new Dialog({
        title: '页边距',
        data: [
          {
            type: 'text',
            label: '上边距',
            name: 'top',
            required: true,
            value: `${topMargin}`,
            placeholder: '请输入上边距'
          },
          {
            type: 'text',
            label: '下边距',
            name: 'bottom',
            required: true,
            value: `${bottomMargin}`,
            placeholder: '请输入下边距'
          },
          {
            type: 'text',
            label: '左边距',
            name: 'left',
            required: true,
            value: `${leftMargin}`,
            placeholder: '请输入左边距'
          },
          {
            type: 'text',
            label: '右边距',
            name: 'right',
            required: true,
            value: `${rightMargin}`,
            placeholder: '请输入右边距'
          }
        ],
        onConfirm: (data) => {
          this.setPaperMargin(data)
        }
      })
    } catch (error) {
      console.error('打开页边距对话框失败:', error)
    }
  }

  /**
   * 设置页边距
   */
  private setPaperMargin(payload: Array<{ name: string; value: string }>): void {
    try {
      const topMargin = parseInt(payload.find(p => p.name === 'top')?.value || '0') || 0
      const bottomMargin = parseInt(payload.find(p => p.name === 'bottom')?.value || '0') || 0
      const leftMargin = parseInt(payload.find(p => p.name === 'left')?.value || '0') || 0
      const rightMargin = parseInt(payload.find(p => p.name === 'right')?.value || '0') || 0

      console.log('设置页边距:', { topMargin, rightMargin, bottomMargin, leftMargin })

      // 执行页边距设置命令
      this.instance.command.executeSetPaperMargin([topMargin, rightMargin, bottomMargin, leftMargin])

      console.log('页边距设置完成')
    } catch (error) {
      console.error('设置页边距失败:', error)
    }
  }

  /**
   * 获取DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
}
