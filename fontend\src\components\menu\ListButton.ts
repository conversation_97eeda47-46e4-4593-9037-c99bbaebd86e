import { ListType, ListStyle } from '../../editor/dataset/enum/List'
import { CanvasEditor } from '../../editor'
import html from './ListButton.html'
import './ListButton.css'

export class ListButton {
  private dom: HTMLDivElement
  private optionDom: HTMLDivElement
  private instance: CanvasEditor
  private documentClickHandler: (e: MouseEvent) => void

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.optionDom = this.dom.querySelector<HTMLDivElement>('.options')!
    
    // 设置快捷键提示
    const isApple = /Mac|iPod|iPhone|iPad/.test(navigator.platform)
    this.dom.title = `列表(${isApple ? '⌘' : 'Ctrl'}+Shift+U)`
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 切换显示状态
      const isVisible = this.optionDom.classList.contains('visible')

      // 先隐藏所有其他的下拉框
      this.hideAllDropdowns()

      if (!isVisible) {
        // 显示当前下拉框并定位
        this.showDropdown()
      }
    }

    this.optionDom.onclick = (evt) => {
      evt.stopPropagation() // 阻止事件冒泡
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        const listType = <ListType>li.dataset.listType || null
        const listStyle = <ListStyle>(<unknown>li.dataset.listStyle)
        this.instance.command.executeList(listType, listStyle)

        // 选择后关闭下拉框
        this.hideDropdown()
      }
    }

    // 阻止下拉框内的点击事件冒泡到document
    this.optionDom.addEventListener('click', (e) => {
      e.stopPropagation()
    })

    // 点击外部关闭下拉框
    this.documentClickHandler = (e) => {
      const target = e.target as Node
      if (!this.dom.contains(target) && !this.optionDom.contains(target)) {
        this.hideDropdown()
      }
    }
    document.addEventListener('click', this.documentClickHandler)
  }

  // 显示下拉框并定位到按钮下方
  private showDropdown(): void {
    // 先设置基本样式
    this.optionDom.style.position = 'fixed'
    this.optionDom.style.zIndex = '999999'

    // 添加visible类
    this.optionDom.classList.add('visible')

    // 等待一帧后计算位置，确保元素已渲染
    requestAnimationFrame(() => {
      this.positionDropdown()
    })
  }

  // 精确定位下拉框到按钮下方
  private positionDropdown(): void {
    const rect = this.dom.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 默认位置：按钮下方
    let left = rect.left
    let top = rect.bottom + 4

    // 水平边界检查
    if (left + 150 > viewportWidth) {
      left = viewportWidth - 150 - 10
    }
    if (left < 10) {
      left = 10
    }

    // 垂直边界检查
    if (top + 200 > viewportHeight) {
      top = rect.top - 200 - 4
    }
    if (top < 10) {
      top = 10
    }

    // 应用位置
    this.optionDom.style.left = left + 'px'
    this.optionDom.style.top = top + 'px'
  }

  // 隐藏下拉框
  private hideDropdown(): void {
    this.optionDom.classList.remove('visible')
  }

  // 隐藏所有下拉框（避免多个下拉框同时显示）
  private hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options.visible')
    allDropdowns.forEach(dropdown => {
      dropdown.classList.remove('visible')
    })
  }
  
  // 销毁组件时移除全局事件监听
  public destroy(): void {
    document.removeEventListener('click', this.documentClickHandler)
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 