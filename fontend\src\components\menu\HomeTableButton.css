/* 开始菜单表格按钮容器 */
.menu-item .menu-item__home-table {
  width: 25px;
  position: relative;
}

/* 开始菜单表格按钮图标 */
.menu-item__home-table i {
  background-image: url('../../assets/images/table.svg');
}

/* 开始菜单表格面板 */
.menu-item__home-table .table-panel {
  width: 250px;
  background: #fff;
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  position: fixed !important;
  display: none;
  z-index: 999999 !important;
  padding: 2px;
  cursor: auto;
}

/* 表格选择容器 */
.menu-item__home-table .table-container {
  display: grid;
  grid-template-columns: repeat(10, 20px);
  grid-template-rows: repeat(8, 20px);
  gap: 2px;
  margin-bottom: 10px;
}

/* 表格单元格 */
.menu-item__home-table .table-cell {
  width: 20px;
  height: 20px;
  border: 1px solid #ddd;
  cursor: pointer;
  transition: all 0.1s ease;
}

/* 表格单元格悬停和高亮状态 */
.menu-item__home-table .table-cell:hover,
.menu-item__home-table .table-cell.highlighted {
  background: #409eff;
  border-color: #409eff;
}

/* 表格信息显示 */
.menu-item__home-table .table-info {
  text-align: center;
  font-size: 12px;
  color: #606266;
  padding: 5px 0;
}
