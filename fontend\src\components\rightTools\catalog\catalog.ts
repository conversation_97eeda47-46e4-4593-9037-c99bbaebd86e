import Editor, { ICatalogItem } from '../../editor'
import html from './catalog.html'
import './catalog.css'

export class Catalog {
  private dom: HTMLDivElement
  private instance: Editor
  private catalogInstance: HTMLDivElement | null = null
  private isVisible = false

  constructor(instance: Editor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    // 初始化组件
    this.init()
  }

  private init(): void {
    this.catalogInstance = this.dom.querySelector<HTMLDivElement>('.catalog__main')!

    // 绑定关闭按钮事件
    this.bindCloseEvent()

    // 初始状态隐藏
    this.hide()
  }

  private bindCloseEvent(): void {
    const closeBtn = this.dom.querySelector('.catalog__header__close i')
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hide()
      })
    }
  }

  public async updateCatalog(): Promise<void> {
    const catalog = await this.instance.command.getCatalog()
    if (catalog && catalog.length && this.catalogInstance) {
      this.catalogInstance.innerHTML = ''
      this.appendCatalog(this.catalogInstance, catalog)
    }
  }

  private appendCatalog(parent: HTMLDivElement, catalogItems: ICatalogItem[]): void {
    for (let c = 0; c < catalogItems.length; c++) {
      const catalogItem = catalogItems[c]
      const catalogItemDom = document.createElement('div')
      catalogItemDom.classList.add('catalog-item')

      // 渲染目录项内容
      const catalogItemContentDom = document.createElement('div')
      catalogItemContentDom.classList.add('catalog-item__content')
      const catalogItemContentSpanDom = document.createElement('span')
      catalogItemContentSpanDom.innerText = catalogItem.name
      catalogItemContentDom.append(catalogItemContentSpanDom)

      // 绑定点击定位事件
      catalogItemContentDom.onclick = () => {
        this.instance.command.executeLocationCatalog(catalogItem.id)
      }

      catalogItemDom.append(catalogItemContentDom)

      // 递归处理子目录
      if (catalogItem.subCatalog && catalogItem.subCatalog.length) {
        this.appendCatalog(catalogItemDom, catalogItem.subCatalog)
      }

      // 追加到父容器
      parent.append(catalogItemDom)
    }
  }

  public show(): void {
    this.isVisible = true
    this.dom.style.display = 'block'
    // 更新目录内容
    this.updateCatalog()
    console.log('目录已显示')
  }

  public hide(): void {
    this.isVisible = false
    this.dom.style.display = 'none'

  }

  public toggle(): void {

    if (this.isVisible) {
      this.hide()
    } else {
      this.show()
    }
  }

  public isShown(): boolean {
    return this.isVisible
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}