# 字体样式配置功能说明

## 功能概述

本功能实现了将顶部菜单栏中开始菜单和段落菜单的文本样式参数分离到独立配置文件中，支持动态加载和更新样式配置。

## 文件结构

```
fontend/src/stylesConfig/
├── fontPrographSet.json          # 样式配置文件
├── FontStyleConfigManager.ts     # 配置管理器
├── index.ts                      # 配置模块导出
├── test-config.ts               # 测试功能
└── README.md                    # 说明文档
```

## 配置文件格式

`fontPrographSet.json` 包含以下样式配置：

- **normal**: 正文样式
- **title1-title6**: 标题1-6样式

每种样式包含以下参数：
- `name`: 样式名称
- `font`: 字体名称
- `size`: 字体大小
- `color`: 字体颜色
- `bold`: 是否加粗
- `italic`: 是否斜体
- `underline`: 是否下划线
- `strikeout`: 是否删除线
- `lineHeight`: 行高
- `letterSpacing`: 字符间距
- `textAlign`: 文本对齐方式
- `marginTop`: 上边距
- `marginBottom`: 下边距

## 使用方法

### 1. 修改配置文件

直接编辑 `fontPrographSet.json` 文件，修改所需的样式参数。

### 2. 应用配置

在右侧工具栏的排版页面中，点击"应用"按钮，系统会：
1. 重新加载配置文件
2. 更新所有文本样式参数
3. 刷新编辑器显示

### 3. 编程方式使用

```typescript
import { fontStyleConfigManager } from './stylesConfig'

// 获取正文样式
const normalStyle = fontStyleConfigManager.getNormalStyle()

// 获取标题样式
const title1Style = fontStyleConfigManager.getTitleStyle(TitleLevel.FIRST)

// 重新加载配置
await fontStyleConfigManager.reloadConfig()
```

## 功能特点

1. **动态加载**: 支持运行时重新加载配置文件
2. **向后兼容**: 保持与原有代码的兼容性
3. **错误处理**: 配置加载失败时自动使用默认配置
4. **类型安全**: 完整的TypeScript类型定义
5. **单例模式**: 确保配置管理器的唯一性

## 测试功能

在浏览器控制台中运行以下命令测试功能：

```javascript
testStylesConfig()
```

## 注意事项

1. 配置文件路径为 `/src/stylesConfig/fontPrographSet.json`
2. 修改配置后需要点击"应用"按钮才能生效
3. 配置加载失败时会自动使用默认配置
4. 建议在修改配置前备份原文件

## 扩展说明

如需添加新的样式参数或样式类型，请：
1. 更新 `ITextStyle` 接口定义
2. 修改配置文件结构
3. 更新相关的应用逻辑
