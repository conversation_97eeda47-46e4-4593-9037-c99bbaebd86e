<!-- Ribbon菜单布局 -->
<div class="ribbon-menu">
  <!-- 选项卡标题栏 -->
  <div class="ribbon-tabs">
    <div class="ribbon-tab active" data-tab="home">开始</div>
    <div class="ribbon-tab" data-tab="font">字体</div>
    <div class="ribbon-tab" data-tab="paragraph">段落</div>
    <div class="ribbon-tab" data-tab="insert">插入</div>
    <div class="ribbon-tab" data-tab="layout">布局</div>
    <div class="ribbon-tab" data-tab="review">审阅</div>
    <div class="ribbon-tab" data-tab="view">视图</div>
  </div>

  <!-- 选项卡内容区域 -->
  <div class="ribbon-content">
    <!-- 开始选项卡 -->
    <div class="ribbon-panel active" data-panel="home">
      <!-- 剪贴板组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__undo"></div>
              <div class="menu-item__redo"></div>
              <div class="menu-item__painter"></div>
              <div class="menu-item__format"></div>
              <div class="menu-item__home-print"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 标题样式组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__home-normal-text"></div>
              <div class="menu-item__home-title1"></div>
              <div class="menu-item__home-title2"></div>
              <div class="menu-item__home-title3"></div>
              <div class="menu-item__home-title4"></div>
              <div class="menu-item__home-title5"></div>
              <div class="menu-item__home-title6"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 字体组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__home-font"></div>
              <div class="menu-item__home-font-size"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 插入组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__home-table"></div>
              <div class="menu-item__home-catalog"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 页面设置组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__home-margin"></div>
              <div class="menu-item__home-orientation"></div>
              <div class="menu-item__home-line-break"></div>
            </div>
          </div>
        </div>
      </div>


    </div>

    <!-- 字体选项卡 -->
    <div class="ribbon-panel" data-panel="font">
      <!-- 字体选择组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__new-font"></div>
              <div class="menu-item__new-font-size"></div>
              <div class="menu-item__size-add"></div>
              <div class="menu-item__size-minus"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 字体样式组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__bold"></div>
              <div class="menu-item__italic"></div>
              <div class="menu-item__underline"></div>
              <div class="menu-item__strikeout"></div>
              <div class="menu-item__superscript"></div>
              <div class="menu-item__subscript"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 字体颜色组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__color"></div>
              <div class="menu-item__highlight"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 段落选项卡 -->
    <div class="ribbon-panel" data-panel="paragraph">
      <!-- 标题样式组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__normal-text"></div>
              <div class="menu-item__title1"></div>
              <div class="menu-item__title2"></div>
              <div class="menu-item__title3"></div>
              <div class="menu-item__title4"></div>
              <div class="menu-item__title5"></div>
              <div class="menu-item__title6"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 对齐方式组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__left"></div>
              <div class="menu-item__center"></div>
              <div class="menu-item__right"></div>
              <div class="menu-item__justify"></div>
              <div class="menu-item__alignment"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 段落格式组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__row-margin"></div>
              <div class="menu-item__list"></div>
              <div class="menu-item__line-break"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 插入选项卡 -->
    <div class="ribbon-panel" data-panel="insert">
      <!-- 表格组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__table"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 插图组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__image"></div>
              <div class="menu-item__latex"></div>
              <div class="menu-item__codeblock"></div>
              <div class="menu-item__block"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 链接组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__hyperlink"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 页面元素组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__separator"></div>
              <div class="menu-item__page-break"></div>
              <div class="menu-item__watermark"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 控件组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__control"></div>
              <div class="menu-item__checkbox"></div>
              <div class="menu-item__radio"></div>
              <div class="menu-item__date"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 布局选项卡 -->
    <div class="ribbon-panel" data-panel="layout">
      <!-- 页面设置组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__watermark"></div>
              <div class="paper-size">
                <i title="纸张类型"></i>
                <div class="options">
                  <ul>
                    <li data-paper-size="794*1123" class="active">A4</li>
                    <li data-paper-size="1593*2251">A2</li>
                    <li data-paper-size="1125*1593">A3</li>
                    <li data-paper-size="565*796">A5</li>
                    <li data-paper-size="412*488">5号信封</li>
                    <li data-paper-size="450*866">6号信封</li>
                    <li data-paper-size="609*862">7号信封</li>
                    <li data-paper-size="862*1221">9号信封</li>
                    <li data-paper-size="813*1266">法律用纸</li>
                    <li data-paper-size="813*1054">信纸</li>
                  </ul>
                </div>
              </div>
              <div class="paper-direction">
                <i title="纸张方向"></i>
                <div class="options">
                  <ul>
                    <li data-paper-direction="vertical" class="active">纵向</li>
                    <li data-paper-direction="horizontal">横向</li>
                  </ul>
                </div>
              </div>
              <div class="page-mode">
                <i title="页面模式(分页、连页)"></i>
                <div class="options">
                  <ul>
                    <li data-page-mode="paging" class="active">分页</li>
                    <li data-page-mode="continuity">连页</li>
                  </ul>
                </div>
              </div>
              <div class="paper-margin" title="页边距">
                <i></i>
              </div>
              <div class="fullscreen" title="全屏显示">
                <i></i>
              </div>
              <div class="editor-option" title="编辑器设置">
                <i></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 页面缩放组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="page-scale-minus" title="缩小(Ctrl+-)">
                <i></i>
              </div>
              <span class="page-scale-percentage" title="显示比例(点击可复原Ctrl+0)">100%</span>
              <div class="page-scale-add" title="放大(Ctrl+=)">
                <i></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审阅选项卡 -->
    <div class="ribbon-panel" data-panel="review">
      <!-- 校对组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__search" data-menu="search"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 批注组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__comment" data-menu="comment">
                <i></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 视图选项卡 -->
    <div class="ribbon-panel" data-panel="view">
      <!-- 文档视图组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__print" data-menu="print"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 编辑模式组 -->
      <div class="ribbon-group">
        <div class="ribbon-group-content">
          <div class="ribbon-single-row">
            <div class="menu-item">
              <div class="menu-item__view-edit-mode"></div>
              <div class="menu-item__view-design-mode"></div>
              <div class="menu-item__view-clean-mode"></div>
              <div class="menu-item__view-form-mode"></div>
              <div class="menu-item__view-readonly-mode"></div>
              <div class="menu-item__view-print-mode"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>