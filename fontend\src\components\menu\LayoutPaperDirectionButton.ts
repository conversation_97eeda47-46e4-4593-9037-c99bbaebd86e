import { CanvasEditor } from '../../editor'
import html from './LayoutPaperDirectionButton.html'

/**
 * 布局菜单纸张方向按钮组件
 * 用于在布局菜单中选择纸张方向
 */
export class LayoutPaperDirectionButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor
  private optionsElement: HTMLDivElement
  private isVisible = false

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.optionsElement = this.dom.querySelector('.options') as HTMLDivElement
    
    this.bindEvents()
    this.setupClickOutside()
  }

  /**
   * 绑定事件处理器
   */
  private bindEvents(): void {
    // 点击图标切换下拉菜单
    const iconElement = this.dom.querySelector('i')!
    iconElement.onclick = (e) => {
      e.stopPropagation()
      this.toggleOptions()
    }
    
    // 点击选项
    this.optionsElement.onclick = (e) => {
      e.stopPropagation()
      const target = e.target as HTMLElement
      
      if (target.tagName === 'LI') {
        const paperDirection = target.dataset.paperDirection!
        this.selectPaperDirection(paperDirection, target as HTMLLIElement)
      }
    }
  }

  /**
   * 设置点击外部关闭下拉菜单
   */
  private setupClickOutside(): void {
    document.addEventListener('click', (e) => {
      if (!this.dom.contains(e.target as Node)) {
        this.hideOptions()
      }
    })

    // 监听窗口大小变化，重新定位下拉框
    window.addEventListener('resize', () => {
      if (this.isVisible) {
        this.positionOptions()
      }
    })

    // 监听滚动事件，重新定位下拉框
    window.addEventListener('scroll', () => {
      if (this.isVisible) {
        this.positionOptions()
      }
    })
  }

  /**
   * 切换下拉选项显示状态
   */
  private toggleOptions(): void {
    if (this.isVisible) {
      this.hideOptions()
    } else {
      this.showOptions()
    }
  }

  /**
   * 显示下拉选项
   */
  private showOptions(): void {
    // 先显示下拉框（但可能位置不对）
    this.optionsElement.classList.add('visible')
    
    // 然后精确定位
    this.positionOptions()
    
    this.isVisible = true
    console.log('纸张方向选项已显示')
  }

  /**
   * 精确定位下拉选项
   */
  private positionOptions(): void {
    const iconElement = this.dom.querySelector('i')!
    const rect = iconElement.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    // 获取下拉框尺寸
    const optionsRect = this.optionsElement.getBoundingClientRect()
    const optionsWidth = optionsRect.width || 80
    const optionsHeight = optionsRect.height || 80
    
    // 默认位置：按钮正下方
    let left = rect.left
    let top = rect.bottom + 2
    
    // 水平边界检查
    if (left + optionsWidth > viewportWidth) {
      left = viewportWidth - optionsWidth - 10
    }
    if (left < 10) {
      left = 10
    }
    
    // 垂直边界检查
    if (top + optionsHeight > viewportHeight) {
      top = rect.top - optionsHeight - 2
    }
    if (top < 10) {
      top = 10
    }
    
    // 应用绝对定位
    this.optionsElement.style.position = 'fixed'
    this.optionsElement.style.left = left + 'px'
    this.optionsElement.style.top = top + 'px'
    this.optionsElement.style.zIndex = '9999'
    
    console.log(`纸张方向下拉框定位: left=${left}, top=${top}`)
  }

  /**
   * 隐藏下拉选项
   */
  private hideOptions(): void {
    this.optionsElement.classList.remove('visible')
    this.isVisible = false
  }

  /**
   * 选择纸张方向
   */
  private selectPaperDirection(paperDirection: string, liElement: HTMLLIElement): void {
    try {
      console.log(`设置纸张方向: ${paperDirection}`)
      
      // 执行纸张方向切换命令
      this.instance.command.executePaperDirection(paperDirection)
      
      // 更新选中状态
      this.updateActiveState(liElement)
      
      // 关闭下拉菜单
      this.hideOptions()
      
      console.log('纸张方向设置完成')
    } catch (error) {
      console.error('设置纸张方向失败:', error)
    }
  }

  /**
   * 更新选中状态
   */
  private updateActiveState(selectedElement: HTMLLIElement): void {
    // 移除所有选项的active状态
    this.optionsElement.querySelectorAll('li').forEach(li => {
      li.classList.remove('active')
    })
    
    // 添加当前选项的active状态
    selectedElement.classList.add('active')
  }

  /**
   * 设置纸张方向（外部调用）
   */
  public setPaperDirection(paperDirection: string): void {
    const targetLi = this.optionsElement.querySelector(`li[data-paper-direction="${paperDirection}"]`) as HTMLLIElement
    if (targetLi) {
      this.selectPaperDirection(paperDirection, targetLi)
    }
  }

  /**
   * 获取当前选中的纸张方向
   */
  public getCurrentPaperDirection(): string | null {
    const activeElement = this.optionsElement.querySelector('li.active') as HTMLLIElement
    return activeElement ? activeElement.dataset.paperDirection! : null
  }

  /**
   * 获取DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
}
