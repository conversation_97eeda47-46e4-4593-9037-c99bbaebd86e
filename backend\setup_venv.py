#!/usr/bin/env python
"""
虚拟环境设置脚本
自动创建虚拟环境并安装依赖
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 版本过低: {version.major}.{version.minor}")
        print("需要 Python 3.8 或更高版本")
        return False
    
    print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
    return True

def create_venv():
    """创建虚拟环境"""
    venv_path = Path(".venv")
    
    if venv_path.exists():
        print("⚠️  虚拟环境已存在")
        response = input("是否重新创建? (y/N): ").lower()
        if response == 'y':
            print("🗑️  删除现有虚拟环境...")
            import shutil
            shutil.rmtree(venv_path)
        else:
            return True
    
    return run_command("python -m venv .venv", "创建虚拟环境")

def get_activation_command():
    """获取激活命令"""
    system = platform.system().lower()
    if system == "windows":
        return ".venv\\Scripts\\activate"
    else:
        return "source .venv/bin/activate"

def install_dependencies():
    """安装依赖"""
    system = platform.system().lower()
    
    if system == "windows":
        pip_command = ".venv\\Scripts\\pip"
    else:
        pip_command = ".venv/bin/pip"
    
    # 升级 pip
    if not run_command(f"{pip_command} install --upgrade pip", "升级 pip"):
        return False
    
    # 安装依赖
    if not run_command(f"{pip_command} install -r requirements.txt", "安装项目依赖"):
        return False
    
    return True

def show_usage_instructions():
    """显示使用说明"""
    system = platform.system().lower()
    
    print("\n" + "="*50)
    print("🎉 虚拟环境设置完成！")
    print("="*50)
    
    if system == "windows":
        print("📝 Windows 使用说明:")
        print("   激活虚拟环境: activate_venv.bat")
        print("   或手动激活: .venv\\Scripts\\activate")
    else:
        print("📝 Linux/Mac 使用说明:")
        print("   激活虚拟环境: source activate_venv.sh")
        print("   或手动激活: source .venv/bin/activate")
    
    print("\n🚀 启动项目:")
    print("   1. 激活虚拟环境")
    print("   2. python start.py 8000")
    
    print("\n💡 常用命令:")
    print("   - deactivate: 退出虚拟环境")
    print("   - pip list: 查看已安装包")
    print("   - pip freeze: 导出依赖列表")
    
    print("\n📁 虚拟环境位置: .venv/")

def main():
    """主函数"""
    print("🐍 Django 后端虚拟环境设置工具")
    print("="*40)
    
    # 检查当前目录
    if not Path("manage.py").exists():
        print("❌ 请在 backend 目录下运行此脚本")
        sys.exit(1)
    
    # 检查 Python 版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建虚拟环境
    if not create_venv():
        print("❌ 虚拟环境创建失败")
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        sys.exit(1)
    
    # 显示使用说明
    show_usage_instructions()

if __name__ == "__main__":
    main()
