# Django
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
# db.sqlite3
# db.sqlite3-journal

# # 环境变量
# .env
# .env.local
# .env.production

# 媒体文件
media/

# 静态文件
staticfiles/
static/

# 虚拟环境
# .venv/
# venv/
# env/
# ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db

# Python
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 测试
.coverage
.pytest_cache/
htmlcov/

# 缓存
.cache/

# 临时文件
*.tmp
*.bak
