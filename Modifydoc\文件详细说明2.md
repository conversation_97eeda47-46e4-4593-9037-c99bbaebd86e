# Canvas Editor 内容获取和设置指南

## 📋 概述

Canvas Editor 提供了完整的内容获取和设置API，支持多种数据格式的转换和处理，包括原生数据格式、HTML、纯文本等。本文档详细介绍如何使用这些API进行内容的读取、写入和转换。

## 🎯 数据结构概览

### 核心数据接口

```typescript
// 编辑器数据结构
interface IEditorData {
  header?: IElement[]  // 页眉内容
  main: IElement[]     // 正文内容
  footer?: IElement[]  // 页脚内容
}

// 编辑器结果结构
interface IEditorResult {
  version: string           // 编辑器版本
  data: IEditorData        // 文档数据
  options: IEditorOption   // 编辑器配置
}

// 元素基础结构
interface IElement {
  id?: string              // 元素唯一标识
  type?: ElementType       // 元素类型
  value: string           // 元素值
  font?: string           // 字体
  size?: number           // 字号
  bold?: boolean          // 加粗
  italic?: boolean        // 斜体
  color?: string          // 颜色
  highlight?: string      // 高亮
  underline?: boolean     // 下划线
  strikeout?: boolean     // 删除线
  rowFlex?: RowFlex       // 行对齐
  rowMargin?: number      // 行间距
  // ...更多属性
}
```

### 数据格式层次

```
┌─────────────────────────────────────────────────────────────┐
│                    数据格式转换层次图                        │
├─────────────────────────────────────────────────────────────┤
│  原生格式 (Native Format)                                   │
│  ├── IEditorResult (完整结果)                               │
│  ├── IEditorData (数据部分)                                 │
│  └── IElement[] (元素数组)                                  │
├─────────────────────────────────────────────────────────────┤
│  HTML格式 (HTML Format)                                     │
│  ├── 完整HTML结构                                           │
│  ├── 分区HTML (header/main/footer)                         │
│  └── DOM元素转换                                            │
├─────────────────────────────────────────────────────────────┤
│  文本格式 (Text Format)                                     │
│  ├── 纯文本内容                                             │
│  ├── 分区文本                                               │
│  └── 选区文本                                               │
├─────────────────────────────────────────────────────────────┤
│  其他格式 (Other Formats)                                   │
│  ├── 图片格式 (Base64)                                      │
│  ├── 剪贴板格式                                             │
│  └── 控件数据格式                                           │
└─────────────────────────────────────────────────────────────┘
```

## 📖 内容获取API

### 1. 获取完整文档数据

#### getValue() - 同步获取

```typescript
// 基础用法
const result = instance.command.getValue()
console.log('编辑器版本:', result.version)
console.log('文档数据:', result.data)
console.log('编辑器配置:', result.options)

// 带选项的获取
const result = instance.command.getValue({
  extraPickAttrs: ['id', 'conceptId'] // 额外保留的属性
})

// 结果结构
interface IEditorResult {
  version: string           // 当前编辑器版本
  data: {
    header?: IElement[]     // 页眉元素列表
    main: IElement[]        // 正文元素列表  
    footer?: IElement[]     // 页脚元素列表
  }
  options: IEditorOption    // 当前编辑器配置
}
```

#### getValueAsync() - 异步获取

```typescript
// 异步获取（适用于大文档）
const result = await instance.command.getValueAsync({
  extraPickAttrs: ['id', 'externalId']
})

// 处理结果
console.log('异步获取完成:', result.data.main.length, '个元素')

// 错误处理
try {
  const result = await instance.command.getValueAsync()
  // 处理成功结果
} catch (error) {
  console.error('获取数据失败:', error)
}
```

### 2. 获取HTML格式

#### getHTML() - 获取HTML内容

```typescript
// 获取HTML格式
const htmlResult = instance.command.getHTML()

console.log('页眉HTML:', htmlResult.header)
console.log('正文HTML:', htmlResult.main)  
console.log('页脚HTML:', htmlResult.footer)

// HTML结果结构
interface IEditorHTML {
  header: string    // 页眉HTML字符串
  main: string      // 正文HTML字符串
  footer: string    // 页脚HTML字符串
}

// 使用示例
const { main } = instance.command.getHTML()
document.getElementById('preview').innerHTML = main
```

### 3. 获取纯文本

#### getText() - 获取文本内容

```typescript
// 获取纯文本
const textResult = instance.command.getText()

console.log('页眉文本:', textResult.header)
console.log('正文文本:', textResult.main)
console.log('页脚文本:', textResult.footer)

// 文本结果结构
interface IEditorText {
  header: string    // 页眉纯文本
  main: string      // 正文纯文本
  footer: string    // 页脚纯文本
}

// 获取选区文本
const selectedText = instance.command.getRangeText()
console.log('选中的文本:', selectedText)
```

### 4. 获取特定信息

#### 获取字数统计

```typescript
// 异步获取字数
const wordCount = await instance.command.getWordCount()
console.log('文档字数:', wordCount)

// 自定义字数统计
function customWordCount(text: string): number {
  // 中文字符计数
  const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length
  // 英文单词计数
  const englishWords = (text.match(/[a-zA-Z]+/g) || []).length
  return chineseChars + englishWords
}

const { main } = instance.command.getText()
const count = customWordCount(main)
console.log('自定义字数统计:', count)
```

#### 获取光标和选区信息

```typescript
// 获取光标位置
const cursorPosition = instance.command.getCursorPosition()
if (cursorPosition) {
  console.log('光标页码:', cursorPosition.pageNo)
  console.log('光标索引:', cursorPosition.index)
  console.log('光标坐标:', cursorPosition.left, cursorPosition.ascent)
}

// 获取选区范围
const range = instance.command.getRange()
console.log('选区开始:', range.startIndex)
console.log('选区结束:', range.endIndex)
console.log('是否跨行列:', range.isCrossRowCol)

// 获取选区文本
const rangeText = instance.command.getRangeText()
console.log('选区内容:', rangeText)
```

### 5. 获取图片数据

```typescript
// 获取页面图片
const imageOptions = {
  pixelRatio: 2,           // 像素比例
  mode: 'current'          // 当前页面或全部页面
}

const base64Images = await instance.command.getImage(imageOptions)
console.log('页面图片:', base64Images)

// 下载图片
function downloadImage(base64: string, filename: string) {
  const link = document.createElement('a')
  link.href = base64
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 下载当前页面为图片
base64Images.forEach((base64, index) => {
  downloadImage(base64, `page_${index + 1}.png`)
})
```

## 📝 内容设置API

### 1. 设置完整文档数据

#### setValue() - 设置原生数据

```typescript
// 基础设置
const newData = {
  header: [
    { value: '文档标题', size: 16, bold: true, rowFlex: RowFlex.CENTER }
  ],
  main: [
    { value: '这是正文内容', size: 14 },
    { value: '\n' },
    { value: '第二段内容', size: 14, color: '#333' }
  ],
  footer: [
    { value: 'canvas-editor', size: 12, color: '#666' }
  ]
}

instance.command.setValue(newData)

// 带选项的设置
instance.command.setValue(newData, {
  isSetCursor: true  // 设置后将光标定位到末尾
})
```

#### 增量设置数据

```typescript
// 只设置正文，保留页眉页脚
instance.command.setValue({
  main: [
    { value: '新的正文内容', size: 14 }
  ]
})

// 只设置页眉
instance.command.setValue({
  header: [
    { value: '新的页眉', size: 12, rowFlex: RowFlex.CENTER }
  ]
})

// 清空特定区域（设置为空数组）
instance.command.setValue({
  footer: []  // 清空页脚
})
```

### 2. 设置HTML内容

#### setHTML() - 从HTML设置

```typescript
// 设置HTML内容
const htmlData = {
  header: '<div style="text-align: center; font-size: 16px;"><strong>HTML标题</strong></div>',
  main: `
    <p>这是从HTML转换的内容</p>
    <p style="color: red; font-size: 18px;">带样式的段落</p>
    <ul>
      <li>列表项1</li>
      <li>列表项2</li>
    </ul>
  `,
  footer: '<div style="font-size: 12px; color: #666;">HTML页脚</div>'
}

instance.command.setHTML(htmlData)

// 只设置正文HTML
instance.command.setHTML({
  main: '<h1>HTML标题</h1><p>HTML段落内容</p>'
})
```

### 3. 插入内容

#### 插入元素列表

```typescript
// 在当前位置插入元素
const elementsToInsert = [
  { value: '插入的文本', size: 14, color: 'blue' },
  { value: '\n' },
  { value: '第二行文本', size: 12, italic: true }
]

instance.command.executeInsertElementList(elementsToInsert)

// 插入特殊元素
instance.command.executeInsertElementList([
  {
    type: ElementType.IMAGE,
    value: '',
    width: 200,
    height: 150,
    imgValue: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'
  }
])
```

#### 插入表格

```typescript
// 插入表格
instance.command.executeInsertTable({
  row: 3,      // 行数
  col: 4       // 列数
})

// 插入带样式的表格
const tableElement = {
  type: ElementType.TABLE,
  trList: [
    {
      tdList: [
        { value: [{ value: '表头1' }] },
        { value: [{ value: '表头2' }] }
      ]
    },
    {
      tdList: [
        { value: [{ value: '数据1' }] },
        { value: [{ value: '数据2' }] }
      ]
    }
  ],
  borderType: TableBorder.ALL,
  borderColor: '#000'
}

instance.command.executeInsertElementList([tableElement])
```

### 4. 控件数据操作

#### 获取控件值

```typescript
// 获取单个控件值
const controlValue = instance.command.getControlValue({
  conceptId: 'patient-name'  // 控件概念ID
})

if (controlValue) {
  console.log('控件类型:', controlValue.type)
  console.log('控件值:', controlValue.value)
  console.log('控件配置:', controlValue.control)
}

// 批量获取控件值
const allControls = instance.command.getControlValueList()
allControls.forEach(control => {
  console.log(`控件 ${control.conceptId}: ${control.value}`)
})
```

#### 设置控件值

```typescript
// 设置单个控件值
instance.command.setControlValue({
  conceptId: 'patient-name',
  value: '张三'
})

// 批量设置控件值
instance.command.setControlValueList([
  { conceptId: 'patient-name', value: '张三' },
  { conceptId: 'patient-age', value: '30' },
  { conceptId: 'patient-gender', value: '男' }
])

// 设置控件扩展属性
instance.command.setControlExtension({
  conceptId: 'patient-name',
  extension: {
    required: true,
    maxLength: 50,
    placeholder: '请输入患者姓名'
  }
})

// 设置控件属性
instance.command.setControlProperties({
  conceptId: 'patient-name',
  properties: {
    disabled: false,
    hide: false,
    border: true
  }
})
```

## 🔄 数据转换和处理

### 1. 元素列表转换

#### 压缩和解压元素列表

```typescript
import { zipElementList, formatElementList } from '@hufe921/canvas-editor'

// 压缩元素列表（移除冗余属性）
const compressedElements = zipElementList(originalElements, {
  extraPickAttrs: ['id', 'conceptId'],  // 保留的额外属性
  isClassifyArea: true                  // 是否分类区域
})

// 格式化元素列表（补全缺失属性）
formatElementList(elements, {
  editorOptions: instance.command.getOptions(),
  isForceCompensation: true  // 强制补偿缺失属性
})
```

#### HTML与元素列表互转

```typescript
import {
  createDomFromElementList,
  getElementListByHTML
} from '@hufe921/canvas-editor'

// 元素列表转HTML
const elements = [
  { value: 'Hello', bold: true },
  { value: ' World', italic: true }
]

const htmlDom = createDomFromElementList(elements, {
  defaultFont: 'Microsoft YaHei',
  defaultSize: 14
})

console.log('生成的HTML:', htmlDom.innerHTML)

// HTML转元素列表
const htmlString = '<p><strong>粗体文本</strong><em>斜体文本</em></p>'
const convertedElements = getElementListByHTML(htmlString, {
  innerWidth: 800  // 页面内宽
})

console.log('转换的元素:', convertedElements)
```

#### 文本与元素列表互转

```typescript
import { getTextFromElementList, splitText } from '@hufe921/canvas-editor'

// 元素列表转文本
const elements = [
  { value: '第一段' },
  { value: '\n' },
  { value: '第二段' }
]

const text = getTextFromElementList(elements)
console.log('提取的文本:', text)

// 文本拆分
const longText = '这是一段很长的文本内容'
const splitChars = splitText(longText)
console.log('拆分的字符:', splitChars)

// 文本转元素列表
function textToElements(text: string): IElement[] {
  return splitText(text).map(char => ({
    value: char,
    size: 14,
    font: 'Microsoft YaHei'
  }))
}
```

### 2. 剪贴板操作

#### 剪贴板数据处理

```typescript
// 复制选中内容到剪贴板
instance.command.executeCopy()

// 复制特定选项
instance.command.executeCopy({
  isPlainText: true  // 只复制纯文本
})

// 粘贴剪贴板内容
instance.command.executePaste()

// 自定义剪贴板处理
import {
  setClipboardData,
  getClipboardData,
  writeClipboardItem
} from '@hufe921/canvas-editor'

// 设置自定义剪贴板数据
setClipboardData({
  text: '纯文本内容',
  elementList: [
    { value: '富文本内容', bold: true }
  ]
})

// 获取剪贴板数据
const clipboardData = getClipboardData()
if (clipboardData) {
  console.log('剪贴板文本:', clipboardData.text)
  console.log('剪贴板元素:', clipboardData.elementList)
}
```

### 3. 数据验证和清理

#### 数据完整性检查

```typescript
// 验证元素列表完整性
function validateElementList(elements: IElement[]): boolean {
  return elements.every(element => {
    // 检查必需属性
    if (!element.value && element.value !== '') {
      console.error('元素缺少value属性:', element)
      return false
    }

    // 检查类型有效性
    if (element.type && !Object.values(ElementType).includes(element.type)) {
      console.error('无效的元素类型:', element.type)
      return false
    }

    return true
  })
}

// 清理无效数据
function cleanElementList(elements: IElement[]): IElement[] {
  return elements.filter(element => {
    // 移除空值元素（除了换行符）
    if (!element.value && element.value !== '\n') {
      return false
    }

    // 移除无效属性
    const cleanElement = { ...element }
    Object.keys(cleanElement).forEach(key => {
      const value = cleanElement[key as keyof IElement]
      if (value === undefined || value === null) {
        delete cleanElement[key as keyof IElement]
      }
    })

    return true
  })
}
```

## 📊 实际应用示例

### 1. 文档模板系统

```typescript
// 定义文档模板
interface DocumentTemplate {
  id: string
  name: string
  data: IEditorData
  variables: string[]  // 模板变量
}

class DocumentTemplateManager {
  private templates: Map<string, DocumentTemplate> = new Map()

  // 注册模板
  registerTemplate(template: DocumentTemplate) {
    this.templates.set(template.id, template)
  }

  // 应用模板
  applyTemplate(templateId: string, variables: Record<string, string>) {
    const template = this.templates.get(templateId)
    if (!template) {
      throw new Error(`模板不存在: ${templateId}`)
    }

    // 克隆模板数据
    const data = JSON.parse(JSON.stringify(template.data))

    // 替换变量
    this.replaceVariables(data, variables)

    return data
  }

  private replaceVariables(data: IEditorData, variables: Record<string, string>) {
    const processElements = (elements: IElement[]) => {
      elements.forEach(element => {
        if (element.value) {
          // 替换变量 {{variableName}}
          element.value = element.value.replace(/\{\{(\w+)\}\}/g, (match, varName) => {
            return variables[varName] || match
          })
        }

        // 处理嵌套元素
        if (element.valueList) {
          processElements(element.valueList)
        }
        if (element.trList) {
          element.trList.forEach(tr => {
            tr.tdList.forEach(td => {
              if (td.value) {
                processElements(td.value)
              }
            })
          })
        }
      })
    }

    if (data.header) processElements(data.header)
    if (data.main) processElements(data.main)
    if (data.footer) processElements(data.footer)
  }
}

// 使用示例
const templateManager = new DocumentTemplateManager()

// 注册模板
templateManager.registerTemplate({
  id: 'medical-report',
  name: '医疗报告模板',
  data: {
    header: [
      { value: '{{hospitalName}}', size: 16, bold: true, rowFlex: RowFlex.CENTER }
    ],
    main: [
      { value: '患者姓名: {{patientName}}', size: 14 },
      { value: '\n' },
      { value: '诊断结果: {{diagnosis}}', size: 14 },
      { value: '\n' },
      { value: '医生签名: {{doctorName}}', size: 14 }
    ],
    footer: [
      { value: '报告日期: {{reportDate}}', size: 12 }
    ]
  },
  variables: ['hospitalName', 'patientName', 'diagnosis', 'doctorName', 'reportDate']
})

// 应用模板
const documentData = templateManager.applyTemplate('medical-report', {
  hospitalName: '第一人民医院',
  patientName: '张三',
  diagnosis: '健康',
  doctorName: '李医生',
  reportDate: '2024-01-15'
})

// 设置到编辑器
instance.command.setValue(documentData)
```

### 2. 自动保存系统

```typescript
class AutoSaveManager {
  private editor: any
  private saveInterval: number
  private lastSaveTime: number = 0
  private saveTimer: NodeJS.Timeout | null = null

  constructor(editor: any, interval: number = 30000) { // 30秒自动保存
    this.editor = editor
    this.saveInterval = interval
    this.setupAutoSave()
  }

  private setupAutoSave() {
    // 监听内容变化
    this.editor.listener.contentChange = () => {
      this.scheduleAutoSave()
    }

    // 页面卸载前保存
    window.addEventListener('beforeunload', () => {
      this.saveNow()
    })
  }

  private scheduleAutoSave() {
    // 清除之前的定时器
    if (this.saveTimer) {
      clearTimeout(this.saveTimer)
    }

    // 设置新的定时器
    this.saveTimer = setTimeout(() => {
      this.saveNow()
    }, this.saveInterval)
  }

  private async saveNow() {
    try {
      const data = this.editor.command.getValue()
      const saveKey = `autosave_${Date.now()}`

      // 保存到本地存储
      localStorage.setItem('editor_autosave', JSON.stringify({
        timestamp: Date.now(),
        data: data
      }))

      // 可选：保存到服务器
      await this.saveToServer(data)

      this.lastSaveTime = Date.now()
      console.log('自动保存完成:', new Date().toLocaleTimeString())

    } catch (error) {
      console.error('自动保存失败:', error)
    }
  }

  private async saveToServer(data: IEditorResult) {
    // 实现服务器保存逻辑
    const response = await fetch('/api/documents/save', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      throw new Error('服务器保存失败')
    }
  }

  // 恢复自动保存的内容
  restoreAutoSave(): boolean {
    try {
      const saved = localStorage.getItem('editor_autosave')
      if (saved) {
        const { timestamp, data } = JSON.parse(saved)

        // 检查保存时间（24小时内有效）
        if (Date.now() - timestamp < 24 * 60 * 60 * 1000) {
          this.editor.command.setValue(data.data)
          console.log('已恢复自动保存的内容')
          return true
        }
      }
    } catch (error) {
      console.error('恢复自动保存失败:', error)
    }
    return false
  }

  // 清除自动保存
  clearAutoSave() {
    localStorage.removeItem('editor_autosave')
    if (this.saveTimer) {
      clearTimeout(this.saveTimer)
      this.saveTimer = null
    }
  }
}

// 使用示例
const autoSave = new AutoSaveManager(instance, 30000)

// 页面加载时尝试恢复
if (autoSave.restoreAutoSave()) {
  console.log('已恢复上次编辑的内容')
}
```

### 3. 数据导入导出系统

```typescript
class DataExportManager {
  private editor: any

  constructor(editor: any) {
    this.editor = editor
  }

  // 导出为JSON
  exportToJSON(): string {
    const data = this.editor.command.getValue()
    return JSON.stringify(data, null, 2)
  }

  // 导出为HTML
  exportToHTML(): string {
    const htmlData = this.editor.command.getHTML()
    const template = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出文档</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; line-height: 1.6; }
        .header { text-align: center; border-bottom: 1px solid #ccc; padding-bottom: 10px; }
        .main { margin: 20px 0; }
        .footer { text-align: center; border-top: 1px solid #ccc; padding-top: 10px; }
    </style>
</head>
<body>
    <div class="header">${htmlData.header}</div>
    <div class="main">${htmlData.main}</div>
    <div class="footer">${htmlData.footer}</div>
</body>
</html>`
    return template
  }

  // 导出为Markdown
  exportToMarkdown(): string {
    const textData = this.editor.command.getText()
    const elements = this.editor.command.getValue().data.main

    let markdown = ''
    let currentLevel = 0

    elements.forEach(element => {
      if (element.type === ElementType.TITLE) {
        const level = element.level || 1
        markdown += '#'.repeat(level) + ' ' + element.value + '\n\n'
      } else if (element.type === ElementType.LIST) {
        if (element.listType === ListType.UL) {
          markdown += '- ' + element.value + '\n'
        } else {
          markdown += '1. ' + element.value + '\n'
        }
      } else if (element.value === '\n') {
        markdown += '\n'
      } else {
        let text = element.value
        if (element.bold) text = `**${text}**`
        if (element.italic) text = `*${text}*`
        if (element.strikeout) text = `~~${text}~~`
        markdown += text
      }
    })

    return markdown
  }

  // 导出为PDF（需要配合打印功能）
  async exportToPDF(): Promise<void> {
    // 切换到打印模式
    const originalMode = this.editor.command.getOptions().mode
    this.editor.command.executeMode(EditorMode.PRINT)

    // 等待渲染完成
    await new Promise(resolve => setTimeout(resolve, 100))

    // 触发打印
    window.print()

    // 恢复原模式
    this.editor.command.executeMode(originalMode)
  }

  // 下载文件
  downloadFile(content: string, filename: string, mimeType: string = 'text/plain') {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

class DataImportManager {
  private editor: any

  constructor(editor: any) {
    this.editor = editor
  }

  // 从JSON导入
  importFromJSON(jsonString: string): boolean {
    try {
      const data = JSON.parse(jsonString)

      // 验证数据格式
      if (this.validateEditorData(data)) {
        this.editor.command.setValue(data.data || data)
        return true
      } else {
        throw new Error('无效的JSON数据格式')
      }
    } catch (error) {
      console.error('JSON导入失败:', error)
      return false
    }
  }

  // 从HTML导入
  importFromHTML(htmlString: string): boolean {
    try {
      // 解析HTML结构
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlString, 'text/html')

      const header = doc.querySelector('.header')?.innerHTML || ''
      const main = doc.querySelector('.main')?.innerHTML || doc.body.innerHTML
      const footer = doc.querySelector('.footer')?.innerHTML || ''

      this.editor.command.setHTML({
        header,
        main,
        footer
      })

      return true
    } catch (error) {
      console.error('HTML导入失败:', error)
      return false
    }
  }

  // 从文件导入
  async importFromFile(file: File): Promise<boolean> {
    return new Promise((resolve) => {
      const reader = new FileReader()

      reader.onload = (e) => {
        const content = e.target?.result as string
        let success = false

        if (file.type === 'application/json' || file.name.endsWith('.json')) {
          success = this.importFromJSON(content)
        } else if (file.type === 'text/html' || file.name.endsWith('.html')) {
          success = this.importFromHTML(content)
        } else if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
          success = this.importFromText(content)
        }

        resolve(success)
      }

      reader.onerror = () => resolve(false)
      reader.readAsText(file)
    })
  }

  // 从纯文本导入
  importFromText(text: string): boolean {
    try {
      const lines = text.split('\n')
      const elements: IElement[] = []

      lines.forEach((line, index) => {
        if (line.trim()) {
          elements.push({ value: line, size: 14 })
        }
        if (index < lines.length - 1) {
          elements.push({ value: '\n' })
        }
      })

      this.editor.command.setValue({ main: elements })
      return true
    } catch (error) {
      console.error('文本导入失败:', error)
      return false
    }
  }

  private validateEditorData(data: any): boolean {
    // 检查是否有main属性或者是元素数组
    if (data.data && data.data.main) {
      return Array.isArray(data.data.main)
    } else if (data.main) {
      return Array.isArray(data.main)
    } else if (Array.isArray(data)) {
      return true
    }
    return false
  }
}

// 使用示例
const exportManager = new DataExportManager(instance)
const importManager = new DataImportManager(instance)

// 导出功能
document.getElementById('exportJSON')?.addEventListener('click', () => {
  const json = exportManager.exportToJSON()
  exportManager.downloadFile(json, 'document.json', 'application/json')
})

document.getElementById('exportHTML')?.addEventListener('click', () => {
  const html = exportManager.exportToHTML()
  exportManager.downloadFile(html, 'document.html', 'text/html')
})

document.getElementById('exportMarkdown')?.addEventListener('click', () => {
  const markdown = exportManager.exportToMarkdown()
  exportManager.downloadFile(markdown, 'document.md', 'text/markdown')
})

// 导入功能
document.getElementById('importFile')?.addEventListener('change', async (e) => {
  const file = (e.target as HTMLInputElement).files?.[0]
  if (file) {
    const success = await importManager.importFromFile(file)
    if (success) {
      console.log('文件导入成功')
    } else {
      console.error('文件导入失败')
    }
  }
})
```

## 🎯 最佳实践和注意事项

### 1. 性能优化

```typescript
// 大文档处理优化
class PerformanceOptimizer {
  private editor: any

  constructor(editor: any) {
    this.editor = editor
  }

  // 分批处理大量数据
  async setBigDataInBatches(elements: IElement[], batchSize: number = 1000) {
    const batches = this.chunkArray(elements, batchSize)

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i]

      if (i === 0) {
        // 第一批直接设置
        this.editor.command.setValue({ main: batch })
      } else {
        // 后续批次追加
        this.editor.command.executeInsertElementList(batch)
      }

      // 让出控制权，避免阻塞UI
      await new Promise(resolve => setTimeout(resolve, 0))

      // 显示进度
      const progress = Math.round(((i + 1) / batches.length) * 100)
      console.log(`处理进度: ${progress}%`)
    }
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  // 异步获取大文档数据
  async getBigDataAsync(): Promise<IEditorResult> {
    return this.editor.command.getValueAsync()
  }

  // 内存使用监控
  monitorMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      console.log('内存使用情况:', {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
      })
    }
  }
}
```

### 2. 错误处理和恢复

```typescript
class ErrorHandler {
  private editor: any
  private backupData: IEditorResult | null = null

  constructor(editor: any) {
    this.editor = editor
    this.setupErrorHandling()
  }

  private setupErrorHandling() {
    // 全局错误捕获
    window.addEventListener('error', (event) => {
      console.error('全局错误:', event.error)
      this.handleError(event.error)
    })

    // Promise错误捕获
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未处理的Promise错误:', event.reason)
      this.handleError(event.reason)
    })
  }

  // 创建数据备份
  createBackup() {
    try {
      this.backupData = this.editor.command.getValue()
      console.log('数据备份创建成功')
    } catch (error) {
      console.error('创建备份失败:', error)
    }
  }

  // 恢复数据
  restoreFromBackup(): boolean {
    if (this.backupData) {
      try {
        this.editor.command.setValue(this.backupData.data)
        console.log('数据恢复成功')
        return true
      } catch (error) {
        console.error('数据恢复失败:', error)
      }
    }
    return false
  }

  // 错误处理
  private handleError(error: any) {
    // 记录错误
    this.logError(error)

    // 尝试恢复
    if (this.isDataCorrupted()) {
      this.restoreFromBackup()
    }
  }

  private logError(error: any) {
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    // 发送错误报告到服务器
    fetch('/api/error-report', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(errorInfo)
    }).catch(console.error)
  }

  private isDataCorrupted(): boolean {
    try {
      const data = this.editor.command.getValue()
      return !data || !data.data || !Array.isArray(data.data.main)
    } catch {
      return true
    }
  }
}
```

### 3. 数据验证和清理

```typescript
class DataValidator {
  // 验证编辑器数据
  static validateEditorData(data: IEditorData): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // 检查main是否存在且为数组
    if (!data.main || !Array.isArray(data.main)) {
      errors.push('main字段必须是数组')
    }

    // 检查header
    if (data.header && !Array.isArray(data.header)) {
      errors.push('header字段必须是数组')
    }

    // 检查footer
    if (data.footer && !Array.isArray(data.footer)) {
      errors.push('footer字段必须是数组')
    }

    // 验证元素
    const allElements = [
      ...(data.header || []),
      ...data.main,
      ...(data.footer || [])
    ]

    allElements.forEach((element, index) => {
      const elementErrors = this.validateElement(element, index)
      errors.push(...elementErrors)
    })

    return {
      valid: errors.length === 0,
      errors
    }
  }

  // 验证单个元素
  static validateElement(element: IElement, index: number): string[] {
    const errors: string[] = []

    // 检查value字段
    if (element.value === undefined || element.value === null) {
      errors.push(`元素${index}: value字段不能为空`)
    }

    // 检查类型
    if (element.type && !Object.values(ElementType).includes(element.type)) {
      errors.push(`元素${index}: 无效的type值: ${element.type}`)
    }

    // 检查数值字段
    if (element.size !== undefined && (element.size <= 0 || element.size > 100)) {
      errors.push(`元素${index}: size值应在1-100之间`)
    }

    // 检查颜色格式
    if (element.color && !/^#[0-9A-Fa-f]{6}$/.test(element.color)) {
      errors.push(`元素${index}: 无效的颜色格式: ${element.color}`)
    }

    return errors
  }

  // 清理数据
  static cleanEditorData(data: IEditorData): IEditorData {
    const cleanData: IEditorData = {
      main: this.cleanElementList(data.main)
    }

    if (data.header) {
      cleanData.header = this.cleanElementList(data.header)
    }

    if (data.footer) {
      cleanData.footer = this.cleanElementList(data.footer)
    }

    return cleanData
  }

  // 清理元素列表
  static cleanElementList(elements: IElement[]): IElement[] {
    return elements
      .filter(element => element.value !== undefined && element.value !== null)
      .map(element => {
        const cleanElement: IElement = { value: element.value }

        // 只保留有效属性
        Object.keys(element).forEach(key => {
          const value = element[key as keyof IElement]
          if (value !== undefined && value !== null && key !== 'value') {
            (cleanElement as any)[key] = value
          }
        })

        return cleanElement
      })
  }
}

// 使用示例
const validator = new DataValidator()

// 验证数据
const data = instance.command.getValue().data
const validation = DataValidator.validateEditorData(data)

if (!validation.valid) {
  console.error('数据验证失败:', validation.errors)

  // 清理数据
  const cleanData = DataValidator.cleanEditorData(data)
  instance.command.setValue(cleanData)
}
```

## 📚 总结

Canvas Editor 提供了完整的内容获取和设置API体系，支持：

### 🎯 核心功能
- **📖 多格式获取**: 原生数据、HTML、纯文本、图片等
- **📝 灵活设置**: 完整替换、增量更新、插入操作
- **🔄 格式转换**: 各种数据格式间的无缝转换
- **🎮 控件操作**: 完整的控件数据管理

### 🛠️ 实用工具
- **📋 模板系统**: 支持变量替换的文档模板
- **💾 自动保存**: 智能的自动保存和恢复机制
- **📤 导入导出**: 多格式的文档导入导出
- **⚡ 性能优化**: 大文档的高效处理

### 🛡️ 质量保证
- **🔍 数据验证**: 完整的数据格式验证
- **🚨 错误处理**: 健壮的错误处理和恢复
- **🧹 数据清理**: 自动的数据清理和优化

这些功能为开发者提供了强大而灵活的内容管理能力，满足各种复杂的业务需求。
