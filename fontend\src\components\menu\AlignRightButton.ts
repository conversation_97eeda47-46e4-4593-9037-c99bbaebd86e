import { RowFlex } from '../../editor/dataset/enum/Row'
import { CanvasEditor } from '../../editor'
import html from './AlignRightButton.html'
import './AlignRightButton.css'

export class AlignRightButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    // 设置快捷键提示
    const isApple = /Mac|iPod|iPhone|iPad/.test(navigator.platform)
    this.dom.title = `右对齐(${isApple ? '⌘' : 'Ctrl'}+R)`
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executeRowFlex(RowFlex.RIGHT)
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 