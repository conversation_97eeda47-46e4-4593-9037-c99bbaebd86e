# Canvas Editor 开发文档

## 🚀 项目概述

Canvas Editor 是一个基于 HTML5 Canvas 的现代化富文本编辑器，提供了丰富的文档编辑功能，支持文本格式化、表格插入、图片处理、搜索替换等特性。

### 核心特性
- 🎨 **富文本编辑**: 支持字体、大小、颜色、加粗、斜体、下划线等格式化
- 📊 **表格支持**: 完整的表格创建、编辑、格式化功能
- 🔍 **搜索替换**: 强大的文本搜索和替换功能
- 📄 **多页面模式**: 支持分页和连页显示模式
- 🖼️ **图片处理**: 图片插入、调整、浮动布局
- 📐 **页面设置**: 纸张大小、方向、边距自定义
- 💾 **导入导出**: 支持多种格式的文档导入导出
- 🎯 **模块化架构**: 高度模块化的代码结构，易于维护和扩展

## 🛠️ 技术栈

### 核心技术
- **TypeScript**: 主要开发语言，提供类型安全
- **HTML5 Canvas**: 核心渲染引擎
- **Vite**: 现代化构建工具，快速开发体验
- **CSS3**: 样式和动画处理
- **Web APIs**: 文件处理、打印、全屏等浏览器API

### 开发工具
- **Node.js**: 开发环境
- **npm**: 包管理器
- **ESLint**: 代码检查
- **Git**: 版本控制

## 📁 项目结构

```
canvas-editor-main/
├── src/                          # 源代码目录
│   ├── main.ts                   # 应用入口文件
│   ├── style.css                 # 全局样式
│   ├── initialize/               # 初始化模块
│   │   ├── initialize.ts         # 核心初始化逻辑
│   │   ├── index.ts              # 统一导出
│   │   └── MAIN_REFACTOR.md      # 重构文档
│   ├── components/               # 组件库
│   │   ├── menu/                 # 顶部菜单组件
│   │   │   ├── menu-index.html   # 菜单HTML模板
│   │   │   ├── menu.css          # 菜单样式
│   │   │   ├── menu.ts           # 菜单逻辑
│   │   │   ├── index.ts          # 菜单初始化
│   │   │   ├── search.ts         # 搜索功能
│   │   │   ├── search.css        # 搜索样式
│   │   │   └── *.ts              # 各功能按钮
│   │   ├── footer/               # 底部状态栏组件
│   │   │   ├── *.ts              # 状态栏功能
│   │   │   └── *.css             # 状态栏样式
│   │   ├── dialog/               # 对话框组件
│   │   └── signature/            # 签名组件
│   ├── editor/                   # 编辑器核心
│   │   ├── core/                 # 核心功能
│   │   ├── interface/            # 类型定义
│   │   └── dataset/              # 数据模型
│   ├── assets/                   # 静态资源
│   │   └── images/               # 图标文件
│   ├── mock/                     # 模拟数据
│   └── utils/                    # 工具函数
├── docs/                         # 文档目录
├── package.json                  # 项目配置
├── tsconfig.json                 # TypeScript配置
├── vite.config.ts                # Vite配置
└── README.md                     # 项目说明
```

## 🚀 快速开始

### 环境要求
- Node.js >= 14.0.0
- npm >= 6.0.0

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```
访问：http://localhost:3001/canvas-editor/

### 构建生产版本
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

## 🏗️ 核心架构

### 初始化系统
项目采用模块化初始化架构，主要包含：

```typescript
// src/initialize/index.ts
export async function initializeCanvasEditor(): Promise<void> {
  // 1. 屏蔽浏览器默认快捷键
  blockBrowserZoomShortcuts()

  // 2. 创建编辑器实例
  const instance = createEditorInstance()

  // 3. 初始化各个系统
  await initMenuSystem(instance)
  await initFooterSystem(instance)
  initEditorModeSystem(instance)
  // ...更多初始化
}
```

### 主要模块

#### 1. 菜单系统 (src/components/menu/)
```typescript
// 菜单组件初始化
export class EditorMenu {
  constructor(config: MenuConfig)
  async init(): Promise<void>
  setMenuPermissions(permissions: Record<string, boolean>): void
}
```

#### 2. 搜索功能 (src/components/menu/search.ts)
```typescript
export class SearchComponent {
  private performSearch(keyword: string): void
  private searchNext(): void
  private searchPrevious(): void
  private performReplace(): void
}
```

#### 3. 底部状态栏 (src/components/footer/)
```typescript
export class EditorFooter {
  updatePageInfo(current: number, total: number, visible: number[]): void
  updateWordCount(count: number): void
  updatePageScale(scale: number): void
}
```

## 🎨 开发指南

### 添加新的菜单按钮

1. **创建按钮HTML模板**
```html
<!-- src/components/menu/new-button.html -->
<div class="menu-item__new-button" title="新功能">
  <i></i>
</div>
```

2. **添加按钮样式**
```css
/* src/components/menu/menu.css */
.menu-item__new-button > i {
  background-image: url('../../assets/images/new-button.svg');
}
```

3. **实现按钮逻辑**
```typescript
// src/components/menu/new-button.ts
export function initNewButton(instance: any) {
  const button = document.querySelector<HTMLDivElement>('.menu-item__new-button')
  if (button) {
    button.onclick = () => {
      // 按钮功能实现
      instance.command.executeNewFeature()
    }
  }
}
```

4. **注册到菜单系统**
```typescript
// src/components/menu/index.ts
import { initNewButton } from './new-button'

export function initAllMenuButtons(instance: any) {
  // ...其他按钮初始化
  initNewButton(instance)
}
```

### 添加新的编辑器命令

1. **扩展命令适配器**
```typescript
// src/editor/core/command/CommandAdapt.ts
export class CommandAdapt {
  public newFeature(payload: any) {
    // 实现新功能逻辑
    this.draw.render()
  }
}
```

2. **注册到命令系统**
```typescript
// src/editor/core/command/Command.ts
export class Command {
  public executeNewFeature: CommandAdapt['newFeature']

  constructor(adapt: CommandAdapt) {
    this.executeNewFeature = adapt.newFeature.bind(adapt)
  }
}
```

### 样式开发规范

#### CSS变量使用
```css
:root {
  --menu-height: 60px;
  --menu-bg: #f2f4f7;
  --menu-hover-bg: rgba(25, 55, 88, 0.04);
  --menu-active-bg: rgba(25, 55, 88, 0.08);
}
```

#### 组件样式结构
```css
/* 组件容器 */
.component-name {
  /* 基础样式 */
}

/* 组件子元素 */
.component-name__element {
  /* 子元素样式 */
}

/* 组件状态 */
.component-name:hover,
.component-name.active,
.component-name.disabled {
  /* 状态样式 */
}
```

### TypeScript 开发规范

#### 接口定义
```typescript
// 统一的接口命名规范
interface IComponentConfig {
  container: HTMLElement
  instance: any
  autoInit?: boolean
}

interface IComponentResult {
  success: boolean
  data?: any
  error?: string
}
```

#### 错误处理
```typescript
// 统一的错误处理模式
try {
  const result = await someAsyncOperation()
  console.log('操作成功:', result)
} catch (error) {
  console.error('操作失败:', error)
  // 用户友好的错误提示
  showErrorMessage('操作失败，请重试')
}
```

## 🧪 测试指南

### 功能测试检查清单

#### 菜单功能测试
- [ ] 所有菜单按钮正确显示图标
- [ ] 按钮hover效果正常
- [ ] 按钮点击功能正常
- [ ] 下拉菜单正确展开/收起
- [ ] 权限控制正常（只读模式）

#### 搜索功能测试
- [ ] 搜索面板正确弹出
- [ ] 搜索统计正确显示
- [ ] 前进/后退导航正常
- [ ] 替换功能正常
- [ ] 清除搜索正常

#### 编辑器功能测试
- [ ] 文本输入正常
- [ ] 格式化功能正常
- [ ] 撤销/重做正常
- [ ] 页面缩放正常
- [ ] 模式切换正常

### 性能测试
```bash
# 构建分析
npm run build -- --analyze

# 加载时间测试
# 首屏加载 < 2s
# 菜单响应 < 100ms
# 搜索响应 < 500ms
```

## 📦 构建和部署

### 构建配置
```typescript
// vite.config.ts
export default defineConfig({
  base: '/canvas-editor/',
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['typescript'],
          editor: ['./src/editor/index.ts']
        }
      }
    }
  }
})
```

### 部署到静态服务器
```bash
# 构建
npm run build

# 部署到 nginx
cp -r dist/* /var/www/html/canvas-editor/

# 配置 nginx
location /canvas-editor/ {
  try_files $uri $uri/ /canvas-editor/index.html;
}
```

### Docker 部署
```dockerfile
FROM node:16-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html/canvas-editor
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
```

## 🤝 贡献指南

### 代码提交规范
```bash
# 提交格式
<type>(<scope>): <subject>

# 示例
feat(menu): 添加页边距设置按钮
fix(search): 修复搜索统计显示问题
docs(readme): 更新开发文档
```

### 分支策略
- `main`: 主分支，稳定版本
- `develop`: 开发分支，最新功能
- `feature/*`: 功能分支
- `hotfix/*`: 紧急修复分支

### Pull Request 流程
1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request
5. 代码审查
6. 合并到主分支

## 🐛 常见问题

### Q: 菜单按钮不显示图标？
A: 检查图标文件路径和CSS样式是否正确，确保SVG文件存在于`src/assets/images/`目录。

### Q: 搜索功能没有统计？
A: 确保调用了`getSearchNavigateInfo()`方法获取搜索统计信息。

### Q: 开发服务器启动失败？
A: 检查Node.js版本是否 >= 14，端口是否被占用，依赖是否正确安装。

### Q: 构建失败？
A: 检查TypeScript类型错误，清除`node_modules`和`dist`目录后重新安装依赖。

## 📖 API 文档

### 编辑器实例方法
```typescript
// 获取编辑器内容
instance.command.getValue()

// 设置编辑器内容
instance.command.setValue(data)

// 执行搜索
instance.command.executeSearch(keyword)

// 获取搜索统计
instance.command.getSearchNavigateInfo()

// 设置页边距
instance.command.executeSetPaperMargin([top, right, bottom, left])
```

### 事件监听
```typescript
// 内容变化
instance.listener.contentChange = () => {
  console.log('内容已更改')
}

// 页面切换
instance.listener.intersectionPageNoChange = (pageNo) => {
  console.log('当前页码:', pageNo)
}
```

## 📊 项目统计

- **代码行数**: ~15,000+ 行
- **组件数量**: 50+ 个
- **功能模块**: 10+ 个
- **图标资源**: 60+ 个
- **文档页面**: 20+ 页

## 🔄 更新日志

### v0.9.110 (2025-06-02)
- ✅ 完成菜单系统模块化重构
- ✅ 修复搜索功能统计显示问题
- ✅ 添加顶部菜单页边距设置按钮
- ✅ 优化纸张类型弹出框方向
- ✅ 完善初始化系统架构

### 未来规划
- 🔄 移动端适配优化
- 🔄 协同编辑功能
- 🔄 插件系统架构
- 🔄 云存储集成
- 🔄 多语言国际化

## 📞 联系方式

- **项目仓库**: [GitHub](https://github.com/hufe921/canvas-editor)
- **问题反馈**: [Issues](https://github.com/hufe921/canvas-editor/issues)
- **开发文档**: [Documentation](https://hufe.club/canvas-editor-docs/)

---

**Made with ❤️ by Canvas Editor Team**