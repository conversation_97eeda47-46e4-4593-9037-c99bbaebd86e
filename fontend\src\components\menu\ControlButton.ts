import { CanvasEditor } from '../../editor'
import { ElementType, ControlType } from '../../editor'
import { Dialog } from '../dialog/Dialog'
import html from './ControlButton.html'
import './ControlButton.css'

export class ControlButton {
  private dom: HTMLDivElement
  private controlDom: HTMLDivElement
  private controlOptionDom: HTMLDivElement
  private instance: CanvasEditor
  private documentClickHandler: (e: MouseEvent) => void

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.controlDom = this.dom
    this.controlOptionDom = this.dom.querySelector<HTMLDivElement>('.options')!
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.controlDom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 切换显示状态
      const isVisible = this.controlOptionDom.classList.contains('visible')

      // 先隐藏所有其他的下拉框
      this.hideAllDropdowns()

      if (!isVisible) {
        // 显示当前下拉框并定位
        this.showDropdown()
      }
    }
    
    this.controlOptionDom.onmousedown = (evt) => {
      evt.stopPropagation() // 阻止事件冒泡
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        this.hideDropdown()
        const type = <ControlType>li.dataset.control

        switch (type) {
          case ControlType.TEXT:
            this.openTextControlDialog(type)
            break
          case ControlType.SELECT:
            this.openSelectControlDialog(type)
            break
          case ControlType.CHECKBOX:
            this.openCheckboxControlDialog(type)
            break
          case ControlType.RADIO:
            this.openRadioControlDialog(type)
            break
          case ControlType.DATE:
            this.openDateControlDialog(type)
            break
          case ControlType.NUMBER:
            this.openNumberControlDialog(type)
            break
          default:
            break
        }
      }
    }
    
    // 点击外部关闭下拉框
    this.documentClickHandler = (e) => {
      const target = e.target as Node
      if (!this.dom.contains(target) && !this.controlOptionDom.contains(target)) {
        this.hideDropdown()
      }
    }
    document.addEventListener('click', this.documentClickHandler)
  }

  // 显示下拉框并定位到按钮下方
  private showDropdown(): void {
    // 先设置基本样式
    this.controlOptionDom.style.position = 'fixed'
    this.controlOptionDom.style.zIndex = '999999'

    // 添加visible类，直接显示不要动画
    this.controlOptionDom.classList.add('visible')

    // 立即计算位置
    this.positionDropdown()
  }

  // 精确定位下拉框到按钮下方
  private positionDropdown(): void {
    const rect = this.controlDom.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 默认位置：按钮下方
    let left = rect.left
    let top = rect.bottom + 4

    // 水平边界检查（控件下拉框宽度约120px）
    if (left + 120 > viewportWidth) {
      left = viewportWidth - 120 - 10
    }
    if (left < 10) {
      left = 10
    }

    // 垂直边界检查
    if (top + 200 > viewportHeight) {
      top = rect.top - 200 - 4
    }
    if (top < 10) {
      top = 10
    }

    // 应用位置
    this.controlOptionDom.style.left = left + 'px'
    this.controlOptionDom.style.top = top + 'px'
  }

  // 隐藏下拉框
  private hideDropdown(): void {
    this.controlOptionDom.classList.remove('visible')
  }

  // 隐藏所有下拉框（避免多个下拉框同时显示）
  private hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options.visible, .menu-item__table__collapse[style*="block"]')
    allDropdowns.forEach(dropdown => {
      if (dropdown.classList.contains('visible')) {
        dropdown.classList.remove('visible')
      } else {
        (dropdown as HTMLElement).style.display = 'none'
      }
    })
  }
  
  // 销毁组件时移除全局事件监听
  public destroy(): void {
    document.removeEventListener('click', this.documentClickHandler)
  }

  private openTextControlDialog(type: ControlType): void {
    new Dialog({
      title: '文本控件',
      data: [
        {
          type: 'text',
          label: '占位符',
          name: 'placeholder',
          required: true,
          placeholder: '请输入占位符'
        },
        {
          type: 'text',
          label: '默认值',
          name: 'value',
          placeholder: '请输入默认值'
        }
      ],
      onConfirm: payload => {
        const placeholder = payload.find(p => p.name === 'placeholder')?.value
        if (!placeholder) return
        const value = payload.find(p => p.name === 'value')?.value || ''
        
        this.instance.command.executeInsertControl({
          type: ElementType.CONTROL,
          value: '',
          control: {
            type,
            value: value
              ? [
                  {
                    value
                  }
                ]
              : null,
            placeholder
          }
        })
      }
    })
  }

  private openSelectControlDialog(type: ControlType): void {
    new Dialog({
      title: '列举控件',
      data: [
        {
          type: 'text',
          label: '占位符',
          name: 'placeholder',
          required: true,
          placeholder: '请输入占位符'
        },
        {
          type: 'text',
          label: '默认值',
          name: 'code',
          placeholder: '请输入默认值'
        },
        {
          type: 'textarea',
          label: '值集',
          name: 'valueSets',
          required: true,
          height: 100,
          placeholder: `请输入值集JSON，例：\n[{\n"value":"有",\n"code":"98175"\n}]`
        }
      ],
      onConfirm: payload => {
        const placeholder = payload.find(p => p.name === 'placeholder')?.value
        if (!placeholder) return
        const valueSets = payload.find(p => p.name === 'valueSets')?.value
        if (!valueSets) return
        const code = payload.find(p => p.name === 'code')?.value
        
        this.instance.command.executeInsertControl({
          type: ElementType.CONTROL,
          value: '',
          control: {
            type,
            code,
            value: null,
            placeholder,
            valueSets: JSON.parse(valueSets)
          }
        })
      }
    })
  }

  private openCheckboxControlDialog(type: ControlType): void {
    new Dialog({
      title: '复选框控件',
      data: [
        {
          type: 'text',
          label: '默认值',
          name: 'code',
          placeholder: '请输入默认值，多个值以英文逗号分割'
        },
        {
          type: 'textarea',
          label: '值集',
          name: 'valueSets',
          required: true,
          height: 100,
          placeholder: `请输入值集JSON，例：\n[{\n"value":"有",\n"code":"98175"\n}]`
        }
      ],
      onConfirm: payload => {
        const valueSets = payload.find(p => p.name === 'valueSets')?.value
        if (!valueSets) return
        const code = payload.find(p => p.name === 'code')?.value
        
        this.instance.command.executeInsertControl({
          type: ElementType.CONTROL,
          value: '',
          control: {
            type,
            code,
            value: null,
            valueSets: JSON.parse(valueSets)
          }
        })
      }
    })
  }

  private openRadioControlDialog(type: ControlType): void {
    new Dialog({
      title: '单选框控件',
      data: [
        {
          type: 'text',
          label: '默认值',
          name: 'code',
          placeholder: '请输入默认值'
        },
        {
          type: 'textarea',
          label: '值集',
          name: 'valueSets',
          required: true,
          height: 100,
          placeholder: `请输入值集JSON，例：\n[{\n"value":"有",\n"code":"98175"\n}]`
        }
      ],
      onConfirm: payload => {
        const valueSets = payload.find(p => p.name === 'valueSets')?.value
        if (!valueSets) return
        const code = payload.find(p => p.name === 'code')?.value
        
        this.instance.command.executeInsertControl({
          type: ElementType.CONTROL,
          value: '',
          control: {
            type,
            code,
            value: null,
            valueSets: JSON.parse(valueSets)
          }
        })
      }
    })
  }

  private openDateControlDialog(type: ControlType): void {
    new Dialog({
      title: '日期控件',
      data: [
        {
          type: 'text',
          label: '占位符',
          name: 'placeholder',
          required: true,
          placeholder: '请输入占位符'
        },
        {
          type: 'text',
          label: '默认值',
          name: 'value',
          placeholder: '请输入默认值'
        },
        {
          type: 'select',
          label: '日期格式',
          name: 'dateFormat',
          value: 'yyyy-MM-dd hh:mm:ss',
          required: true,
          options: [
            {
              label: 'yyyy-MM-dd hh:mm:ss',
              value: 'yyyy-MM-dd hh:mm:ss'
            },
            {
              label: 'yyyy-MM-dd',
              value: 'yyyy-MM-dd'
            }
          ]
        }
      ],
      onConfirm: payload => {
        const placeholder = payload.find(p => p.name === 'placeholder')?.value
        if (!placeholder) return
        const value = payload.find(p => p.name === 'value')?.value || ''
        const dateFormat = payload.find(p => p.name === 'dateFormat')?.value || ''
        
        this.instance.command.executeInsertControl({
          type: ElementType.CONTROL,
          value: '',
          control: {
            type,
            dateFormat,
            value: value
              ? [
                  {
                    value
                  }
                ]
              : null,
            placeholder
          }
        })
      }
    })
  }

  private openNumberControlDialog(type: ControlType): void {
    new Dialog({
      title: '数值控件',
      data: [
        {
          type: 'text',
          label: '占位符',
          name: 'placeholder',
          required: true,
          placeholder: '请输入占位符'
        },
        {
          type: 'text',
          label: '默认值',
          name: 'value',
          placeholder: '请输入默认值'
        }
      ],
      onConfirm: payload => {
        const placeholder = payload.find(p => p.name === 'placeholder')?.value
        if (!placeholder) return
        const value = payload.find(p => p.name === 'value')?.value || ''
        
        this.instance.command.executeInsertControl({
          type: ElementType.CONTROL,
          value: '',
          control: {
            type,
            value: value
              ? [
                  {
                    value
                  }
                ]
              : null,
            placeholder
          }
        })
      }
    })
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}