from django.db import models
from django.contrib.auth.models import User

# Create your models here.

class Document(models.Model):
    """
    文档模型 - 存储 Canvas Editor 创建的文档
    """
    title = models.CharField(max_length=200, verbose_name="文档标题")
    content = models.JSONField(verbose_name="文档内容", help_text="Canvas Editor 的 JSON 格式内容")
    author = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="作者")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    is_public = models.BooleanField(default=False, verbose_name="是否公开")
    tags = models.CharField(max_length=500, blank=True, verbose_name="标签", help_text="用逗号分隔")

    class Meta:
        verbose_name = "文档"
        verbose_name_plural = "文档"
        ordering = ['-updated_at']

    def __str__(self):
        return self.title

class DocumentVersion(models.Model):
    """
    文档版本模型 - 存储文档的历史版本
    """
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='versions', verbose_name="文档")
    version_number = models.PositiveIntegerField(verbose_name="版本号")
    content = models.JSONField(verbose_name="版本内容")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="创建者")
    comment = models.TextField(blank=True, verbose_name="版本说明")

    class Meta:
        verbose_name = "文档版本"
        verbose_name_plural = "文档版本"
        ordering = ['-version_number']
        unique_together = ['document', 'version_number']

    def __str__(self):
        return f"{self.document.title} - v{self.version_number}"
