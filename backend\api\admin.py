from django.contrib import admin
from .models import Document, DocumentVersion

# Register your models here.

@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ['title', 'author', 'is_public', 'created_at', 'updated_at']
    list_filter = ['is_public', 'created_at', 'author']
    search_fields = ['title', 'tags']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'author', 'is_public')
        }),
        ('内容', {
            'fields': ('content', 'tags')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(DocumentVersion)
class DocumentVersionAdmin(admin.ModelAdmin):
    list_display = ['document', 'version_number', 'created_by', 'created_at']
    list_filter = ['created_at', 'created_by']
    search_fields = ['document__title', 'comment']
    readonly_fields = ['created_at']
