import { ElementType } from '../../editor'
import { CanvasEditor } from '../../editor'
import { Dialog } from '../dialog/Dialog'
import { splitText } from '../../editor'
import html from './HyperlinkButton.html'
import './HyperlinkButton.css'

export class HyperlinkButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      new Dialog({
        title: '超链接',
        data: [
          {
            type: 'text',
            label: '文本',
            name: 'name',
            required: true,
            placeholder: '请输入文本',
            value: this.instance.command.getRangeText()
          },
          {
            type: 'text',
            label: '链接',
            name: 'url',
            required: true,
            placeholder: '请输入链接'
          }
        ],
        onConfirm: payload => {
          const name = payload.find(p => p.name === 'name')?.value
          if (!name) return
          const url = payload.find(p => p.name === 'url')?.value
          if (!url) return
          
          this.instance.command.executeHyperlink({
            type: ElementType.HYPERLINK,
            value: '',
            url,
            valueList: splitText(name).map(n => ({
              value: n,
              size: 16
            }))
          })
        }
      })
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 