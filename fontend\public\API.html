<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Editor - API测试面板</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 30px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 10px 20px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 25px;
            transition: all 0.3s ease;
            display: inline-block;
        }
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.6);
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1em;
            display: flex;
            align-items: center;
        }
        .test-title::before {
            content: "🔧";
            margin-right: 8px;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
            white-space: pre-wrap;
            font-size: 0.9em;
            line-height: 1.4;
        }
        .success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,123,255,0.3);
        }
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,123,255,0.4);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-unknown { background: #ffc107; }
        .api-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .api-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔗 Canvas Editor API测试面板</h1>
        <p>测试后端API连接和功能</p>
    </div>

    <div class="nav-links">
        <a href="../">← 返回编辑器</a>
        <a href="http://127.0.0.1:8000/admin/" target="_blank">管理后台</a>
        <a href="http://127.0.0.1:8000/api/docs/" target="_blank">API文档</a>
    </div>

    <div class="container">
        <div class="api-info">
            <h3>📡 API连接信息</h3>
            <p><strong>后端地址:</strong> http://127.0.0.1:8000/</p>
            <p><strong>数据库:</strong> MySQL 远程数据库 (8.155.46.35:3306)</p>
            <p><strong>代理配置:</strong> /api → http://127.0.0.1:8000</p>
            <p><strong>连接状态:</strong> <span class="status-indicator status-unknown"></span><span id="connection-status">检测中...</span></p>
        </div>
        
        <div class="test-section">
            <div class="test-title">健康检查测试</div>
            <button onclick="testHealthCheck()">🏥 测试健康检查</button>
            <div id="health-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">文档API测试</div>
            <button onclick="testDocuments()">📄 获取文档列表</button>
            <button onclick="testCreateDocument()">➕ 创建测试文档</button>
            <div id="documents-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">API根路径测试</div>
            <button onclick="testApiRoot()">🌐 测试API根路径</button>
            <div id="api-root-result" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">完整连接测试</div>
            <button onclick="runAllTests()">🧪 运行所有测试</button>
            <div id="all-tests-result" class="result"></div>
        </div>
    </div>

    <script>
        // API基础配置
        const API_BASE = '/api';
        
        // 显示结果的辅助函数
        function showResult(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 显示加载状态
        function showLoading(elementId, message = '正在测试...') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result loading';
        }

        // 更新连接状态
        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connection-status');
            const indicator = document.querySelector('.status-indicator');
            
            statusElement.textContent = message;
            indicator.className = `status-indicator status-${status}`;
        }

        // 健康检查测试
        async function testHealthCheck() {
            showLoading('health-result', '正在检查API健康状态...');
            
            try {
                const response = await fetch(`${API_BASE}/health/`);
                const data = await response.json();
                
                if (response.ok) {
                    const result = `✅ 健康检查成功！
状态: ${data.status}
时间: ${data.timestamp}
版本: ${data.version}
数据库: ${data.database}
调试模式: ${data.debug}`;
                    showResult('health-result', result, 'success');
                    updateConnectionStatus('online', '连接正常');
                } else {
                    showResult('health-result', `❌ 健康检查失败: ${response.status} ${response.statusText}`, 'error');
                    updateConnectionStatus('offline', '连接失败');
                }
            } catch (error) {
                showResult('health-result', `❌ 网络错误: ${error.message}`, 'error');
                updateConnectionStatus('offline', '网络错误');
            }
        }

        // 文档API测试
        async function testDocuments() {
            showLoading('documents-result', '正在获取文档列表...');
            
            try {
                const response = await fetch(`${API_BASE}/documents/`);
                const data = await response.json();
                
                if (response.ok) {
                    const result = `✅ 文档列表获取成功！
总数: ${data.count}
文档数量: ${data.results.length}
${data.results.map(doc => `- ${doc.title} (ID: ${doc.id})`).join('\n')}`;
                    showResult('documents-result', result, 'success');
                } else {
                    showResult('documents-result', `❌ 获取文档失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult('documents-result', `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 创建测试文档
        async function testCreateDocument() {
            showLoading('documents-result', '正在创建测试文档...');
            
            try {
                const testDoc = {
                    title: `API测试文档 - ${new Date().toLocaleString()}`,
                    content: {
                        main: [
                            {
                                type: 'paragraph',
                                children: [
                                    { text: '这是一个通过API创建的测试文档。' }
                                ]
                            }
                        ]
                    },
                    is_public: false
                };

                const response = await fetch(`${API_BASE}/documents/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testDoc)
                });

                const data = await response.json();
                
                if (response.ok) {
                    const result = `✅ 文档创建成功！
ID: ${data.id}
标题: ${data.title}
创建时间: ${data.created_at}
作者: ${data.author || '未知'}`;
                    showResult('documents-result', result, 'success');
                } else {
                    showResult('documents-result', `❌ 创建文档失败: ${response.status} ${response.statusText}\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('documents-result', `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // API根路径测试
        async function testApiRoot() {
            showLoading('api-root-result', '正在测试API根路径...');
            
            try {
                const response = await fetch(`${API_BASE}/`);
                const data = await response.json();
                
                if (response.ok) {
                    const result = `✅ API根路径访问成功！
消息: ${data.message}
版本: ${data.version}
数据库: ${data.database}
可用端点:
${Object.entries(data.endpoints).map(([key, value]) => `- ${key}: ${value}`).join('\n')}`;
                    showResult('api-root-result', result, 'success');
                } else {
                    showResult('api-root-result', `❌ API根路径访问失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult('api-root-result', `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 运行所有测试
        async function runAllTests() {
            showLoading('all-tests-result', '正在运行完整测试套件...');
            
            const tests = [
                { name: '健康检查', fn: () => fetch(`${API_BASE}/health/`) },
                { name: 'API根路径', fn: () => fetch(`${API_BASE}/`) },
                { name: '文档列表', fn: () => fetch(`${API_BASE}/documents/`) }
            ];

            const results = [];
            
            for (const test of tests) {
                try {
                    const response = await test.fn();
                    if (response.ok) {
                        results.push(`✅ ${test.name}: 成功`);
                    } else {
                        results.push(`❌ ${test.name}: 失败 (${response.status})`);
                    }
                } catch (error) {
                    results.push(`❌ ${test.name}: 网络错误`);
                }
            }

            const summary = `🧪 完整测试结果:
${results.join('\n')}

📊 测试统计:
- 总测试数: ${tests.length}
- 成功: ${results.filter(r => r.includes('✅')).length}
- 失败: ${results.filter(r => r.includes('❌')).length}

${results.every(r => r.includes('✅')) ? '🎉 所有测试通过！前后端连接正常！' : '⚠️ 部分测试失败，请检查后端服务状态。'}`;

            showResult('all-tests-result', summary, results.every(r => r.includes('✅')) ? 'success' : 'error');
            
            // 更新整体连接状态
            if (results.every(r => r.includes('✅'))) {
                updateConnectionStatus('online', '所有测试通过');
            } else {
                updateConnectionStatus('offline', '部分测试失败');
            }
        }

        // 页面加载完成后自动运行健康检查
        window.addEventListener('load', () => {
            console.log('🚀 API测试面板已加载');
            testHealthCheck();
        });
    </script>
</body>
</html>
