import { CanvasEditor } from '../../editor'
import html from './FullscreenButton.html'
import './FullscreenButton.css'

export class FullscreenButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.toggleFullscreen()
    }
    
    window.addEventListener('keydown', evt => {
      if (evt.key === 'F11') {
        this.toggleFullscreen()
        evt.preventDefault()
      }
    })
    
    document.addEventListener('fullscreenchange', () => {
      this.dom.classList.toggle('exist')
    })
  }
  
  private toggleFullscreen(): void {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 