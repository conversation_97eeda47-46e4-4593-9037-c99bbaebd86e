.menu-item__search {
  position: relative;
}

.menu-item__search i {
  background-image: url('../../assets/images/search.svg');
}

.menu-item__search__collapse {
  position: absolute;
  top: 34px;
  padding: 8px;
  width: 280px;
  background-color: #fff;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  display: none;
  z-index: 2;
}

.menu-item__search__collapse.visible {
  display: block;
}

.menu-item__search__collapse__search {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.menu-item__search__collapse__search input {
  width: 220px;
  height: 24px;
  margin-right: 5px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transition: border-color 0.3s;
  outline: none;
}

.menu-item__search__collapse__search input:focus {
  border-color: #4a89ff;
}

.menu-item__search__collapse__search .search-result {
  margin-right: 5px;
  white-space: nowrap;
}

.menu-item__search__collapse__search .arrow-left {
  cursor: pointer;
  width: 16px;
  height: 16px;
  border-radius: 2px;
  margin-right: 5px;
  background-color: #f7f7f7;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-item__search__collapse__search .arrow-left:hover {
  background-color: #f1f1f1;
}

.menu-item__search__collapse__search .arrow-left i {
  display: block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/arrow-left.svg');
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.menu-item__search__collapse__search .arrow-right {
  cursor: pointer;
  width: 16px;
  height: 16px;
  border-radius: 2px;
  margin-right: 5px;
  background-color: #f7f7f7;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-item__search__collapse__search .arrow-right:hover {
  background-color: #f1f1f1;
}

.menu-item__search__collapse__search .arrow-right i {
  display: block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/arrow-right.svg');
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.menu-item__search__collapse__search span {
  cursor: pointer;
  font-size: 20px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  color: #000;
  border-radius: 2px;
  margin-right: 5px;
  background-color: #f7f7f7;
  text-align: center;
}

.menu-item__search__collapse__search span:hover {
  background-color: #f1f1f1;
}

.menu-item__search__collapse__replace {
  display: flex;
  align-items: center;
}

.menu-item__search__collapse__replace input {
  width: 220px;
  height: 24px;
  margin-right: 5px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transition: border-color 0.3s;
  outline: none;
}
.searchclose {
  color: #000 !important;

}

.menu-item__search__collapse__replace input:focus {
  border-color: #4a89ff;
}

.menu-item__search__collapse__replace button {
  width: 50px;
  height: 24px;
  line-height: 1;
  font-size: 12px;
  color: #000000;
  border: none;
  border-radius: 2px;
  background-color: #4a89ff;
  cursor: pointer;
} 