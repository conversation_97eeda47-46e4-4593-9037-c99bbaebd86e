{"compilerOptions": {"target": "ESNext", "module": "ESNext", "lib": ["ESNext", "DOM"], "moduleResolution": "Node", "strict": true, "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "declaration": true, "noEmit": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "outDir": "dist", "rootDir": "", "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["./src/", "./src/types"], "exclude": ["node_modules", "dist"]}