/**
 * 浏览器扩展兼容性处理模块
 * 用于处理浏览器扩展可能引起的Selection API冲突
 */

import { SafeSelectionHandler } from './selection'

/**
 * 扩展冲突检测器
 */
export class ExtensionConflictDetector {
  private static instance: ExtensionConflictDetector
  private conflictingExtensions: Set<string> = new Set()
  private selectionHandler: SafeSelectionHandler

  private constructor() {
    this.selectionHandler = new SafeSelectionHandler()
    this.detectConflicts()
  }

  public static getInstance(): ExtensionConflictDetector {
    if (!ExtensionConflictDetector.instance) {
      ExtensionConflictDetector.instance = new ExtensionConflictDetector()
    }
    return ExtensionConflictDetector.instance
  }

  /**
   * 检测可能冲突的扩展
   */
  private detectConflicts(): void {
    // 检测常见的可能引起Selection API冲突的扩展特征
    this.checkForCommonExtensions()
    
    // 监听DOM变化，检测扩展注入的内容
    this.observeExtensionInjections()
    
    // 设置Selection API保护
    this.setupSelectionProtection()
  }

  /**
   * 检查常见扩展
   */
  private checkForCommonExtensions(): void {
    // 检查是否存在常见扩展的标识
    const extensionIndicators = [
      'chrome-extension://',
      'moz-extension://',
      'safari-extension://',
      'edge-extension://'
    ]

    // 检查页面中是否有扩展注入的脚本或样式
    const scripts = document.querySelectorAll('script[src*="extension"]')
    const links = document.querySelectorAll('link[href*="extension"]')
    
    if (scripts.length > 0 || links.length > 0) {
      console.warn('检测到可能的浏览器扩展注入内容')
      this.conflictingExtensions.add('unknown-extension')
    }

    // 检查全局对象中是否有扩展相关属性
    if (typeof window !== 'undefined') {
      const globalKeys = Object.keys(window)
      const extensionKeys = globalKeys.filter(key => 
        key.includes('extension') || 
        key.includes('chrome') || 
        key.includes('addon')
      )
      
      if (extensionKeys.length > 0) {
        console.debug('检测到可能的扩展全局对象:', extensionKeys)
      }
    }
  }

  /**
   * 监听扩展注入
   */
  private observeExtensionInjections(): void {
    // 创建MutationObserver来监听DOM变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element
              
              // 检查是否是扩展注入的元素
              if (this.isExtensionInjectedElement(element)) {
                console.warn('检测到扩展注入的元素:', element)
                this.handleExtensionInjection(element)
              }
            }
          })
        }
      })
    })

    // 开始观察
    observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  }

  /**
   * 检查是否是扩展注入的元素
   */
  private isExtensionInjectedElement(element: Element): boolean {
    // 检查元素的属性和内容
    const extensionIndicators = [
      'chrome-extension',
      'moz-extension',
      'safari-extension',
      'edge-extension',
      'extension-',
      'ext-'
    ]

    // 检查元素的ID、类名、属性等
    const elementText = element.outerHTML.toLowerCase()
    return extensionIndicators.some(indicator => 
      elementText.includes(indicator)
    )
  }

  /**
   * 处理扩展注入
   */
  private handleExtensionInjection(element: Element): void {
    // 如果注入的元素可能影响Selection API，则采取保护措施
    if (this.mightAffectSelection(element)) {
      this.applySelectionProtection()
    }
  }

  /**
   * 检查元素是否可能影响Selection
   */
  private mightAffectSelection(element: Element): boolean {
    // 检查元素是否包含可能影响Selection的内容
    const dangerousPatterns = [
      'selection',
      'range',
      'getrangeAt',
      'contenteditable'
    ]

    const elementContent = element.outerHTML.toLowerCase()
    return dangerousPatterns.some(pattern => 
      elementContent.includes(pattern)
    )
  }

  /**
   * 设置Selection API保护
   */
  private setupSelectionProtection(): void {
    // 保存原始的Selection API方法
    const originalGetRangeAt = Selection.prototype.getRangeAt
    const originalAddRange = Selection.prototype.addRange
    const originalRemoveAllRanges = Selection.prototype.removeAllRanges

    // 包装getRangeAt方法
    Selection.prototype.getRangeAt = function(index: number): Range {
      try {
        // 检查索引有效性
        if (this.rangeCount === 0) {
          throw new DOMException('No ranges available', 'IndexSizeError')
        }
        
        if (index < 0 || index >= this.rangeCount) {
          throw new DOMException(`Index ${index} is not valid`, 'IndexSizeError')
        }
        
        return originalGetRangeAt.call(this, index)
      } catch (error) {
        console.warn('Selection.getRangeAt 错误已被捕获:', error)
        
        // 尝试创建一个空的Range作为fallback
        const range = document.createRange()
        range.collapse(true)
        return range
      }
    }

    // 包装addRange方法
    Selection.prototype.addRange = function(range: Range): void {
      try {
        return originalAddRange.call(this, range)
      } catch (error) {
        console.warn('Selection.addRange 错误已被捕获:', error)
      }
    }

    // 包装removeAllRanges方法
    Selection.prototype.removeAllRanges = function(): void {
      try {
        return originalRemoveAllRanges.call(this)
      } catch (error) {
        console.warn('Selection.removeAllRanges 错误已被捕获:', error)
      }
    }
  }

  /**
   * 应用Selection保护
   */
  private applySelectionProtection(): void {
    // 临时禁用可能有问题的Selection操作
    const protectionDuration = 1000 // 1秒

    setTimeout(() => {
      // 清理可能的Selection状态
      try {
        const selection = window.getSelection()
        if (selection && selection.rangeCount > 0) {
          selection.removeAllRanges()
        }
      } catch (error) {
        console.warn('清理Selection状态时发生错误:', error)
      }
    }, protectionDuration)
  }

  /**
   * 获取冲突扩展列表
   */
  public getConflictingExtensions(): string[] {
    return Array.from(this.conflictingExtensions)
  }

  /**
   * 检查是否存在扩展冲突
   */
  public hasConflicts(): boolean {
    return this.conflictingExtensions.size > 0
  }

  /**
   * 手动报告扩展冲突
   */
  public reportConflict(extensionName: string): void {
    this.conflictingExtensions.add(extensionName)
    console.warn(`报告扩展冲突: ${extensionName}`)
  }
}

/**
 * 初始化扩展兼容性检测
 */
export function initializeExtensionCompat(): ExtensionConflictDetector {
  return ExtensionConflictDetector.getInstance()
}

/**
 * 创建扩展安全的Selection操作包装器
 */
export function createExtensionSafeWrapper<T extends (...args: any[]) => any>(
  fn: T,
  fallback?: ReturnType<T>
): T {
  return ((...args: Parameters<T>): ReturnType<T> => {
    try {
      return fn(...args)
    } catch (error) {
      console.warn('扩展安全包装器捕获错误:', error)
      
      // 检查是否是Selection相关错误
      if (error instanceof DOMException && 
          error.message.includes('not a valid index')) {
        // 报告可能的扩展冲突
        const detector = ExtensionConflictDetector.getInstance()
        detector.reportConflict('selection-api-conflict')
      }
      
      return fallback as ReturnType<T>
    }
  }) as T
}
