import './NewFontButton.css'

export class NewFontButton {
  private element: HTMLDivElement;
  private selectElement: HTMLSpanElement;
  private optionsElement: HTMLDivElement;
  private command: any;
  private documentClickHandler: (e: MouseEvent) => void = () => {
    // 处理文档点击事件
  };

  constructor(container: HTMLElement, command: any) {
    this.command = command
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.new-font-button') as HTMLDivElement
    this.selectElement = this.element.querySelector('.select') as HTMLSpanElement
    this.optionsElement = this.element.querySelector('.options') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="new-font-button">
      <span class="select" title="字体">微软雅黑</span>
      <div class="options">
        <ul>
          <li data-family="Microsoft YaHei" style="font-family:'Microsoft YaHei';">微软雅黑</li>
          <li data-family="SimSun" style="font-family:'SimSun';">宋体</li>
          <li data-family="SimHei" style="font-family:'SimHei';">黑体</li>
          <li data-family="KaiTi" style="font-family:'KaiTi';">楷体</li>
          <li data-family="FangSong" style="font-family:'FangSong';">仿宋</li>
          <li data-family="华文宋体" style="font-family:'华文宋体';">华文宋体</li>
          <li data-family="华文黑体" style="font-family:'华文黑体';">华文黑体</li>
          <li data-family="华文仿宋" style="font-family:'华文仿宋';">华文仿宋</li>
          <li data-family="华文楷体" style="font-family:'华文楷体';">华文楷体</li>
          <li data-family="华文琥珀" style="font-family:'华文琥珀';">华文琥珀</li>
          <li data-family="华文隶书" style="font-family:'华文隶书';">华文隶书</li>
          <li data-family="华文新魏" style="font-family:'华文新魏';">华文新魏</li>
          <li data-family="华文行楷" style="font-family:'华文行楷';">华文行楷</li>
          <li data-family="华文中宋" style="font-family:'华文中宋';">华文中宋</li>
          <li data-family="华文彩云" style="font-family:'华文彩云';">华文彩云</li>
          <li data-family="Arial" style="font-family:'Arial';">Arial</li>
          <li data-family="Times New Roman" style="font-family:'Times New Roman';">Times New Roman</li>
          <li data-family="Calibri" style="font-family:'Calibri';">Calibri</li>
          <li data-family="Segoe UI" style="font-family:'Segoe UI';">Segoe UI</li>
          <li data-family="Helvetica" style="font-family:'Helvetica';">Helvetica</li>
          <li data-family="Georgia" style="font-family:'Georgia';">Georgia</li>
          <li data-family="Verdana" style="font-family:'Verdana';">Verdana</li>
          <li data-family="Tahoma" style="font-family:'Tahoma';">Tahoma</li>
          <li data-family="Trebuchet MS" style="font-family:'Trebuchet MS';">Trebuchet MS</li>
          <li data-family="Courier New" style="font-family:'Courier New';">Courier New</li>
          <li data-family="Consolas" style="font-family:'Consolas';">Consolas</li>
          <li data-family="Monaco" style="font-family:'Monaco';">Monaco</li>
          <li data-family="Menlo" style="font-family:'Menlo';">Menlo</li>
          <li data-family="Source Code Pro" style="font-family:'Source Code Pro';">Source Code Pro</li>
          <li data-family="Fira Code" style="font-family:'Fira Code';">Fira Code</li>
        </ul>
      </div>
    </div>`
  }

  private bindEvents(): void {
    // 点击按钮切换下拉框显示状态
    this.element.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      console.log('new-font')
      
      // 切换显示状态
      const isVisible = this.optionsElement.classList.contains('visible')
      
      // 先隐藏所有其他的下拉框
      this.hideAllDropdowns()
      
      if (!isVisible) {
        // 显示当前下拉框
        this.showDropdown()
      }
    }

    // 点击下拉框选项
    this.optionsElement.onclick = (evt) => {
      evt.stopPropagation() // 阻止事件冒泡
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        const family = li.dataset.family
        if (family) {
          this.command.executeFont(family)
          this.updateSelectedFont(family, li.innerText)
          this.hideDropdown()
        }
      }
    }

    // 阻止下拉框内的点击事件冒泡到document
    this.optionsElement.addEventListener('click', (e) => {
      e.stopPropagation()
    })

    // 点击外部关闭下拉框
    this.documentClickHandler = (e) => {
      const target = e.target as Node
      if (!this.element.contains(target) && !this.optionsElement.contains(target)) {
        this.hideDropdown()
      }
    }
    document.addEventListener('click', this.documentClickHandler)
  }

  // 显示下拉框并定位到最上层
  private showDropdown(): void {
    // 先设置基本样式
    this.optionsElement.style.position = 'fixed'
    this.optionsElement.style.zIndex = '999999'

    // 添加visible类
    this.optionsElement.classList.add('visible')

    // 等待一帧后计算位置，确保元素已渲染
    requestAnimationFrame(() => {
      this.positionDropdown()
    })
  }

  // 精确定位下拉框
  private positionDropdown(): void {
    const rect = this.element.getBoundingClientRect()
    const optionsRect = this.optionsElement.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 默认位置：按钮下方
    let left = rect.left
    let top = rect.bottom + 4

    // 水平边界检查
    if (left + 200 > viewportWidth) {
      left = viewportWidth - 200 - 10
    }
    if (left < 10) {
      left = 10
    }

    // 垂直边界检查
    if (top + 300 > viewportHeight) {
      top = rect.top - 300 - 4
    }
    if (top < 10) {
      top = 10
    }

    // 应用位置
    this.optionsElement.style.left = left + 'px'
    this.optionsElement.style.top = top + 'px'
  }

  // 隐藏下拉框
  private hideDropdown(): void {
    this.optionsElement.classList.remove('visible')
  }

  // 隐藏所有下拉框（避免多个下拉框同时显示）
  private hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options.visible')
    allDropdowns.forEach(dropdown => {
      dropdown.classList.remove('visible')
    })
  }

  // 更新选中的字体显示
  private updateSelectedFont(family: string, displayName: string): void {
    this.selectElement.innerText = displayName
    this.selectElement.style.fontFamily = family
    
    // 更新选项的活动状态
    const options = this.optionsElement.querySelectorAll('li')
    options.forEach(li => li.classList.remove('active'))
    
    const selectedOption = this.optionsElement.querySelector(`[data-family='${family}']`)
    if (selectedOption) {
      selectedOption.classList.add('active')
    }
  }

  // 销毁组件时移除全局事件监听
  public destroy(): void {
    document.removeEventListener('click', this.documentClickHandler)
  }

  // 更新按钮状态（外部调用）
  public updateState(font: string): void {
    const options = this.optionsElement.querySelectorAll('li')
    options.forEach(li => li.classList.remove('active'))
    
    const curFontDom = this.optionsElement.querySelector<HTMLLIElement>(`[data-family='${font}']`)
    if (curFontDom) {
      this.selectElement.innerText = curFontDom.innerText
      this.selectElement.style.fontFamily = font
      curFontDom.classList.add('active')
    }
  }
}
