import { FORMAT_PLACEHOLDER } from '../../../dataset/constant/Common'
import { NumberType } from '../../../dataset/enum/Common'
import { RowFlex } from '../../../dataset/enum/Row'
import { DeepRequired } from '../../../interface/Common'
import { IEditorOption } from '../../../interface/Editor'
import { Draw } from '../Draw'

export class PageNumber {
  private draw: Draw
  private options: DeepRequired<IEditorOption>

  constructor(draw: Draw) {
    this.draw = draw
    this.options = draw.getOptions()
  }

  // 页码渲染方法，接受canvas上下文和页码参数
  public render(ctx: CanvasRenderingContext2D, pageNo: number) {
    const {
      scale,
      pageNumber: {
        bottom,
        size,
        font,
        color,
        rowFlex,
        format,
        numberType,
        startPageNo,
        fromPageNo
      }
    } = this.options

    // 检查是否应该显示页码
    if (pageNo < fromPageNo) {
      return
    }

    const textParticle = this.draw.getTextParticle()
    const margins = this.draw.getMargins()
    const innerWidth = this.draw.getInnerWidth()
    const pageHeight = this.draw.getHeight()

    // 格式化页码文本
    const currentPageNo = pageNo + startPageNo
    const totalPages = this.draw.getPageCount()
    const pageText = this.formatPageNumber(format, currentPageNo, totalPages, numberType)

    ctx.save()
    ctx.fillStyle = color
    ctx.font = `${size * scale}px ${font}`

    // 测量文本尺寸
    const textMetrics = textParticle.measureText(ctx, {
      value: pageText
    })

    // 计算页码位置
    const textWidth = textMetrics.width * scale
    const textHeight = textMetrics.actualBoundingBoxAscent * scale

    // 根据对齐方式计算X坐标
    let x: number
    switch (rowFlex) {
      case RowFlex.LEFT:
        x = margins[3]
        break
      case RowFlex.RIGHT:
        x = margins[3] + innerWidth - textWidth
        break
      case RowFlex.CENTER:
      default:
        x = margins[3] + (innerWidth - textWidth) / 2
        break
    }

    // Y坐标：页面底部向上偏移指定距离
    const y = pageHeight - bottom * scale + textHeight

    // 绘制页码文本
    ctx.fillText(pageText, x, y)
    ctx.restore()
  }

  // 格式化页码文本
  private formatPageNumber(
    format: string,
    currentPageNo: number,
    totalPages: number,
    numberType: NumberType
  ): string {
    // 根据数字类型转换页码
    const formatNumber = (num: number): string => {
      switch (numberType) {
        case NumberType.CHINESE:
          return this.toChineseNumeral(num)
        case NumberType.ARABIC:
        default:
          return String(num)
      }
    }

    return format
      .replace(new RegExp(FORMAT_PLACEHOLDER.PAGE_NO, 'g'), formatNumber(currentPageNo))
      .replace(new RegExp(FORMAT_PLACEHOLDER.PAGE_COUNT, 'g'), formatNumber(totalPages))
  }

  // 转换为中文数字
  private toChineseNumeral(num: number): string {
    const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    const chineseUnits = ['', '十', '百', '千', '万']

    if (num === 0) return chineseNums[0]
    if (num < 10) return chineseNums[num]

    let result = ''
    let unitIndex = 0

    while (num > 0) {
      const digit = num % 10
      if (digit !== 0) {
        result = chineseNums[digit] + chineseUnits[unitIndex] + result
      } else if (result && !result.startsWith('零')) {
        result = '零' + result
      }
      num = Math.floor(num / 10)
      unitIndex++
    }

    // 处理特殊情况：十几的数字
    if (result.startsWith('一十')) {
      result = result.substring(1)
    }

    return result
  }

  // 静态方法：格式化页码占位符（保持向后兼容）
  public static formatNumberPlaceholder(
    format: string,
    pageValue: number,
    _regex: RegExp,
    _numberType: any
  ): string {
    // 简单的占位符替换逻辑
    return format
      .replace(/\{pageNo\}/g, String(pageValue))
      .replace(/\{pageCount\}/g, String(pageValue))
  }

  public getPageNumber(): number {
    return this.draw.getPageNo() + 1
  }

  public getTotalPages(): number {
    return this.draw.getPageCount()
  }
}