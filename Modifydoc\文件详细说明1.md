# Canvas Editor 核心架构和主要模块

## 🏗️ 编辑器核心架构设计

Canvas Editor 采用分层架构设计，从上到下分为以下几个层次：

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│                    组件层 (Component Layer)                  │
├─────────────────────────────────────────────────────────────┤
│                    编辑器层 (Editor Layer)                   │
├─────────────────────────────────────────────────────────────┤
│                    核心层 (Core Layer)                       │
├─────────────────────────────────────────────────────────────┤
│                    渲染层 (Render Layer)                     │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心模块详解

### 1. 编辑器主入口 (`src/editor/index.ts`)

**功能**: 编辑器的统一入口，负责整个编辑器的初始化和生命周期管理

```typescript
export class Editor {
  public command: Command
  public listener: Listener
  public eventBus: EventBus<EventBusMap>
  public register: Register
  public destroy: Function
  public use: Function
  public override: Override

  constructor(
    container: HTMLDivElement,
    data: IEditorData | IElement[],
    options: IEditorOption = {}
  ) {
    // 监听
    this.listener = new Listener()
    // 事件
    this.eventBus = new EventBus<EventBusMap>()
    // 重写
    this.override = new Override()
    // 启动
    const draw = new Draw(...)
    // 命令
    this.command = new Command(new CommandAdapt(draw))
    // 菜单
    const contextMenu = new ContextMenu(draw, this.command)
    // 快捷键
    const shortcut = new Shortcut(draw, this.command)
    // 注册
    this.register = new Register({...})
    // 插件
    const plugin = new Plugin(this)
  }
}
```

**核心职责**:
- 🎛️ **统一入口**: 提供编辑器的唯一实例化入口
- 🔗 **模块协调**: 协调各个核心模块的初始化和交互
- 📡 **事件管理**: 管理全局事件总线和监听器
- 🔌 **插件系统**: 支持插件的注册和使用
- 💾 **生命周期**: 管理编辑器的创建和销毁

### 2. 渲染引擎 (`src/editor/core/draw/Draw.ts`)

**功能**: 基于Canvas的核心渲染引擎，负责所有视觉元素的绘制

```typescript
export class Draw {
  private container: HTMLDivElement
  private pageList: HTMLCanvasElement[]
  private ctxList: CanvasRenderingContext2D[]
  private pageNo: number
  private mode: EditorMode
  private options: DeepRequired<IEditorOption>
  private elementList: IElement[]

  constructor(
    rootContainer: HTMLElement,
    options: DeepRequired<IEditorOption>,
    data: IEditorData,
    listener: Listener,
    eventBus: EventBus<EventBusMap>,
    override: Override
  ) {
    // 初始化渲染环境
    this._formatContainer()
    this.pageContainer = this._createPageContainer()
    this._createPage(0)
    
    // 初始化各种渲染器
    this.textParticle = new TextParticle(this)
    this.tableParticle = new TableParticle(this)
    this.imageParticle = new ImageParticle(this)
    // ...更多渲染器
  }

  public render(payload?: IDrawOption) {
    // 计算文档信息
    if (isCompute) {
      this.position.computePositionList()
      this.rowList = this.computeRowList()
      this.pageRowList = this.computePageList()
    }
    // 绘制元素
    this._immediateRender()
    // 光标重绘
    if (isSetCursor) {
      curIndex = this.setCursor(curIndex)
    }
  }
}
```

**核心职责**:
- 🎨 **Canvas管理**: 管理多页面Canvas的创建和销毁
- 📐 **布局计算**: 计算元素位置、行信息、分页信息
- 🖼️ **元素渲染**: 渲染文本、图片、表格等各种元素
- 📄 **分页处理**: 处理分页模式和连续页模式
- 🔄 **重绘优化**: 实现懒加载和增量渲染

### 3. 命令系统 (`src/editor/core/command/`)

**功能**: 实现命令模式，提供统一的操作接口和撤销重做功能

```typescript
export class Command {
  public executeMode: CommandAdapt['mode']
  public executeCut: CommandAdapt['cut']
  public executeCopy: CommandAdapt['copy']
  public executePaste: CommandAdapt['paste']
  // ...更多命令

  constructor(adapt: CommandAdapt) {
    // 全局命令
    this.executeMode = adapt.mode.bind(adapt)
    this.executeCut = adapt.cut.bind(adapt)
    this.executeCopy = adapt.copy.bind(adapt)
    // ...绑定所有命令
  }
}
```

**核心职责**:
- 🎯 **命令封装**: 将所有编辑操作封装为命令
- 🔄 **撤销重做**: 实现完整的撤销重做机制
- 🛡️ **权限控制**: 根据编辑器模式控制命令执行
- 📝 **操作记录**: 记录和管理操作历史
- 🔗 **适配器模式**: 通过CommandAdapt隔离核心逻辑

### 4. 事件系统 (`src/editor/core/event/`)

**功能**: 处理用户交互事件和系统事件

```typescript
export class CanvasEvent {
  private draw: Draw
  private cursor: Cursor
  private range: RangeManager
  private position: Position

  public register() {
    // 注册各种事件监听器
    this.container.addEventListener('mousedown', this.mousedown.bind(this))
    this.container.addEventListener('mousemove', this.mousemove.bind(this))
    this.container.addEventListener('mouseup', this.mouseup.bind(this))
    this.container.addEventListener('click', this.click.bind(this))
    // ...更多事件
  }

  private mousedown(evt: MouseEvent) {
    // 处理鼠标按下事件
    const position = this.position.getPositionByXY({...})
    this.range.setRange(position.index, position.index)
    this.draw.render({curIndex: position.index})
  }
}
```

**核心职责**:
- 🖱️ **鼠标事件**: 处理点击、拖拽、选择等鼠标操作
- ⌨️ **键盘事件**: 处理文本输入、快捷键等键盘操作
- 📱 **触摸事件**: 支持移动端的触摸操作
- 🎯 **事件分发**: 将事件分发到对应的处理器
- 🔄 **状态同步**: 保持事件状态与编辑器状态同步

## 🔄 模块间交互流程

### 用户操作流程
```
用户操作 --> 事件系统 --> 命令系统 --> 历史管理
                    |           |
                    |           +--> 范围管理
                    |           |
                    |           +--> 位置管理
                    |                    |
                    |                    v
                    +--> 渲染引擎 <-------+
                              |
                              v
                         Canvas绘制
```

### 数据流向
```
原始数据 --> 数据处理 --> 位置计算 --> 布局计算 --> 渲染绘制 --> 用户界面
```

## 🎯 架构特点

### 1. **分层解耦**
- 每层职责明确，依赖关系清晰
- 上层可以调用下层，下层通过事件通知上层
- 便于单元测试和模块替换

### 2. **命令模式**
- 所有操作都封装为命令
- 支持撤销重做、宏录制等高级功能
- 便于权限控制和操作审计

### 3. **事件驱动**
- 模块间通过事件总线通信
- 降低模块间的直接依赖
- 支持插件和扩展的灵活接入

### 4. **渐进式渲染**
- 支持懒加载和增量渲染
- 优化大文档的性能表现
- 提供流畅的用户体验

### 5. **插件化架构**
- 核心功能与扩展功能分离
- 支持第三方插件开发
- 便于功能的定制和扩展

---

## 🔧 编辑器扩展和自定义功能

### 🎯 扩展机制概览

Canvas Editor 提供了多种扩展机制，支持开发者根据需求自定义编辑器功能：

```
┌─────────────────────────────────────────────────────────────┐
│                    扩展机制层次图                            │
├─────────────────────────────────────────────────────────────┤
│  插件系统 (Plugin System)                                   │
│  ├── 命令扩展 (Command Extension)                           │
│  ├── 事件监听 (Event Listeners)                             │
│  └── 方法重写 (Method Override)                             │
├─────────────────────────────────────────────────────────────┤
│  注册系统 (Register System)                                 │
│  ├── 右键菜单 (Context Menu)                                │
│  ├── 快捷键 (Shortcuts)                                     │
│  ├── 控件系统 (Controls)                                    │
│  └── 国际化 (i18n)                                          │
├─────────────────────────────────────────────────────────────┤
│  组件系统 (Component System)                                │
│  ├── 菜单组件 (Menu Components)                             │
│  ├── 工具栏 (Toolbar)                                       │
│  └── 状态栏 (Status Bar)                                    │
└─────────────────────────────────────────────────────────────┘
```

### 🔌 插件系统

#### 1. 插件基础结构

<augment_code_snippet path="src/editor/core/plugin/Plugin.ts" mode="EXCERPT">
````typescript
export class Plugin {
  private editor: Editor

  constructor(editor: Editor) {
    this.editor = editor
  }

  public use<Options>(
    pluginFunction: PluginFunction<Options>,
    options?: Options
  ) {
    pluginFunction(this.editor, options)
  }
}
````
</augment_code_snippet>

#### 2. 创建自定义插件

**基础插件模板**:

```typescript
// 插件函数类型定义
export interface PluginFunction<Options = any> {
  (editor: Editor, options?: Options): void
}

// 自定义插件示例
export function myCustomPlugin(editor: Editor, options?: MyPluginOptions) {
  // 1. 扩展命令系统
  const command = editor.command as any
  command.executeMyCustomFunction = (payload: any) => {
    // 实现自定义功能
    console.log('执行自定义功能:', payload)
    // 触发重新渲染
    editor.command.executeForceUpdate()
  }

  // 2. 监听编辑器事件
  editor.listener.rangeStyleChange = (payload) => {
    console.log('样式变化:', payload)
  }

  // 3. 注册快捷键
  editor.register.shortcutList([
    {
      key: 'F1',
      callback: (command) => {
        command.executeMyCustomFunction('快捷键触发')
      }
    }
  ])

  // 4. 注册右键菜单
  editor.register.contextMenuList([
    {
      key: 'myCustomMenu',
      name: '我的自定义菜单',
      callback: (command) => {
        command.executeMyCustomFunction('右键菜单触发')
      }
    }
  ])
}

// 使用插件
const editor = new Editor(container, data, options)
editor.use(myCustomPlugin, { /* 插件选项 */ })
```

#### 3. 官方插件示例

**Markdown插件**:

<augment_code_snippet path="src/plugins/markdown/index.ts" mode="EXCERPT">
````typescript
export function markdownPlugin(editor: Editor) {
  const command = <CommandWithMarkdown>editor.command
  command.executeInsertMarkdown = (markdown: string) => {
    const elementList = convertMarkdownToElement(markdown)
    command.executeInsertElementList(elementList)
  }
}

function convertMarkdownToElement(markdown: string): IElement[] {
  const lines = markdown.split('\n')
  const elementList: IElement[] = []

  for (const line of lines) {
    if (/^# (.*)/.test(line)) {
      // 处理标题
      const match = line.match(/^# (.*)/)
      elementList.push({
        type: ElementType.TEXT,
        value: match![1],
        title: TitleLevel.FIRST
      })
    } else if (/^\*\*(.*?)\*\*$/.test(line)) {
      // 处理加粗
      const match = line.match(/^\*\*(.*?)\*\*$/)
      elementList.push({
        type: ElementType.TEXT,
        value: match![1],
        bold: true
      })
    }
    // ...更多Markdown语法处理
  }
  return elementList
}
````
</augment_code_snippet>

### 📋 注册系统扩展

#### 1. 自定义右键菜单

<augment_code_snippet path="docs/guide/contextmenu-custom.md" mode="EXCERPT">
````javascript
instance.register.contextMenuList([
  {
    key: 'customSearch',
    name: '搜索：%s', // %s代表选区文本
    shortCut: 'Ctrl + F',
    when: (payload) => {
      // 只在有选中文本时显示
      return payload.editorHasSelection
    },
    callback: (command, context) => {
      const selectedText = context.selectedText
      // 执行搜索功能
      command.executeSearch(selectedText)
    }
  },
  {
    isDivider: true // 分割线
  },
  {
    key: 'customFormat',
    name: '自定义格式化',
    icon: 'format-icon',
    childMenus: [
      {
        key: 'upperCase',
        name: '转大写',
        callback: (command) => {
          // 实现转大写功能
        }
      },
      {
        key: 'lowerCase',
        name: '转小写',
        callback: (command) => {
          // 实现转小写功能
        }
      }
    ]
  }
])
````
</augment_code_snippet>

#### 2. 自定义快捷键

<augment_code_snippet path="src/editor/core/shortcut/keys/richtextKeys.ts" mode="EXCERPT">
````typescript
// 注册自定义快捷键
editor.register.shortcutList([
  {
    key: 'S',
    ctrl: true,
    shift: true,
    callback: (command: Command) => {
      // Ctrl+Shift+S 执行特殊保存
      command.executeCustomSave()
    }
  },
  {
    key: 'F12',
    callback: (command: Command) => {
      // F12 切换预览模式
      command.executeMode(EditorMode.READONLY)
    }
  },
  {
    key: 'ArrowUp',
    ctrl: true,
    callback: (command: Command) => {
      // Ctrl+↑ 移动到文档开头
      command.executeSetRange(0, 0)
    }
  }
])
````
</augment_code_snippet>

#### 3. 国际化扩展

```typescript
// 注册自定义语言包
editor.register.langMap('zh-CN', {
  contextmenu: {
    custom: {
      search: '搜索',
      format: '格式化',
      export: '导出'
    }
  },
  toolbar: {
    custom: {
      myButton: '我的按钮',
      myTool: '我的工具'
    }
  }
})

// 注册英文语言包
editor.register.langMap('en-US', {
  contextmenu: {
    custom: {
      search: 'Search',
      format: 'Format',
      export: 'Export'
    }
  },
  toolbar: {
    custom: {
      myButton: 'My Button',
      myTool: 'My Tool'
    }
  }
})
```

### 🎮 控件系统扩展

#### 1. 控件类型定义

<augment_code_snippet path="src/editor/dataset/enum/Control.ts" mode="EXCERPT">
````typescript
export enum ControlType {
  TEXT = 'text',
  SELECT = 'select',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  DATE = 'date',
  NUMBER = 'number'
}

export enum ControlComponent {
  PREFIX = 'prefix',
  POSTFIX = 'postfix',
  PRE_TEXT = 'preText',
  POST_TEXT = 'postText',
  PLACEHOLDER = 'placeholder',
  VALUE = 'value',
  CHECKBOX = 'checkbox',
  RADIO = 'radio'
}
````
</augment_code_snippet>

#### 2. 自定义控件

**创建自定义控件**:

```typescript
// 定义自定义控件接口
interface ICustomControl extends IControlBasic {
  type: 'custom'
  customType: 'slider' | 'colorPicker' | 'fileUpload'
  customOptions?: {
    min?: number
    max?: number
    step?: number
    accept?: string
    multiple?: boolean
  }
}

// 注册自定义控件
editor.register.controlComponentRender((
  controlComponent,
  control,
  controlElement,
  elementList,
  editor,
  pageContainer
) => {
  if (control.type === 'custom') {
    // 创建自定义控件DOM
    const customElement = document.createElement('div')
    customElement.className = 'custom-control'

    if (control.customType === 'slider') {
      // 创建滑块控件
      const slider = document.createElement('input')
      slider.type = 'range'
      slider.min = control.customOptions?.min?.toString() || '0'
      slider.max = control.customOptions?.max?.toString() || '100'
      slider.step = control.customOptions?.step?.toString() || '1'

      slider.addEventListener('input', (e) => {
        const value = (e.target as HTMLInputElement).value
        // 更新控件值
        editor.command.executeSetControlValue(control.id, value)
      })

      customElement.appendChild(slider)
    } else if (control.customType === 'colorPicker') {
      // 创建颜色选择器
      const colorPicker = document.createElement('input')
      colorPicker.type = 'color'

      colorPicker.addEventListener('change', (e) => {
        const color = (e.target as HTMLInputElement).value
        editor.command.executeSetControlValue(control.id, color)
      })

      customElement.appendChild(colorPicker)
    }

    // 将自定义控件添加到页面容器
    pageContainer.appendChild(customElement)
    return true // 返回true表示已处理
  }

  return false // 返回false使用默认处理
})
```

#### 3. 控件事件监听

<augment_code_snippet path="src/init/index.ts" mode="EXCERPT">
````typescript
// 注册控件变化回调
instance.register.controlChange((payload, control) => {
  console.log('控件值变化:', control.type, payload)

  if (control.type === ControlType.SELECT) {
    const selectedOption = control.select?.options.find(o => o.value === payload)
    if (selectedOption) {
      console.log('选择项:', selectedOption)
      // 根据选择项执行相应逻辑
      handleSelectChange(selectedOption)
    }
  } else if (control.type === ControlType.CHECKBOX) {
    console.log('复选框状态:', payload)
    // 处理复选框变化
    handleCheckboxChange(payload)
  } else if (control.type === ControlType.DATE) {
    console.log('日期变化:', payload)
    // 处理日期变化
    handleDateChange(payload)
  }
})

function handleSelectChange(option: any) {
  // 根据选择项动态更新其他控件
  if (option.value === 'showAdvanced') {
    // 显示高级选项控件
    editor.command.executeSetControlProperty('advancedOptions', { hide: false })
  }
}

function handleCheckboxChange(checked: boolean) {
  // 根据复选框状态控制其他元素
  if (checked) {
    // 启用相关功能
    editor.command.executeSetControlProperty('relatedControl', { disabled: false })
  }
}
````
</augment_code_snippet>

#### 4. 控件样式自定义

```typescript
// 自定义控件样式
const customControlStyles = `
.custom-control {
  display: inline-block;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
}

.custom-control input[type="range"] {
  width: 100px;
  height: 20px;
}

.custom-control input[type="color"] {
  width: 30px;
  height: 20px;
  border: none;
  cursor: pointer;
}

.control-highlight {
  background-color: rgba(255, 255, 0, 0.3);
  border: 2px solid #ffeb3b;
}

.control-error {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}
`

// 注入样式
const styleElement = document.createElement('style')
styleElement.textContent = customControlStyles
document.head.appendChild(styleElement)
```

### 🎨 菜单组件扩展

#### 1. 自定义菜单按钮

**创建新的菜单按钮**:

```typescript
// src/components/menu/custom-button.ts
export function initCustomButton(instance: any) {
  const button = document.querySelector('.menu-item__custom') as HTMLDivElement
  if (!button) return

  button.addEventListener('click', () => {
    // 检查编辑器状态
    if (instance.isReadonly()) {
      console.log('只读模式下不可操作')
      return
    }

    // 执行自定义功能
    try {
      instance.command.executeCustomFunction({
        type: 'myCustomAction',
        data: 'custom data'
      })

      // 更新按钮状态
      button.classList.add('active')
      setTimeout(() => {
        button.classList.remove('active')
      }, 200)

    } catch (error) {
      console.error('自定义按钮执行失败:', error)
    }
  })

  // 监听编辑器状态变化
  instance.listener.rangeStyleChange = (payload: any) => {
    // 根据当前选区状态更新按钮状态
    if (payload.isCustomFormatActive) {
      button.classList.add('active')
    } else {
      button.classList.remove('active')
    }
  }
}
```

#### 2. 自定义选择器组件

```typescript
// src/components/menu/custom-selector.ts
export class CustomSelector {
  private instance: any
  private container: HTMLElement
  private selectorElement!: HTMLSelectElement
  private options: Array<{value: string, label: string}>

  constructor(instance: any, container: HTMLElement, options: Array<{value: string, label: string}>) {
    this.instance = instance
    this.container = container
    this.options = options
    this.render()
    this.init()
  }

  private render() {
    const html = `
      <div class="custom-selector">
        <select class="custom-select">
          ${this.options.map(option =>
            `<option value="${option.value}">${option.label}</option>`
          ).join('')}
        </select>
        <i class="selector-arrow"></i>
      </div>
    `
    this.container.innerHTML = html
    this.selectorElement = this.container.querySelector('.custom-select')!
  }

  private init() {
    this.selectorElement.addEventListener('change', (e) => {
      const selectedValue = (e.target as HTMLSelectElement).value
      this.handleSelectionChange(selectedValue)
    })

    // 监听编辑器状态变化
    this.instance.listener.rangeStyleChange = (payload: any) => {
      this.updateSelectorState(payload)
    }
  }

  private handleSelectionChange(value: string) {
    try {
      // 根据选择值执行相应命令
      switch (value) {
        case 'format1':
          this.instance.command.executeCustomFormat('format1')
          break
        case 'format2':
          this.instance.command.executeCustomFormat('format2')
          break
        default:
          console.log('未知选择:', value)
      }
    } catch (error) {
      console.error('选择器操作失败:', error)
    }
  }

  private updateSelectorState(payload: any) {
    // 根据当前状态更新选择器
    if (payload.currentFormat) {
      this.selectorElement.value = payload.currentFormat
    }
  }

  public setValue(value: string) {
    this.selectorElement.value = value
  }

  public destroy() {
    this.container.innerHTML = ''
  }
}
```

#### 3. 菜单组件注册

```typescript
// src/components/menu/index.ts
import { initCustomButton } from './custom-button'
import { CustomSelector } from './custom-selector'

// 存储自定义组件实例
let customSelector: CustomSelector | null = null

export function initCustomMenuComponents(instance: any) {
  // 初始化自定义按钮
  initCustomButton(instance)

  // 初始化自定义选择器
  const selectorContainer = document.querySelector('.menu-item__custom-selector')
  if (selectorContainer) {
    customSelector = new CustomSelector(instance, selectorContainer as HTMLElement, [
      { value: 'format1', label: '格式1' },
      { value: 'format2', label: '格式2' },
      { value: 'format3', label: '格式3' }
    ])
  }

  // 注册到主菜单初始化函数
  console.log('自定义菜单组件初始化完成')
}

// 在主初始化函数中调用
export function initAllMenuButtons(instance: any) {
  // ...现有按钮初始化

  // 初始化自定义组件
  initCustomMenuComponents(instance)
}
```

### 🛠️ 工具栏和状态栏扩展

#### 1. 自定义工具栏组件

```typescript
// src/components/toolbar/custom-toolbar.ts
export class CustomToolbar {
  private instance: any
  private container: HTMLElement
  private tools: Map<string, HTMLElement>

  constructor(instance: any, container: HTMLElement) {
    this.instance = instance
    this.container = container
    this.tools = new Map()
    this.render()
    this.init()
  }

  private render() {
    const toolbarHtml = `
      <div class="custom-toolbar">
        <div class="toolbar-group">
          <button class="toolbar-btn" data-tool="wordCount">
            <i class="icon-word-count"></i>
            <span>字数统计</span>
          </button>
          <button class="toolbar-btn" data-tool="readingTime">
            <i class="icon-reading-time"></i>
            <span>阅读时间</span>
          </button>
        </div>
        <div class="toolbar-group">
          <button class="toolbar-btn" data-tool="export">
            <i class="icon-export"></i>
            <span>导出</span>
          </button>
          <button class="toolbar-btn" data-tool="share">
            <i class="icon-share"></i>
            <span>分享</span>
          </button>
        </div>
        <div class="toolbar-status">
          <span class="status-item" id="wordCountDisplay">字数: 0</span>
          <span class="status-item" id="readingTimeDisplay">阅读: 0分钟</span>
        </div>
      </div>
    `
    this.container.innerHTML = toolbarHtml
  }

  private init() {
    // 绑定工具按钮事件
    const toolButtons = this.container.querySelectorAll('.toolbar-btn')
    toolButtons.forEach(button => {
      const tool = button.getAttribute('data-tool')
      if (tool) {
        this.tools.set(tool, button as HTMLElement)
        button.addEventListener('click', () => this.handleToolClick(tool))
      }
    })

    // 监听编辑器内容变化
    this.instance.listener.contentChange = () => {
      this.updateStatus()
    }

    // 初始状态更新
    this.updateStatus()
  }

  private handleToolClick(tool: string) {
    switch (tool) {
      case 'wordCount':
        this.showWordCountDialog()
        break
      case 'readingTime':
        this.showReadingTimeDialog()
        break
      case 'export':
        this.handleExport()
        break
      case 'share':
        this.handleShare()
        break
    }
  }

  private updateStatus() {
    const text = this.instance.command.executeGetText()
    const wordCount = this.calculateWordCount(text)
    const readingTime = this.calculateReadingTime(wordCount)

    const wordCountDisplay = document.getElementById('wordCountDisplay')
    const readingTimeDisplay = document.getElementById('readingTimeDisplay')

    if (wordCountDisplay) {
      wordCountDisplay.textContent = `字数: ${wordCount}`
    }
    if (readingTimeDisplay) {
      readingTimeDisplay.textContent = `阅读: ${readingTime}分钟`
    }
  }

  private calculateWordCount(text: string): number {
    // 中文字符和英文单词计数
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length
    return chineseChars + englishWords
  }

  private calculateReadingTime(wordCount: number): number {
    // 假设每分钟阅读200字
    return Math.ceil(wordCount / 200)
  }

  private showWordCountDialog() {
    const text = this.instance.command.executeGetText()
    const stats = {
      characters: text.length,
      charactersNoSpaces: text.replace(/\s/g, '').length,
      words: this.calculateWordCount(text),
      paragraphs: text.split('\n').filter(p => p.trim()).length
    }

    // 创建统计对话框
    const dialog = document.createElement('div')
    dialog.className = 'word-count-dialog'
    dialog.innerHTML = `
      <div class="dialog-content">
        <h3>文档统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">字符数:</span>
            <span class="stat-value">${stats.characters}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">字符数(不含空格):</span>
            <span class="stat-value">${stats.charactersNoSpaces}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">词数:</span>
            <span class="stat-value">${stats.words}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">段落数:</span>
            <span class="stat-value">${stats.paragraphs}</span>
          </div>
        </div>
        <button class="dialog-close">关闭</button>
      </div>
    `

    document.body.appendChild(dialog)

    // 绑定关闭事件
    dialog.querySelector('.dialog-close')?.addEventListener('click', () => {
      document.body.removeChild(dialog)
    })
  }

  private handleExport() {
    // 创建导出选项菜单
    const exportMenu = document.createElement('div')
    exportMenu.className = 'export-menu'
    exportMenu.innerHTML = `
      <div class="menu-content">
        <div class="menu-item" data-format="pdf">导出为PDF</div>
        <div class="menu-item" data-format="word">导出为Word</div>
        <div class="menu-item" data-format="html">导出为HTML</div>
        <div class="menu-item" data-format="markdown">导出为Markdown</div>
      </div>
    `

    // 定位到导出按钮附近
    const exportBtn = this.tools.get('export')!
    const rect = exportBtn.getBoundingClientRect()
    exportMenu.style.position = 'fixed'
    exportMenu.style.top = `${rect.bottom + 5}px`
    exportMenu.style.left = `${rect.left}px`

    document.body.appendChild(exportMenu)

    // 绑定导出格式选择事件
    exportMenu.querySelectorAll('.menu-item').forEach(item => {
      item.addEventListener('click', (e) => {
        const format = (e.target as HTMLElement).getAttribute('data-format')
        this.executeExport(format!)
        document.body.removeChild(exportMenu)
      })
    })

    // 点击外部关闭菜单
    setTimeout(() => {
      document.addEventListener('click', function closeMenu(e) {
        if (!exportMenu.contains(e.target as Node)) {
          if (document.body.contains(exportMenu)) {
            document.body.removeChild(exportMenu)
          }
          document.removeEventListener('click', closeMenu)
        }
      })
    }, 100)
  }

  private executeExport(format: string) {
    try {
      switch (format) {
        case 'pdf':
          this.instance.command.executePrint()
          break
        case 'word':
          // 如果有Word插件
          if (this.instance.command.executeExportDocx) {
            this.instance.command.executeExportDocx({
              fileName: `document_${Date.now()}.docx`
            })
          }
          break
        case 'html':
          const html = this.instance.command.executeGetHTML()
          this.downloadFile(html.main, 'document.html', 'text/html')
          break
        case 'markdown':
          // 如果有Markdown插件
          if (this.instance.command.executeGetMarkdown) {
            const markdown = this.instance.command.executeGetMarkdown()
            this.downloadFile(markdown, 'document.md', 'text/markdown')
          }
          break
      }
    } catch (error) {
      console.error('导出失败:', error)
      alert('导出失败，请检查是否安装了相应的插件')
    }
  }

  private downloadFile(content: string, filename: string, mimeType: string) {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  private handleShare() {
    // 实现分享功能
    if (navigator.share) {
      // 使用Web Share API
      navigator.share({
        title: '文档分享',
        text: this.instance.command.executeGetText().substring(0, 100) + '...',
        url: window.location.href
      })
    } else {
      // 降级到复制链接
      navigator.clipboard.writeText(window.location.href).then(() => {
        alert('链接已复制到剪贴板')
      })
    }
  }
}
```

### 📡 事件监听和回调扩展

#### 1. 编辑器事件监听

```typescript
// 完整的事件监听示例
export function setupEditorListeners(instance: any) {
  // 1. 内容变化监听
  instance.listener.contentChange = () => {
    console.log('文档内容发生变化')
    // 自动保存
    autoSave(instance)
    // 更新状态栏
    updateStatusBar(instance)
  }

  // 2. 选区变化监听
  instance.listener.rangeStyleChange = (payload: any) => {
    console.log('选区样式变化:', payload)
    // 更新工具栏按钮状态
    updateToolbarState(payload)
  }

  // 3. 页面变化监听
  instance.listener.visiblePageNoListChange = (pageList: number[]) => {
    console.log('可见页面变化:', pageList)
    // 更新页面导航
    updatePageNavigation(pageList)
  }

  // 4. 页面大小变化监听
  instance.listener.pageSizeChange = (width: number, height: number) => {
    console.log('页面大小变化:', width, height)
    // 调整布局
    adjustLayout(width, height)
  }

  // 5. 控件变化监听
  instance.register.controlChange((payload: any, control: any) => {
    console.log('控件变化:', control.type, payload)
    // 处理控件联动
    handleControlInteraction(control, payload)
  })

  // 6. 编辑模式变化监听
  instance.register.editorModeChange((mode: EditorMode) => {
    console.log('编辑模式变化:', mode)
    // 更新UI状态
    updateUIForMode(mode)
  })
}

function autoSave(instance: any) {
  // 防抖保存
  clearTimeout(autoSave.timer)
  autoSave.timer = setTimeout(() => {
    const data = instance.command.executeGetValue()
    localStorage.setItem('editor_autosave', JSON.stringify(data))
    console.log('自动保存完成')
  }, 2000)
}

function updateToolbarState(payload: any) {
  // 更新加粗按钮状态
  const boldBtn = document.querySelector('.menu-item__bold')
  if (boldBtn) {
    if (payload.bold) {
      boldBtn.classList.add('active')
    } else {
      boldBtn.classList.remove('active')
    }
  }

  // 更新斜体按钮状态
  const italicBtn = document.querySelector('.menu-item__italic')
  if (italicBtn) {
    if (payload.italic) {
      italicBtn.classList.add('active')
    } else {
      italicBtn.classList.remove('active')
    }
  }
}
```

### 🎯 扩展最佳实践

#### 1. 插件开发规范

```typescript
// 插件开发模板
export interface MyPluginOptions {
  enabled?: boolean
  theme?: 'light' | 'dark'
  customSettings?: Record<string, any>
}

export function myPlugin(editor: Editor, options: MyPluginOptions = {}) {
  const { enabled = true, theme = 'light', customSettings = {} } = options

  // 1. 检查插件是否启用
  if (!enabled) {
    console.log('插件已禁用')
    return
  }

  // 2. 插件初始化
  console.log('初始化插件:', 'myPlugin')

  // 3. 扩展命令系统（使用类型安全的方式）
  const command = editor.command as any
  command.executeMyFeature = (payload: any) => {
    try {
      // 实现功能逻辑
      console.log('执行自定义功能:', payload)

      // 触发重新渲染
      editor.command.executeForceUpdate()

      // 触发自定义事件
      editor.eventBus.emit('myFeatureExecuted', payload)
    } catch (error) {
      console.error('插件功能执行失败:', error)
    }
  }

  // 4. 注册事件监听
  editor.eventBus.on('contentChange', () => {
    // 响应内容变化
    handleContentChange()
  })

  // 5. 注册快捷键
  editor.register.shortcutList([
    {
      key: 'M',
      ctrl: true,
      shift: true,
      callback: (command) => {
        command.executeMyFeature({ source: 'shortcut' })
      }
    }
  ])

  // 6. 注册右键菜单
  editor.register.contextMenuList([
    {
      key: 'myPluginMenu',
      name: '我的插件功能',
      when: (payload) => {
        // 只在特定条件下显示
        return payload.editorHasSelection
      },
      callback: (command, context) => {
        command.executeMyFeature({
          source: 'contextMenu',
          context
        })
      }
    }
  ])

  // 7. 插件清理函数
  const cleanup = () => {
    console.log('清理插件:', 'myPlugin')
    // 移除事件监听
    editor.eventBus.off('contentChange', handleContentChange)
    // 清理DOM元素
    // 清理定时器等
  }

  // 8. 返回插件API（可选）
  return {
    name: 'myPlugin',
    version: '1.0.0',
    cleanup,
    executeMyFeature: command.executeMyFeature
  }
}

function handleContentChange() {
  // 处理内容变化
}
```

#### 2. 错误处理和调试

```typescript
// 带错误处理的插件示例
export function robustPlugin(editor: Editor, options: any = {}) {
  try {
    // 检查编辑器实例
    if (!editor || !editor.command) {
      throw new Error('无效的编辑器实例')
    }

    // 检查必要的API
    const requiredMethods = ['executeForceUpdate', 'executeGetValue']
    for (const method of requiredMethods) {
      if (typeof editor.command[method] !== 'function') {
        console.warn(`缺少必要的方法: ${method}`)
      }
    }

    // 安全地扩展命令
    const command = editor.command as any
    const originalMethod = command.executeForceUpdate

    command.executeRobustFeature = (payload: any) => {
      try {
        // 验证输入参数
        if (!payload || typeof payload !== 'object') {
          throw new Error('无效的参数')
        }

        // 执行功能
        console.log('执行健壮功能:', payload)

        // 安全地调用原始方法
        if (originalMethod) {
          originalMethod.call(command)
        }

      } catch (error) {
        console.error('功能执行失败:', error)
        // 显示用户友好的错误信息
        showErrorMessage('操作失败，请重试')
      }
    }

    // 调试模式
    if (options.debug) {
      console.log('插件调试模式已启用')

      // 监听所有事件
      const originalEmit = editor.eventBus.emit
      editor.eventBus.emit = function(event: string, ...args: any[]) {
        console.log('事件触发:', event, args)
        return originalEmit.call(this, event, ...args)
      }
    }

  } catch (error) {
    console.error('插件初始化失败:', error)
  }
}

function showErrorMessage(message: string) {
  // 创建错误提示
  const errorDiv = document.createElement('div')
  errorDiv.className = 'plugin-error-message'
  errorDiv.textContent = message
  errorDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #f44336;
    color: white;
    padding: 12px 16px;
    border-radius: 4px;
    z-index: 10000;
    animation: slideIn 0.3s ease;
  `

  document.body.appendChild(errorDiv)

  // 3秒后自动移除
  setTimeout(() => {
    if (document.body.contains(errorDiv)) {
      document.body.removeChild(errorDiv)
    }
  }, 3000)
}
```

#### 3. 性能优化

```typescript
// 性能优化的插件示例
export function performantPlugin(editor: Editor, options: any = {}) {
  // 1. 防抖处理
  const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout
    return function executedFunction(...args: any[]) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  // 2. 节流处理
  const throttle = (func: Function, limit: number) => {
    let inThrottle: boolean
    return function executedFunction(...args: any[]) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  // 3. 缓存机制
  const cache = new Map<string, any>()

  const command = editor.command as any
  command.executeOptimizedFeature = (payload: any) => {
    const cacheKey = JSON.stringify(payload)

    // 检查缓存
    if (cache.has(cacheKey)) {
      console.log('使用缓存结果')
      return cache.get(cacheKey)
    }

    // 执行计算
    const result = performExpensiveOperation(payload)

    // 缓存结果
    cache.set(cacheKey, result)

    // 限制缓存大小
    if (cache.size > 100) {
      const firstKey = cache.keys().next().value
      cache.delete(firstKey)
    }

    return result
  }

  // 4. 防抖的内容变化监听
  const debouncedContentChange = debounce(() => {
    console.log('内容变化处理')
    // 执行耗时操作
  }, 500)

  editor.listener.contentChange = debouncedContentChange

  // 5. 节流的滚动监听
  const throttledScroll = throttle(() => {
    console.log('滚动处理')
    // 执行滚动相关操作
  }, 100)

  // 6. 内存清理
  const cleanup = () => {
    cache.clear()
    // 清理其他资源
  }

  // 页面卸载时清理
  window.addEventListener('beforeunload', cleanup)

  return { cleanup }
}

function performExpensiveOperation(payload: any) {
  // 模拟耗时操作
  return `处理结果: ${JSON.stringify(payload)}`
}
```

### 📚 完整扩展示例

#### 综合扩展示例

```typescript
// 完整的编辑器扩展示例
export function comprehensiveExtension(editor: Editor) {
  console.log('🚀 开始初始化综合扩展')

  // 1. 扩展命令系统
  const command = editor.command as any

  // 添加自定义格式化命令
  command.executeCustomFormat = (formatType: string) => {
    const range = editor.command.executeGetRange()
    if (range) {
      switch (formatType) {
        case 'highlight':
          editor.command.executeHighlight('#ffff00')
          break
        case 'code':
          editor.command.executeFont('Consolas')
          editor.command.executeBackgroundColor('#f5f5f5')
          break
      }
    }
  }

  // 2. 注册快捷键
  editor.register.shortcutList([
    {
      key: 'H',
      ctrl: true,
      shift: true,
      callback: (command) => {
        command.executeCustomFormat('highlight')
      }
    },
    {
      key: 'K',
      ctrl: true,
      callback: (command) => {
        command.executeCustomFormat('code')
      }
    }
  ])

  // 3. 注册右键菜单
  editor.register.contextMenuList([
    {
      key: 'customFormat',
      name: '自定义格式',
      childMenus: [
        {
          key: 'highlight',
          name: '高亮显示',
          callback: (command) => {
            command.executeCustomFormat('highlight')
          }
        },
        {
          key: 'codeFormat',
          name: '代码格式',
          callback: (command) => {
            command.executeCustomFormat('code')
          }
        }
      ]
    }
  ])

  // 4. 注册控件
  editor.register.controlChange((payload, control) => {
    if (control.type === 'custom') {
      console.log('自定义控件变化:', payload)
    }
  })

  // 5. 事件监听
  editor.listener.contentChange = () => {
    // 自动保存
    const data = editor.command.executeGetValue()
    localStorage.setItem('editor_backup', JSON.stringify(data))
  }

  console.log('✅ 综合扩展初始化完成')
}

// 使用扩展
const editor = new Editor(container, data, options)
editor.use(comprehensiveExtension)
```

### 🎉 总结

通过以上扩展机制，开发者可以：

1. **🔌 插件系统**: 创建功能丰富的插件
2. **📋 注册系统**: 自定义菜单、快捷键、控件
3. **🎨 组件系统**: 扩展菜单和工具栏组件
4. **📡 事件系统**: 监听和响应编辑器事件
5. **🛡️ 错误处理**: 实现健壮的扩展功能
6. **⚡ 性能优化**: 确保扩展的高性能

这些扩展机制为Canvas Editor提供了强大的定制能力，满足各种复杂的业务需求。
我来分析编辑器的扩展和自定义功能，然后添加到文档中。

现在我来查看文件详细说明1.md的内容，然后添加扩展和自定义功能的说明：

现在我来添加扩展和自定义功能的详细说明：

继续添加控件系统和菜单组件的扩展内容：

继续添加工具栏扩展、事件监听和最佳实践部分：

最后添加最佳实践和完整示例部分：

让我创建一个扩展架构的可视化图表：


完成！我已经成功为文档添加了编辑器扩展和自定义功能的详细说明。新增的内容包括：
📋 新增内容概览
🔧 编辑器扩展和自定义功能
🎯 扩展机制概览
扩展机制层次图
插件系统、注册系统、组件系统的完整架构
🔌 插件系统 (详细实现)
插件基础结构和API
自定义插件创建模板
官方插件示例 (Markdown插件)
命令扩展、事件监听、方法重写
📋 注册系统扩展 (4个主要方面)
自定义右键菜单: 条件显示、子菜单、回调处理
自定义快捷键: 组合键、回调函数、键盘事件
国际化扩展: 多语言支持、语言包注册
🎮 控件系统扩展 (完整控件开发)
控件类型定义和枚举
自定义控件创建 (滑块、颜色选择器等)
控件事件监听和回调处理
控件样式自定义和主题
🎨 菜单组件扩展 (UI组件开发)
自定义菜单按钮创建
自定义选择器组件
菜单组件注册和管理
🛠️ 工具栏和状态栏扩展 (高级UI)
自定义工具栏组件 (字数统计、阅读时间等)
导出功能实现 (PDF、Word、HTML、Markdown)
分享功能和状态显示
📡 事件监听和回调扩展 (事件系统)
完整的事件监听示例
自动保存、状态更新、UI同步
事件防抖和节流处理
🎯 扩展最佳实践 (开发规范)
插件开发规范和模板
错误处理和调试技巧
性能优化策略 (缓存、防抖、节流)
内存管理和资源清理
📚 完整扩展示例 (综合案例)
综合扩展功能实现
实际使用场景和代码示例
🎯 核心特色
📊 可视化架构: 通过Mermaid图表展示扩展机制和流程
💻 完整代码: 每个扩展点都提供完整的实现代码
🔍 详细说明: 从基础到高级的渐进式教程
🛡️ 最佳实践: 包含错误处理、性能优化等实用技巧
🎨 实际案例: 提供真实可用的扩展示例
这些内容为开发者提供了完整的编辑器扩展指南，涵盖了：
🔌 插件开发: 从零开始创建功能插件
🎨 UI定制: 自定义菜单、工具栏、控件
📡 事件处理: 监听和响应编辑器事件
🛡️ 质量保证: 错误处理和性能优化
📚 实战应用: 完整的扩展开发流程
这将大大降低开发者的学习成本，提高扩展开发的效率和质量。