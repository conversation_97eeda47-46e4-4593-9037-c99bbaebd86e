import { CanvasEditor } from '../../editor'
import html from './TableButton.html'
import './TableButton.css'

export class TableButton {
  private dom: HTMLDivElement
  private mainDom: HTMLDivElement
  private tablePanelContainer: HTMLDivElement
  private tableClose: HTMLDivElement
  private tableTitle: HTMLDivElement
  private tablePanel: HTMLDivElement
  private tableCellList: HTMLDivElement[][] = []
  private instance: CanvasEditor
  private colIndex = 0
  private rowIndex = 0

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    
    // 获取各个DOM元素
    this.mainDom = this.dom.querySelector<HTMLDivElement>('.menu-item__table')!
    this.tablePanelContainer = this.dom.querySelector<HTMLDivElement>('.menu-item__table__collapse')!
    this.tableClose = this.dom.querySelector<HTMLDivElement>('.table-close')!
    this.tableTitle = this.dom.querySelector<HTMLDivElement>('.table-select')!
    this.tablePanel = this.dom.querySelector<HTMLDivElement>('.table-panel')!
    
    // 绘制表格选择器的行列
    this.createTableCells()
    
    this.bindEvents()
  }

  // 创建表格选择器的10x10网格
  private createTableCells(): void {
    for (let i = 0; i < 10; i++) {
      const tr = document.createElement('tr')
      tr.classList.add('table-row')
      const trCellList: HTMLDivElement[] = []
      
      for (let j = 0; j < 10; j++) {
        const td = document.createElement('td')
        td.classList.add('table-cel')
        tr.append(td)
        trCellList.push(td)
      }
      
      this.tablePanel.append(tr)
      this.tableCellList.push(trCellList)
    }
  }

  // 移除所有表格单元格选择
  private removeAllTableCellSelect(): void {
    this.tableCellList.forEach(tr => {
      tr.forEach(td => td.classList.remove('active'))
    })
  }

  // 设置标题内容
  private setTableTitle(payload: string): void {
    this.tableTitle.innerText = payload
  }

  // 恢复初始状态
  private recoveryTable(): void {
    // 还原选择样式、标题、选择行列
    this.removeAllTableCellSelect()
    this.setTableTitle('插入')
    this.colIndex = 0
    this.rowIndex = 0
    // 隐藏panel
    this.tablePanelContainer.style.display = 'none'
  }

  // 显示面板并定位到按钮下方
  private showPanel(): void {
    // 先设置基本样式
    this.tablePanelContainer.style.position = 'fixed'
    this.tablePanelContainer.style.zIndex = '999999'
    this.tablePanelContainer.style.display = 'block'

    // 立即计算位置，直接弹出不显示移动效果
    this.positionPanel()
  }

  // 精确定位面板到按钮下方
  private positionPanel(): void {
    const rect = this.mainDom.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 默认位置：按钮下方
    let left = rect.left
    let top = rect.bottom + 4

    // 水平边界检查（表格面板宽度约300px）
    if (left + 300 > viewportWidth) {
      left = viewportWidth - 300 - 10
    }
    if (left < 10) {
      left = 10
    }

    // 垂直边界检查（表格面板高度约250px）
    if (top + 250 > viewportHeight) {
      top = rect.top - 250 - 4
    }
    if (top < 10) {
      top = 10
    }

    // 应用位置
    this.tablePanelContainer.style.left = left + 'px'
    this.tablePanelContainer.style.top = top + 'px'
  }

  // 隐藏所有下拉框（避免多个下拉框同时显示）
  private hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options.visible, .menu-item__table__collapse[style*="block"]')
    allDropdowns.forEach(dropdown => {
      if (dropdown.classList.contains('visible')) {
        dropdown.classList.remove('visible')
      } else {
        (dropdown as HTMLElement).style.display = 'none'
      }
    })
  }

  private bindEvents(): void {
    // 点击表格按钮显示面板
    this.mainDom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 先隐藏所有其他的下拉框
      this.hideAllDropdowns()

      // 显示当前面板并定位
      this.showPanel()
    }

    // 鼠标移动时更新选中的表格大小
    this.tablePanel.onmousemove = (evt) => {
      const celSize = 16
      const rowMarginTop = 10
      const celMarginRight = 6
      const { offsetX, offsetY } = evt
      
      // 移除所有选择
      this.removeAllTableCellSelect()
      
      this.colIndex = Math.ceil(offsetX / (celSize + celMarginRight)) || 1
      this.rowIndex = Math.ceil(offsetY / (celSize + rowMarginTop)) || 1
      
      // 改变选择样式
      this.tableCellList.forEach((tr, trIndex) => {
        tr.forEach((td, tdIndex) => {
          if (tdIndex < this.colIndex && trIndex < this.rowIndex) {
            td.classList.add('active')
          }
        })
      })
      
      // 改变表格标题
      this.setTableTitle(`${this.rowIndex}×${this.colIndex}`)
    }

    // 点击关闭按钮
    this.tableClose.onclick = () => {
      this.recoveryTable()
    }

    // 点击表格面板插入表格
    this.tablePanel.onclick = () => {
      // 应用选择
      this.instance.command.executeInsertTable(this.rowIndex, this.colIndex)
      this.recoveryTable()
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 