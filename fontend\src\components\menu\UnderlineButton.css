.underline-button {
  width: 30px;
  height: 24px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
}

.underline-button:hover {
  background: rgba(25, 55, 88, .04);
}

.underline-button.active {
  background: rgba(25, 55, 88, .08);
}

.underline-button > i {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  display: inline-block;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-image: url('../../assets/images/underline.svg');
}

.underline-button .select {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 1;
}

.underline-button .options {
  width: 128px;
  position: absolute;
  left: 0;
  top: 25px;
  padding: 10px;
  background: #fff;
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgb(56 56 56 / 20%);
  border: 1px solid #e2e6ed;
  border-radius: 2px;
  display: none;
  z-index: 10;
}

.underline-button .options.visible {
  display: block;
}

.underline-button .options li {
  padding: 1px 5px;
  margin: 5px 0;
  user-select: none;
  transition: all .3s;
  cursor: pointer;
  height: 20px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.underline-button .options li:hover {
  background-color: #ebecef;
}

.underline-button .options li.active {
  background-color: #e2e6ed;
}

.underline-button .options li i {
  pointer-events: none;
}

.underline-button .options li[data-decoration-style="solid"] {
  background-image: url('../../assets/images/line-single.svg');
}

.underline-button .options li[data-decoration-style="double"] {
  background-image: url('../../assets/images/line-double.svg')
}

.underline-button .options li[data-decoration-style="dashed"] {
  background-image: url('../../assets/images/line-dash-small-gap.svg');
}

.underline-button .options li[data-decoration-style="dotted"] {
  background-image: url('../../assets/images/line-dot.svg');
}

.underline-button .options li[data-decoration-style="wavy"] {
  background-image: url('../../assets/images/line-wavy.svg');
} 