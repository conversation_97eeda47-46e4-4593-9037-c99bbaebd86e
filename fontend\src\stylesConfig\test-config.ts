/**
 * 样式配置功能测试文件
 * 用于验证字体样式配置功能是否正常工作
 */

import { fontStyleConfigManager } from './FontStyleConfigManager'
import { TitleLevel } from '../editor/dataset/enum/Title'

/**
 * 测试样式配置管理器功能
 */
export async function testStylesConfigManager(): Promise<void> {
  console.log('🧪 开始测试样式配置管理器功能...')

  try {
    // 测试重新加载配置
    await fontStyleConfigManager.reloadConfig()
    console.log('✅ 配置重新加载成功')

    // 测试获取完整配置
    const config = fontStyleConfigManager.getConfig()
    console.log('📋 完整配置:', config)

    // 测试获取正文样式
    const normalStyle = fontStyleConfigManager.getNormalStyle()
    console.log('📝 正文样式:', normalStyle)

    // 测试获取各级标题样式
    const titleLevels = [
      TitleLevel.FIRST,
      TitleLevel.SECOND,
      TitleLevel.THIRD,
      TitleLevel.FOURTH,
      TitleLevel.FIFTH,
      TitleLevel.SIXTH
    ]

    titleLevels.forEach(level => {
      const style = fontStyleConfigManager.getTitleStyle(level)
      console.log(`📖 ${level}样式:`, style)
    })

    // 测试获取标题大小映射
    const sizeMapping = fontStyleConfigManager.getTitleSizeMapping()
    console.log('📏 标题大小映射:', sizeMapping)

    console.log('🎉 配置管理器功能测试完成')

  } catch (error) {
    console.error('❌ 配置管理器功能测试失败:', error)
  }
}

/**
 * 在浏览器控制台中运行测试
 * 可以在浏览器开发者工具中调用此函数进行测试
 */
if (typeof window !== 'undefined') {
  // 将测试函数暴露到全局作用域，方便在控制台调用
  (window as any).testStylesConfig = testStylesConfigManager

  // 简化的测试函数
  const testStylesConfigSimple = async () => {
    console.log('🧪 开始简化测试...')
    try {
      const { fontStyleConfigManager } = await import('./FontStyleConfigManager')
      console.log('✅ 配置管理器导入成功')

      const config = fontStyleConfigManager.getConfig()
      console.log('📋 当前配置:', config)

      await fontStyleConfigManager.reloadConfig()
      console.log('🔄 配置重新加载完成')

      const normalStyle = fontStyleConfigManager.getNormalStyle()
      console.log('📝 正文样式:', normalStyle)

      console.log('🎉 简化测试完成')
    } catch (error) {
      console.error('❌ 简化测试失败:', error)
    }
  }

  // 将简化测试函数暴露到全局作用域
  (window as any).testStylesConfigSimple = testStylesConfigSimple

  console.log('💡 提示：')
  console.log('  - 运行 testStylesConfig() 进行完整测试')
  console.log('  - 运行 testStylesConfigSimple() 进行简化测试')
}
