import { CanvasEditor } from '../../editor'
import html from './PrintButton.html'
import './PrintButton.css'

export class PrintButton {
  private dom: HTMLDivElement
  private isApple: boolean
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.isApple = /Mac OS X/i.test(navigator.userAgent)
    
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    // 设置标题
    this.dom.title = `打印(${this.isApple ? '⌘' : 'Ctrl'}+P)`
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executePrint()
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 