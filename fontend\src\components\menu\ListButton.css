/* 列表按钮容器 */
.menu-item__list {
  position: relative;
}

/* 列表按钮图标 */
.menu-item__list i {
  background-image: url('../../assets/images/list.svg');
}

/* 列表选择下拉框 */
.menu-item__list .options {
  width: 150px; /* 增加宽度以适应内容 */
  position: fixed !important; /* 使用fixed定位确保浮于最上层 */
  z-index: 999999 !important; /* 确保最高层级 */
  background: #fff;
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  pointer-events: none; /* 隐藏时不响应鼠标事件 */
}

/* 下拉框显示状态 */
.menu-item__list .options.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto; /* 显示时恢复鼠标事件 */
}

/* 列表选项内容禁用指针事件（保持原有逻辑） */
.menu-item__list .options>ul>li * {
  pointer-events: none;
}

/* 列表子项缩进 */
.menu-item__list .options>ul>li li {
  margin-left: 8px;
}

/* 复选框列表样式 */
.menu-item__list .options>ul>li[data-list-style='checkbox'] li::marker {
  font-size: 11px;
}

/* 列表选项基础样式 - 只应用于顶级li */
.menu-item__list .options > ul > li {
  padding: 8px 12px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  margin: 2px 0;
}

/* 列表选项悬停效果 - 只应用于顶级li */
.menu-item__list .options > ul > li:hover {
  background: #f5f7fa;
  color: #409eff;
}

/* 列表选项激活状态 - 只应用于顶级li */
.menu-item__list .options > ul > li.active {
  background: #ecf5ff;
  color: #409eff;
  font-weight: 600;
}

/* 确保列表示例的样式不被覆盖 - 使用大图标 */
.menu-item__list .options > ul > li ol,
.menu-item__list .options > ul > li ul {
  margin: 0px 0;
  padding-left: 28px; /* 增加左内边距以容纳大图标 */
  list-style-position: outside;
}

/* 重置所有列表样式，防止双图标 */
.menu-item__list .options > ul > li ul {
  list-style-type: none !important; /* 强制移除默认列表样式 */
}

/* 确保列表示例项的样式正常显示 - 大图标样式 */
.menu-item__list .options > ul > li ol li,
.menu-item__list .options > ul > li ul li {
  padding: 4px 0; /* 增加内边距 */
  margin: 2px 0; /* 增加外边距 */
  font-size: 14px; /* 增大字体 */
  color: #606266; /* 使用更深的颜色 */
  background: none !important;
  cursor: default;
  transition: none;
  line-height: 1.6; /* 增加行高 */
  list-style: inherit;
}

/* 特别处理复选框列表的显示 - 大图标 */
.menu-item__list .options > ul > li[data-list-style='checkbox'] ul {
  list-style-type: none;
  padding-left: 0px;
}

.menu-item__list .options > ul > li[data-list-style='checkbox'] ul li::before {
  content: '☑️';
  font-size: 18px; /* 大图标尺寸 */
  margin-right: 8px;
  display: inline-block;
  width: 22px;
  vertical-align: middle;
}

/* 特别处理空心方块列表的显示 - 大图标 */
.menu-item__list .options > ul > li[data-list-style='square'] ul {
  list-style-type: none;
  padding-left: 5px;
}

.menu-item__list .options > ul > li[data-list-style='square'] ul li::before {
  content: '☐';
  font-size: 18px; /* 大图标尺寸 */
  color: #409eff;
  margin-right: 8px;
  display: inline-block;
  width: 22px;
  vertical-align: middle;
}

/* 有序列表大图标样式 */
.menu-item__list .options > ul > li[data-list-style='decimal'] ol {
  list-style-type: decimal;
  padding-left: 15px;
}

.menu-item__list .options > ul > li[data-list-style='decimal'] ol li {
  font-size: 14px;
  font-weight: normal;
}

.menu-item__list .options > ul > li[data-list-style='decimal'] ol li::marker {
  font-size: 16px; /* 大数字标记 */
  font-weight: 600;
  color: #409eff;
}

/* 实心圆点列表大图标样式 */
.menu-item__list .options > ul > li[data-list-style='disc'] ul {
  list-style-type: none;
  padding-left: 5px;
}

.menu-item__list .options > ul > li[data-list-style='disc'] ul li::before {
  content: '●';
  font-size: 20px; /* 大圆点 */
  color: #409eff;
  margin-right: 8px;
  display: inline-block;
  width: 22px;
  vertical-align: middle;
}

/* 空心圆点列表大图标样式 */
.menu-item__list .options > ul > li[data-list-style='circle'] ul {
  list-style-type: none;
  padding-left: 5px;
}

.menu-item__list .options > ul > li[data-list-style='circle'] ul li::before {
  content: '○';
  font-size: 20px; /* 大圆点 */
  color: #409eff;
  margin-right: 8px;
  display: inline-block;
  width: 22px;
  vertical-align: middle;
}

/* 标签样式 - 增大字体 */
.menu-item__list .options > ul > li label {
  font-weight: 600; /* 加粗标签 */
  font-size: 15px; /* 增大标签字体 */
  margin-bottom: 4px; /* 增加下边距 */
  display: block;
  color: inherit;
}