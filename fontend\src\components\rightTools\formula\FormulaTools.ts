import Editor from '../../../editor'
import { ElementType } from '../../../editor/dataset/enum/Element'
import html from './FormulaTools.html'
import './FormulaTools.css'

/**
 * 公式工具组件类
 * 提供LaTeX公式编辑、预览和插入功能
 */
export class FormulaTools {
  private dom: HTMLDivElement
  private instance: Editor
  private inputElement: HTMLTextAreaElement | null = null
  private previewElement: HTMLDivElement | null = null
  private selectedColor = '#000000' // 默认黑色
  private currentFormula = ''
  private mathjaxScript: HTMLScriptElement | null = null

  /**
   * 构造函数
   * @param instance 编辑器实例
   */
  constructor(instance: Editor) {
    this.instance = instance

    // 创建DOM元素
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html

    // 从模板中获取根元素
    const rootElement = tempDiv.firstElementChild as HTMLDivElement
    if (rootElement) {
      this.dom = rootElement
    } else {
      console.error('FormulaTools: 无法解析HTML模板')
      this.dom = document.createElement('div')
      this.dom.className = 'formula-tools'
      this.dom.setAttribute('editor-component', 'formula-tools')
    }

    // 初始化组件
    this.init()
  }

  /**
   * 初始化公式工具组件
   * 设置基本事件和初始状态
   */
  private init(): void {
    try {
      // 引入MathJax脚本（如果尚未加载）
      this.loadMathJax()

      // 获取DOM元素引用
      this.inputElement = this.dom.querySelector('.formula-input')
      this.previewElement = this.dom.querySelector('.formula-preview-content')

      // 绑定颜色选择事件
      this.bindColorSelectionEvents()

      // 绑定输入变化事件
      this.bindInputEvents()

      // 绑定按钮事件
      this.bindButtonEvents()

      // 设置默认公式
      if (this.inputElement) {
        this.inputElement.value = 'E = mc^2'
        // 触发输入事件以更新预览
        this.updatePreview(this.inputElement.value)
      }
    } catch (error) {
      console.error('FormulaTools初始化失败:', error)
    }
  }

  /**
   * 加载MathJax脚本
   * 用于渲染LaTeX公式
   */
  private loadMathJax(): void {
    // 检查MathJax是否已加载
    if (window.MathJax) {
      return
    }

    try {
      // 创建MathJax配置
      const configScript = document.createElement('script')
      configScript.type = 'text/javascript'
      configScript.text = `
        window.MathJax = {
          tex: {
            inlineMath: [['$', '$'], ['\\\\(', '\\\\)']],
            displayMath: [['$$', '$$'], ['\\\\[', '\\\\]']],
            processEscapes: true,
            packages: ['base', 'ams', 'noerrors', 'noundefined']
          },
          svg: {
            fontCache: 'global',
            scale: 1,
            minScale: 0.5,
            displayAlign: 'center',
            displayIndent: '0'
          },
          startup: {
            typeset: false
          },
          loader: {
            load: ['[tex]/noerrors']
          }
        };
      `
      document.head.appendChild(configScript)

      // 加载MathJax脚本
      this.mathjaxScript = document.createElement('script')
      this.mathjaxScript.type = 'text/javascript'
      this.mathjaxScript.src = 'https://polyfill.io/v3/polyfill.min.js?features=es6'

      // 备用CDN列表
      const cdnList = [
        'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js',
        'https://unpkg.com/mathjax@3/es5/tex-svg.js',
        'https://cdnjs.cloudflare.com/ajax/libs/mathjax/3.2.2/es5/tex-svg.min.js'
      ]

      // 尝试第一个CDN
      this.mathjaxScript.src = cdnList[0]
      this.mathjaxScript.async = true
      this.mathjaxScript.onload = () => {
        // 等待MathJax完全初始化
        setTimeout(() => {
          // 初始渲染
          if (this.inputElement && this.inputElement.value) {
            this.updatePreview(this.inputElement.value)
          }
        }, 100)
      }
      this.mathjaxScript.onerror = (error) => {
        console.error('❌ MathJax加载失败:', error)
        if (this.previewElement) {
          this.previewElement.innerHTML = `<div style="color: red; padding: 10px;">MathJax加载失败，请检查网络连接</div>`
        }
      }
      document.head.appendChild(this.mathjaxScript)
    } catch (error) {
      console.error('加载MathJax失败:', error)
    }
  }

  /**
   * 绑定颜色选择事件
   */
  private bindColorSelectionEvents(): void {
    try {
      const colorItems = this.dom.querySelectorAll('.formula-color-item')

      colorItems.forEach((item) => {
        item.addEventListener('click', () => {
          // 移除其他项的active类
          colorItems.forEach(i => i.classList.remove('active'))

          // 添加当前项的active类
          item.classList.add('active')

          // 更新选中的颜色
          const newColor = item.getAttribute('data-color') || '#000000'
          this.selectedColor = newColor

          // 立即更新预览颜色
          if (this.currentFormula) {
            this.updatePreview(this.currentFormula)
          }
        })
      })
    } catch (error) {
      console.error('绑定颜色选择事件失败:', error)
    }
  }

  /**
   * 绑定输入事件
   */
  private bindInputEvents(): void {
    try {
      if (this.inputElement) {
        // 使用输入事件和防抖处理
        let debounceTimer: number | null = null

        this.inputElement.addEventListener('input', () => {
          const formulaText = this.inputElement?.value || ''
          this.currentFormula = formulaText

          // 防抖处理，避免频繁更新
          if (debounceTimer !== null) {
            clearTimeout(debounceTimer)
          }

          debounceTimer = window.setTimeout(() => {
            this.updatePreview(formulaText)
          }, 500) // 500ms防抖延迟
        })
      }
    } catch (error) {
      console.error('绑定输入事件失败:', error)
    }
  }

  /**
   * 绑定按钮事件
   */
  private bindButtonEvents(): void {
    try {
      // 添加公式按钮
      const addBtn = this.dom.querySelector('.add-formula')
      if (addBtn) {
        addBtn.addEventListener('click', () => {
          this.insertFormula()
        })
      }
    } catch (error) {
      console.error('绑定按钮事件失败:', error)
    }
  }

  /**
   * 更新公式预览
   * @param formula LaTeX公式文本
   */
  private updatePreview(formula: string): void {
    try {
      if (!this.previewElement) return

      // 保存当前公式
      this.currentFormula = formula

      // 准备预览内容
      const wrappedFormula = `$$${formula}$$`
      this.previewElement.innerHTML = wrappedFormula
      this.previewElement.style.color = this.selectedColor

      // 使用MathJax渲染公式
      if (window.MathJax) {
        try {
          // 重置MathJax状态
          if (window.MathJax.texReset) {
            window.MathJax.texReset()
          }
          if (window.MathJax.typesetClear) {
            window.MathJax.typesetClear()
          }

          // 渲染公式 - 使用MathJax 3.x的正确API
          let renderPromise: Promise<any>

          if (window.MathJax.typesetPromise) {
            // MathJax 3.x 标准API
            renderPromise = window.MathJax.typesetPromise([this.previewElement])
          } else if (window.MathJax.startup && window.MathJax.startup.promise) {
            // 等待MathJax完全加载后再渲染
            renderPromise = window.MathJax.startup.promise.then(() => {
              return window.MathJax.typesetPromise ?
                window.MathJax.typesetPromise([this.previewElement]) :
                Promise.resolve(window.MathJax.typeset([this.previewElement]))
            })
          } else if (window.MathJax.typeset) {
            // 同步API包装为Promise
            renderPromise = Promise.resolve(window.MathJax.typeset([this.previewElement]))
          } else {
            // 降级到MathJax 2.x API
            renderPromise = new Promise((resolve) => {
              if (window.MathJax.Hub) {
                window.MathJax.Hub.Queue(['Typeset', window.MathJax.Hub, this.previewElement, resolve])
              } else {
                resolve(null)
              }
            })
          }

          renderPromise
            .then(() => {
              // 检查是否有渲染错误
              const errorElements = this.previewElement?.querySelectorAll('.MathJax_Error, .merror')
              if (errorElements && errorElements.length > 0) {
                this.previewElement!.innerHTML = `<div style="color: red; padding: 10px;">公式语法错误，请检查LaTeX语法</div>`
                return
              }

              // 应用颜色和字体到SVG元素
              const svgElements = this.previewElement?.querySelectorAll('svg')

              if (svgElements && svgElements.length > 0) {
                svgElements.forEach(svg => {
                  // 先应用基本样式
                  svg.style.color = this.selectedColor
                  svg.style.fill = this.selectedColor
                  // 移除width属性，让SVG使用其原始尺寸
                  svg.removeAttribute('width')
                  svg.style.maxWidth = 'none'

                  // 然后尝试应用Times New Roman字体
                  try {
                    this.ensureTimesNewRomanFont(svg)
                  } catch (fontError) {
                    console.warn('字体设置失败，继续使用默认字体:', fontError)
                  }

                  // 为SVG内的所有路径元素设置颜色
                  const pathElements = svg.querySelectorAll('path')
                  pathElements.forEach(pathEl => {
                    try {
                      const pathElement = pathEl as SVGPathElement
                      pathElement.style.fill = this.selectedColor
                      pathElement.style.stroke = this.selectedColor
                    } catch (pathError) {
                      console.warn('路径颜色设置失败:', pathError)
                    }
                  })

                  // 调整容器高度以适应SVG
                  try {
                    const container = this.dom.querySelector('.formula-preview-container') as HTMLElement
                    if (container && svg.height.baseVal.value > 0) {
                      const heightWithPadding = svg.height.baseVal.value + 20
                      container.style.minHeight = `${heightWithPadding}px`
                    }
                  } catch (containerError) {
                    console.warn('容器高度调整失败:', containerError)
                  }
                })
              }
            })
            .catch((err: any) => {
              console.error('公式渲染失败:', err)
              this.previewElement!.innerHTML = `<div style="color: red; padding: 10px;">渲染失败: ${err.message || '未知错误'}</div>`
            })
        } catch (error) {
          console.error('MathJax调用异常:', error)
          this.previewElement!.innerHTML = `<div style="color: red; padding: 10px;">MathJax调用失败</div>`
        }
      } else {
        this.previewElement!.innerHTML = `<div style="color: orange; padding: 10px;">MathJax正在加载中...</div>`
      }
    } catch (error) {
      console.error('更新公式预览失败:', error)
    }
  }

  /**
   * 确保SVG使用Times New Roman字体
   * @param svgElement SVG元素
   */
  private ensureTimesNewRomanFont(svgElement: SVGElement): void {
    try {
      // 设置SVG根元素的字体
      svgElement.style.fontFamily = 'Times New Roman, serif'

      // 为SVG内的所有文本元素设置Times New Roman字体
      const textElements = svgElement.querySelectorAll('text, tspan')
      textElements.forEach(textEl => {
        (textEl as SVGElement).style.fontFamily = 'Times New Roman, serif'
      })

      // 设置SVG的font-family属性
      svgElement.setAttribute('font-family', 'Times New Roman, serif')

      // 为所有使用字体的元素添加font-family属性
      const allElements = svgElement.querySelectorAll('*')
      allElements.forEach(el => {
        if (el.tagName === 'text' || el.tagName === 'tspan') {
          el.setAttribute('font-family', 'Times New Roman, serif')
        }
      })
    } catch (error) {
      console.error('设置SVG字体失败:', error)
    }
  }

  /**
   * 获取当前选择的颜色值
   * @returns 当前选择的颜色值
   */
  private getCurrentSelectedColor(): string {
    try {
      // 查找当前激活的颜色项
      const activeColorItem = this.dom.querySelector('.formula-color-item.active')
      if (activeColorItem) {
        const color = activeColorItem.getAttribute('data-color')
        if (color) {
          return color
        }
      }

      // 如果没有找到激活的颜色项，返回默认黑色
      return '#000000'
    } catch (error) {
      console.error('获取当前选择颜色失败:', error)
      return '#000000'
    }
  }

  /**
   * 将公式插入编辑器
   */
  private insertFormula(): void {
    try {
      if (!this.currentFormula) {
        return
      }

      // 获取当前选择的颜色值
      const currentSelectedColor = this.getCurrentSelectedColor()

      // 创建LaTeX元素，让编辑器自动处理SVG生成和颜色应用
      const latexElement = {
        type: ElementType.LATEX,
        value: this.currentFormula, // LaTeX公式文本
        color: currentSelectedColor, // 使用当前选择的颜色
        font: 'Times New Roman', // 指定字体
        // 添加自定义属性用于标识和后续编辑
        extension: {
          formulaType: 'mathjax',
          latex: this.currentFormula,
          color: currentSelectedColor,
          font: 'Times New Roman',
          timestamp: Date.now()
        }
      }

      // 使用编辑器的executeInsertElementList方法插入公式
      if (this.instance && this.instance.command && typeof this.instance.command.executeInsertElementList === 'function') {
        try {
          this.instance.command.executeInsertElementList([latexElement])
        } catch (insertError) {
          console.error('插入公式失败:', insertError)
        }
      }
    } catch (error) {
      console.error('插入公式失败:', error)
    }
  }

  /**
   * 设置LaTeX公式内容（供外部调用）
   * @param latexContent LaTeX公式内容
   */
  public setLatexContent(latexContent: string): void {
    try {
      if (this.inputElement) {
        this.inputElement.value = latexContent
        this.currentFormula = latexContent

        // 立即更新预览
        this.updatePreview(latexContent)

        // 聚焦到输入框
        this.inputElement.focus()
      }
    } catch (error) {
      console.error('设置LaTeX内容失败:', error)
    }
  }

  /**
   * 获取DOM元素
   * @returns 组件DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
}

// 声明全局MathJax类型
declare global {
  interface Window {
    MathJax: any
  }
}