import './ItalicButton.css'

export class ItalicButton {
  private element: HTMLDivElement;
  private command: any;
  private isApple: boolean;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    this.isApple = typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent)
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.italic-button') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="italic-button" title="斜体(${this.isApple ? '⌘' : 'Ctrl'}+I)">
      <i></i>
    </div>`
  }

  private bindEvents(): void {
    this.element.onclick = () => {
      console.log('italic')
      this.command.executeItalic()
    }
  }

  // 更新按钮状态
  public updateState(isItalic: boolean): void {
    if (isItalic) {
      this.element.classList.add('active')
    } else {
      this.element.classList.remove('active')
    }
  }
} 