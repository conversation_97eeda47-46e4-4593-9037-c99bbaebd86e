.ce-zone-indicator>div {
  padding: 3px 6px;
  color: #000000;
  font-size: 12px;
  background: rgb(218 231 252);
  position: absolute;
  transform-origin: 0 0;
}

.ce-zone-indicator-border__top,
.ce-zone-indicator-border__bottom,
.ce-zone-indicator-border__left,
.ce-zone-indicator-border__right {
  display: block;
  position: absolute;
  z-index: 0;
}

.ce-zone-indicator-border__top {
  border-top: 2px dashed rgb(238, 238, 238);
}

.ce-zone-indicator-border__bottom {
  border-top: 2px dashed rgb(238, 238, 238);
  width: 100%;
}

.ce-zone-indicator-border__left {
  border-left: 2px dashed rgb(238, 238, 238);
}

.ce-zone-indicator-border__right {
  border-right: 2px dashed rgb(238, 238, 238);
}

.ce-zone-tip {
  display: none;
  align-items: center;
  height: 30px;
  white-space: nowrap;
  position: fixed;
  opacity: .9;
  background-color: #000000;
  padding: 0 5px;
  border-radius: 4px;
  z-index: 9;
  transition: all .3s;
  outline: none;
  user-select: none;
  pointer-events: none;
  transform: translate(10px, 10px);
}

.ce-zone-tip.show {
  display: flex;
}

.ce-zone-tip span {
  color: #ffffff;
  font-size: 12px;
}