/* RightTools 右侧工具栏组件样式 - 模拟WinForm TabControl风格 */
.right-tools-container {
  position: fixed;
  right: 0;
  bottom: 30px;
  top: 65px !important;
  z-index: 1500;
  display: block !important;
  width: 340px !important;
  background: #fff;
  box-shadow: -2px 0 10px rgba(0,0,0,0.1);
  overflow: hidden;
  padding-top: 10px;
  padding-left: 1px !important;
}

.right-tools {
  width: 100%;
  height: 100%;
  display: flex !important;
  flex-direction: column;
  background-color: #fff;
  box-sizing: border-box;
  overflow: hidden !important;
}

/* 头部样式 */
.right-tools .right-tools__header {
  height: 38px;
  min-height: 38px; /* 确保最小高度 */
  display: flex !important;
  align-items: center;
  justify-content: flex-start;
  /* border-bottom: 1px solid #e2e6ed; */
  padding: 0 20px;
  background: #f5f7fa;
  box-sizing: border-box;
}

.right-tools .right-tools__header span {
  color: #3d4757;
  font-size: 14px;
  font-weight: bold;
  display: inline-block; /* 确保显示 */
}

.right-tools .right-tools__header__close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.right-tools .right-tools__header i {
  width: 16px;
  height: 16px;
  cursor: pointer;
  display: inline-block;
  background: url(../../assets/images/close.svg) no-repeat;
  transition: all .2s;
}

.right-tools .right-tools__header>div:hover {
  background: rgba(235, 238, 241);
  cursor: pointer;
  border-radius: 50%;
  padding: 4px;
}

/* 一级标签页容器样式 */
.right-tools .right-tools__primary-tabs {
  display: flex;
  flex-direction: row;
  background: #e8f4ff; /* 稍微不同的背景色以区分层级 */
  border-bottom: 1px solid #d0e7ff;
  height: 35px; /* 稍高一些以突出层级 */
  min-height: 35px;
  width: 100%;
  padding: 0;
  margin: 0;
  flex-shrink: 0;
}

/* 一级标签样式 */
.right-tools .right-tools__primary-tab {
  padding: 0 5px; /* 更大的内边距 */
  height: 35px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all .2s;
  position: relative;
  background: #e8f4ff;
  border-right: 1px solid #d0e7ff;
  margin-bottom: -1px;
  user-select: none;
  flex: 1;
}

.right-tools .right-tools__primary-tab span {
  font-size: 14px; /* 稍大的字体 */
  color: #4991f2;
  display: inline-block;
  white-space: nowrap;
  font-weight: 600; /* 更粗的字体以突出重要性 */
}

.right-tools .right-tools__primary-tab.active {
  background:  #ffffff;
  border-bottom: 2px solid #4991f2; /* 更明显的激活指示 */
  z-index: 2;
}

.right-tools .right-tools__primary-tab.active span {
  color: #4991f2;
  font-weight: bold;
  font-size: 15px;
}

.right-tools .right-tools__primary-tab:hover {
  background: #d4edda;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.right-tools .right-tools__primary-tab.active:hover {
  transform: none;
  box-shadow: none;
}

/* 二级标签页容器样式 - TabControl风格 */
.right-tools .right-tools__tabs {
  display: flex;
  flex-direction: row; /* 水平排列标签 */
  background: #f5f7fa;
  /* border-bottom: 1px solid #e2e6ed; */
  height: 30px !important;
  min-height: 30px !important;
  width: 100%;
  padding: 0;
  margin: 0;
  flex-shrink: 0; /* 防止压缩 */
}

.right-tools .right-tools__tab {
  padding: 0 20px; /* 增加左右内边距以适应无图标布局 */
  height: 30px; /* 降低高度从40px到30px */
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all .2s;
  position: relative;
  background: #f0f0f0;
  border-right: 1px solid #e2e6ed;
  /* border-bottom: 1px solid #e2e6ed; */
  margin-bottom: -1px; /* 使活跃标签覆盖底部边框 */
  user-select: none; /* 防止选中文本 */
  flex: 1; /* 均分宽度 */
}

/* 移除图标相关样式 */
.right-tools .right-tools__tab img {
  display: none !important; /* 隐藏所有图标 */
}

.right-tools .right-tools__tab span {
  font-size: 13px; /* 稍微增大字体以提高可读性 */
  color: #000000 !important;
  display: inline-block; /* 确保显示 */
  white-space: nowrap; /* 防止换行 */
  font-weight: 500; /* 增加字体粗细以提高视觉效果 */
}

.right-tools .right-tools__tab.active {
  background: #fff;
  border-bottom: 2px solid #4991f2; /* 更明显的激活指示 */
  z-index: 1;
}

.right-tools .right-tools__tab.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: #4991f2;
}

.right-tools .right-tools__tab.active span {
  color: #4991f2;
  font-weight: bold;
  font-size: 14px; /* 激活状态下字体稍大 */
}

/* 悬停效果 - 增强纯文本标签的交互体验 */
.right-tools .right-tools__tab:hover {
  background: #e8f4ff;
  transform: translateY(-1px); /* 轻微上移效果 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
  transition: all 0.2s ease; /* 平滑过渡 */
}

.right-tools .right-tools__tab:hover span {
  color: #4991f2;
  font-weight: 600; /* 悬停时字体稍粗 */
}

/* 激活状态不受悬停影响 */
.right-tools .right-tools__tab.active:hover {
  transform: none;
  box-shadow: none;
}

/* 内容区域样式 */
.right-tools .right-tools__contents {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #fff;
  border-top: none;
  width: 100%;
  height: calc(100% - 103px); /* 减去header(38px)、primary-tabs(35px)和tabs(30px)的高度 */
}

.right-tools .right-tools__content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 1px;
  box-sizing: border-box;
  overflow-y: auto;
  display: none;
  background: #fff;
}

.right-tools .right-tools__content.active {
  display: block;
}

/* 占位样式 */
.right-tools .right-tools__placeholder {
  color: #909399;
  text-align: center;
  padding: 2px;
  font-size: 14px;
  border: 1px dashed #dcdfe6;
  margin: 10px 0;
  border-radius: 4px;
  background: #fafafa;
  display: block; /* 确保显示 */
}

/* 排版相关样式 - 从目标文件合并 */
.right-tools .typography-section-title {
  font-size: 12px !important;
  color: #333;
  text-decoration: none;
  line-height: normal;
  border-bottom: none !important;
}
