/* 新标题按钮容器 */
.menu-item .menu-item__new-title {
  width: 60px;
  position: relative;
}

/* 新标题选择显示区域 */
.menu-item__new-title .select {
  width: calc(100% - 10px);
  font-size: 12px;
  height: 100%;
  padding-top: 10px;
}

/* 新标题按钮图标 */
.menu-item__new-title i {
  transform: translateX(-5px);
  background-image: url('../../assets/images/title.svg');
}

/* 新标题选择下拉框 */
.menu-item__new-title .options {
  width: 100px;
  position: fixed !important; /* 使用fixed定位确保浮于最上层 */
  z-index: 999999 !important; /* 确保最高层级 */
  background: #fff;
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  pointer-events: none; /* 隐藏时不响应鼠标事件 */
}

/* 下拉框显示状态 */
.menu-item__new-title .options.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto; /* 显示时恢复鼠标事件 */
}

/* 新标题选项样式 */
.menu-item__new-title .options li {
  padding: 8px 12px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 新标题选项悬停效果 */
.menu-item__new-title .options li:hover {
  background: #f5f7fa;
  color: #409eff;
}

/* 新标题选项激活状态 */
.menu-item__new-title .options li.active {
  background: #ecf5ff;
  color: #409eff;
  font-weight: 600;
}
