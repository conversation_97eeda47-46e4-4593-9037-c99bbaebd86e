/* 公式工具整体样式 */
.formula-tools {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 5px;
  box-sizing: border-box;
  font-size: 14px;
  color: #333;
  background-color: #fff;
}

/* 各区域通用样式 */
.formula-input-section,
.formula-color-section,
.formula-preview-section,
.formula-actions-section {
  margin-bottom: 0px;
  padding: 3px;
  border-radius: 6px;
  background-color: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 区域标题样式 */
.formula-input-header,
.formula-color-header,
.formula-preview-header {
  display: flex;
  align-items: center;
  font-weight: bold;
  padding-top: 5px;
  color: #3d4757;
  font-family: 'Times New Roman', serif;
}

/* 提示文字的字体大小设置 formula-hint */
.formula-hint {
  font-size: 10px;
  color: #909399;
  font-weight: normal;
  margin-left: 5px;
}

/* 提示文字的字体大小设置 formula-hint-insert */
.formula-hint-insert {
  font-size: 10px;
  color: #ffffff;
  font-weight: normal;
  margin-left: 5px;
}



/* 图标样式 */
.formula-input-icon,
.formula-color-icon,
.formula-preview-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 公式工具图标样式 - 修复图标路径 */
.formula-input-icon {
  background-image: url('../../assets/images/right-tools/formula-input.svg');
}

.formula-color-icon {
  background-image: url('../../assets/images/right-tools/formula-color.svg');
}

.formula-preview-icon {
  background-image: url('../../assets/images/right-tools/formula-preview.svg');
}

/* 公式输入区域样式 */
.formula-input-container {
  width: 100%;
  height: 200px;
}

.formula-input {
  width: 100%;
  height: 200px;
  padding: 5px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  resize: none;
  font-family: 'Times New Roman', serif !important;
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  transition: border-color 0.2s;
}

.formula-input:focus {
  outline: none;
  border-color: #4991f2;
  box-shadow: 0 0 0 2px rgba(73, 145, 242, 0.2);
}

.formula-input-footer {
  margin-top: 17px;
  text-align: right;
  padding: 0,0,8px,8px;
  margin-bottom: 5px;
}

.formula-input-tips {
  font-size: 12px;
  color: #909399;
  font-family: 'Times New Roman', serif;
}

/* 颜色选择区域样式 */
.formula-color-container {
    display: flex;
    flex-direction: row; /* 修改为 row */
    justify-content: space-evenly; /* 可选：均匀分布每个颜色圆 */
    align-items: center;
    padding: 8px 0;
}

.formula-color-item {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  margin: 5px 0;
}

.formula-color-item:hover {
  transform: scale(1.1);
}

.formula-color-item.active {
  box-shadow: 0 0 0 2px #fff, 0 0 0 4px #4991f2;
}

.formula-color-item.black {
  background-color: #000;
}

.formula-color-item.blue {
  background-color: #006eff;
}

.formula-color-item.orange {
  background-color: #ff6a00;
}

.formula-color-item.green {
  background-color: #00ff6e;
}

.formula-color-item.purple {
  background-color: #ae00ff;
}

.formula-color-item.red {
  background-color: #ff0019;
}

/* 公式预览区域样式 */
.formula-preview-section {
  display: flex;
  flex-direction: column;
  min-height: 50px;
  height: auto;
  /* 允许高度自适应内容 */
}

.formula-preview-container {
  min-height: 250px;
  height: 250px;
  /* 设置固定高度为150px */
  padding: 5px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow-x: auto;
  /* 添加水平滚动条 */
  overflow-y: auto;
  /* 允许垂直滚动 */
}

.formula-preview-content {
  width: 100%;
  height: 150px;
  /* 设置高度为150px */
  text-align: center;
  line-height: 1.5;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  /* 修改为flex布局以居中对齐 */
  white-space: nowrap;
  /* 防止换行 */
}

/* 确保预览内容中的SVG能正确展示 */
.formula-preview-content svg {
  max-width: none;
  /* 允许SVG保持原始宽度 */
  height: auto;
  /* 高度自适应 */
  font-family: 'Times New Roman', serif !important;
  /* 强制使用Times New Roman字体 */
}

/* 确保SVG内的所有文本元素使用Times New Roman字体 */
.formula-preview-content svg text,
.formula-preview-content svg tspan {
  font-family: 'Times New Roman', serif !important;
}

/* 为编辑器中的公式元素设置字体 */
.editor-formula svg,
.editor-formula svg text,
.editor-formula svg tspan {
  font-family: 'Times New Roman', serif !important;
}

/* 底部按钮区域样式 */
.formula-actions-section {
  display: flex;
  flex-direction: column;
  margin-top: 5px;
  padding: 3px;
  background-color: transparent;
  box-shadow: none;
}

.formula-action-btn {
  width: 100%;
  margin: 5px 0;
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  background-color: #4991f2;
  color: #fff;
  font-size: 14px;
  font-family: 'Times New Roman', serif;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.formula-action-btn:hover {
  background-color: #3a7ed5;
}

.formula-action-btn:active {
  background-color: #2d6bb7;
}

/* 公式添加图标样式 - 修复图标路径 */
.formula-add-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 5px;
  background-size: contain;
  background-repeat: no-repeat;
  background-image: url('../../assets/images/right-tools/formula-add.svg');
}