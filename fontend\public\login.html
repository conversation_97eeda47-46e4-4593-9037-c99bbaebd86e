<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Editor - 用户登录</title>
    <style>
        /* 内联CSS样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .background-decoration {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .circle-1 {
            width: 200px;
            height: 200px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .circle-2 {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .circle-3 {
            width: 100px;
            height: 100px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 10px 40px  !important;
            width: 100%;
            max-width: 420px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
        }

        .logo {
            display: inline-block;
        }

        .login-title {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .login-subtitle {
            font-size: 16px;
            color: #6b7280;
            margin: 0;
        }

        .form-group {
            margin-bottom: 4px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .form-input {
            width: 100%;
            padding: 12px 12px 12px 44px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #ffffff;
        }

        .form-input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .form-input::placeholder {
            color: #9ca3af;
        }

        .form-input.error {
            border-color: #ef4444;
        }

        .input-icon {
            position: absolute;
            left: 12px;
            z-index: 2;
            pointer-events: none;
        }

        .captcha-wrapper {
            display: flex;
            gap: 12px;
            align-items: flex-start;
        }

        .captcha-input {
            flex: 1;
        }

        .captcha-display {
            display: flex;
            align-items: center;
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 12px 16px;
            min-width: 100px;
            justify-content: center;
            gap: 8px;
        }

        .captcha-code {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #4f46e5;
            letter-spacing: 2px;
        }

        .captcha-refresh {
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            color: #6b7280;
            transition: all 0.2s;
        }

        .captcha-refresh:hover {
            color: #4f46e5;
            background-color: rgba(79, 70, 229, 0.1);
        }

        .login-button {
            width: 100%;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 4px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
        }

        .login-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .button-text {
            transition: opacity 0.3s ease;
        }

        .button-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .login-button.loading .button-text {
            opacity: 0;
        }

        .login-button.loading .button-loading {
            opacity: 1;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .error-message {
            color: #ef4444;
            font-size: 12px;
            margin-top: 4px;
            min-height: 16px;
        }

        .form-error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            margin-top: 16px;
            display: none;
        }

        .form-error.show {
            display: block;
        }

        .login-footer {
            text-align: center;
        }

        .footer-text {
            font-size: 14px;
            color: #6b7280;
        }

        .footer-link {
            color: #4f46e5;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.2s;
        }

        .footer-link:hover {
            color: #3730a3;
            text-decoration: underline;
        }

        .version-info {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            z-index: 1;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 16px;
            }

            .login-card {
                padding: 24px;
                border-radius: 16px;
            }

            .login-title {
                font-size: 24px;
            }

            .captcha-wrapper {
                flex-direction: column;
            }

            .captcha-display {
                align-self: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 背景装饰 -->
        <div class="background-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>

        <!-- 登录卡片 -->
        <div class="login-card">
            <!-- 头部 -->
            <div class="login-header">
                <div class="logo">
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                        <rect width="40" height="40" rx="8" fill="#4F46E5"/>
                        <path d="M12 16h16M12 20h16M12 24h12" stroke="white" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </div>
                <h1 class="login-title">Book Editor</h1>
            </div>

            <!-- 登录表单 -->
            <form class="login-form" id="loginForm">
                <!-- 用户名输入 -->
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M10 10C12.7614 10 15 7.76142 15 5C15 2.23858 12.7614 0 10 0C7.23858 0 5 2.23858 5 5C5 7.76142 7.23858 10 10 10Z" fill="#9CA3AF"/>
                            <path d="M10 12.5C4.47715 12.5 0 16.9772 0 22.5H20C20 16.9772 15.5228 12.5 10 12.5Z" fill="#9CA3AF"/>
                        </svg>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            class="form-input"
                            placeholder="请输入用户名"
                            required
                            autocomplete="username"
                        >
                    </div>
                    <div class="error-message" id="usernameError"></div>
                </div>

                <!-- 密码输入 -->
                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <div class="input-wrapper">
                        <svg class="input-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M5 10V8C5 5.79086 6.79086 4 9 4H11C13.2091 4 15 5.79086 15 8V10M3 10H17C18.1046 10 19 10.8954 19 12V18C19 19.1046 18.1046 20 17 20H3C1.89543 20 1 19.1046 1 18V12C1 10.8954 1.89543 10 3 10Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="form-input"
                            placeholder="请输入密码"
                            required
                            autocomplete="current-password"
                        >
                    </div>
                    <div class="error-message" id="passwordError"></div>
                </div>

                <!-- 验证码输入 -->
                <div class="form-group">
                    <label for="captcha" class="form-label">验证码</label>
                    <div class="captcha-wrapper">
                        <div class="input-wrapper captcha-input">
                            <svg class="input-icon" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <input
                                type="text"
                                id="captcha"
                                name="captcha"
                                class="form-input"
                                placeholder="请输入验证码"
                                required
                                maxlength="4"
                            >
                        </div>
                        <div class="captcha-display" id="captchaDisplay">
                            <span class="captcha-code" id="captchaCode">----</span>
                            <button type="button" class="captcha-refresh" id="captchaRefresh" title="刷新验证码">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <path d="M1.5 8a6.5 6.5 0 1113 0M1.5 8l3-3M1.5 8l3 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="error-message" id="captchaError"></div>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" class="login-button" id="loginButton">
                    <span class="button-text">登录</span>
                    <div class="button-loading" id="buttonLoading">
                        <div class="loading-spinner"></div>
                    </div>
                </button>

                <!-- 错误提示 -->
                <div class="form-error" id="formError"></div>
            </form>

            <!-- 底部链接 -->
            <div class="login-footer">
                <p class="footer-text">
                    还没有账户？
                    <a href="#" class="footer-link" id="registerLink">立即注册</a>
                </p>
                <p class="footer-text">
                    <a href="#" class="footer-link" id="forgotPasswordLink">忘记密码？</a>
                </p>
            </div>
        </div>

        <!-- 版本信息 -->
        <div class="version-info">
            <p>Canvas Editor v1.0.0</p>
        </div>
    </div>

    <script>
        // 简化的登录逻辑（内联JavaScript）
        class SimpleLoginPage {
            constructor() {
                this.form = document.getElementById('loginForm')
                this.usernameInput = document.getElementById('username')
                this.passwordInput = document.getElementById('password')
                this.captchaInput = document.getElementById('captcha')
                this.captchaCode = document.getElementById('captchaCode')
                this.captchaRefresh = document.getElementById('captchaRefresh')
                this.loginButton = document.getElementById('loginButton')
                this.formError = document.getElementById('formError')

                this.currentCaptchaKey = ''
                this.isLoading = false

                this.bindEvents()
                this.loadCaptcha()
            }

            bindEvents() {
                this.form.addEventListener('submit', this.handleSubmit.bind(this))
                this.captchaRefresh.addEventListener('click', this.loadCaptcha.bind(this))

                document.getElementById('registerLink').addEventListener('click', (e) => {
                    e.preventDefault()
                    alert('注册功能正在开发中...')
                })

                document.getElementById('forgotPasswordLink').addEventListener('click', (e) => {
                    e.preventDefault()
                    alert('忘记密码功能正在开发中...')
                })
            }

            async loadCaptcha() {
                try {
                    this.captchaRefresh.disabled = true

                    const response = await fetch('/api/auth/captcha/')
                    if (!response.ok) {
                        throw new Error('获取验证码失败')
                    }

                    const data = await response.json()
                    this.currentCaptchaKey = data.captcha_key
                    this.captchaCode.textContent = data.captcha_code

                    this.captchaInput.value = ''
                    this.clearError()

                } catch (error) {
                    console.error('加载验证码失败:', error)
                    this.captchaCode.textContent = '----'
                    this.showError('获取验证码失败，请刷新页面重试')
                } finally {
                    this.captchaRefresh.disabled = false
                }
            }

            async handleSubmit(event) {
                event.preventDefault()

                if (this.isLoading) return

                const username = this.usernameInput.value.trim()
                const password = this.passwordInput.value
                const captcha = this.captchaInput.value.trim()

                if (!username || !password || !captcha) {
                    this.showError('请填写所有必填项')
                    return
                }

                this.setLoading(true)
                this.clearError()

                try {
                    const response = await fetch('/api/auth/login/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            username: username,
                            password: password,
                            captcha_key: this.currentCaptchaKey,
                            captcha_code: captcha
                        })
                    })

                    const data = await response.json()

                    if (!response.ok) {
                        throw new Error(data.error || '登录失败')
                    }

                    // 登录成功，保存会话信息
                    const session = {
                        user: data.user,
                        session_token: data.session_token,
                        expires_at: Date.now() + (data.expires_in * 1000),
                        remember_me: false
                    }
                    sessionStorage.setItem('book_editor_session', JSON.stringify(session))

                    this.showSuccess('登录成功，正在跳转...')

                    // 跳转到主页面
                    setTimeout(() => {
                        window.location.href = '/Book-Editor/'
                    }, 1000)

                } catch (error) {
                    console.error('登录失败:', error)
                    this.showError(error.message)
                    await this.loadCaptcha()
                } finally {
                    this.setLoading(false)
                }
            }

            setLoading(loading) {
                this.isLoading = loading
                this.loginButton.disabled = loading

                if (loading) {
                    this.loginButton.classList.add('loading')
                } else {
                    this.loginButton.classList.remove('loading')
                }
            }

            showError(message) {
                this.formError.textContent = message
                this.formError.style.background = '#fef2f2'
                this.formError.style.borderColor = '#fecaca'
                this.formError.style.color = '#dc2626'
                this.formError.classList.add('show')
            }

            showSuccess(message) {
                this.formError.textContent = message
                this.formError.style.background = '#f0f9ff'
                this.formError.style.borderColor = '#0ea5e9'
                this.formError.style.color = '#0369a1'
                this.formError.classList.add('show')
            }

            clearError() {
                this.formError.classList.remove('show')
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new SimpleLoginPage()
        })
    </script>
</body>
</html>
