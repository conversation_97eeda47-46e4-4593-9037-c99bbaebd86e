<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Selection API 修复演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .test-area {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            min-height: 100px;
        }
        
        .button-group {
            margin: 10px 0;
        }
        
        .button-group button {
            margin: 5px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .button-group button:hover {
            background: #0056b3;
        }
        
        .log-area {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .error {
            color: #ff6b6b;
        }
        
        .success {
            color: #51cf66;
        }
        
        .warning {
            color: #ffd43b;
        }
        
        .editor-container {
            border: 1px solid #ccc;
            min-height: 300px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <h1>Selection API 修复演示</h1>
    <p>这个演示页面用于测试和展示Selection API错误修复功能。</p>

    <!-- 基本Selection测试 -->
    <div class="demo-section">
        <h3>1. 基本Selection API测试</h3>
        <div class="test-area" contenteditable="true">
            这是一个可编辑的测试区域。请选择一些文本，然后点击下面的按钮测试Selection API功能。
            你可以尝试选择这段文字的任意部分来测试各种Selection操作。
        </div>
        <div class="button-group">
            <button onclick="testGetSafeRange()">测试安全获取Range</button>
            <button onclick="testHasValidSelection()">测试选择状态检查</button>
            <button onclick="testGetSelectionText()">获取选择文本</button>
            <button onclick="testClearSelection()">清除选择</button>
        </div>
    </div>

    <!-- 错误模拟测试 -->
    <div class="demo-section">
        <h3>2. 错误模拟测试</h3>
        <p>模拟常见的Selection API错误场景：</p>
        <div class="button-group">
            <button onclick="simulateGetRangeAtError()">模拟getRangeAt错误</button>
            <button onclick="simulateNoSelectionError()">模拟无选择错误</button>
            <button onclick="simulateExtensionConflict()">模拟扩展冲突</button>
            <button onclick="testErrorRecovery()">测试错误恢复</button>
        </div>
    </div>

    <!-- 扩展兼容性测试 -->
    <div class="demo-section">
        <h3>3. 扩展兼容性测试</h3>
        <div class="button-group">
            <button onclick="checkExtensionConflicts()">检查扩展冲突</button>
            <button onclick="testSafeWrapper()">测试安全包装器</button>
            <button onclick="reportFakeConflict()">报告虚假冲突</button>
        </div>
    </div>

    <!-- 编辑器集成测试 -->
    <div class="demo-section">
        <h3>4. 编辑器集成测试</h3>
        <div id="editor-container" class="editor-container">
            <!-- 编辑器将在这里初始化 -->
        </div>
        <div class="button-group">
            <button onclick="initEditor()">初始化编辑器</button>
            <button onclick="testEditorSelection()">测试编辑器选择</button>
            <button onclick="getEditorStats()">获取错误统计</button>
        </div>
    </div>

    <!-- 日志显示区域 -->
    <div class="demo-section">
        <h3>5. 日志输出</h3>
        <div id="log-area" class="log-area"></div>
        <div class="button-group">
            <button onclick="clearLog()">清除日志</button>
            <button onclick="exportLog()">导出日志</button>
        </div>
    </div>

    <script type="module">
        // 导入我们的修复工具
        import { 
            getSafeRange, 
            hasValidSelection, 
            getSafeSelectionText,
            safeClearSelection,
            SafeSelectionHandler 
        } from '../src/editor/utils/selection.js'

        import { 
            SelectionErrorHandler,
            safeExecute 
        } from '../src/editor/utils/errorHandler.js'

        import { 
            ExtensionConflictDetector,
            createExtensionSafeWrapper 
        } from '../src/editor/utils/extensionCompat.js'

        // 全局变量
        window.selectionHandler = new SafeSelectionHandler()
        window.errorHandler = SelectionErrorHandler.getInstance()
        window.extensionDetector = ExtensionConflictDetector.getInstance()
        window.editor = null

        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area')
            const timestamp = new Date().toLocaleTimeString()
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : ''
            logArea.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`
            logArea.scrollTop = logArea.scrollHeight
            console.log(`[${type.toUpperCase()}] ${message}`)
        }

        // 测试函数
        window.testGetSafeRange = function() {
            try {
                const range = getSafeRange()
                if (range) {
                    log('✓ 成功获取选择范围', 'success')
                    log(`范围信息: ${range.toString()}`)
                } else {
                    log('⚠ 当前没有选择范围', 'warning')
                }
            } catch (error) {
                log(`✗ 获取范围时发生错误: ${error.message}`, 'error')
            }
        }

        window.testHasValidSelection = function() {
            try {
                const hasSelection = hasValidSelection()
                log(`选择状态: ${hasSelection ? '有选择' : '无选择'}`, hasSelection ? 'success' : 'warning')
            } catch (error) {
                log(`✗ 检查选择状态时发生错误: ${error.message}`, 'error')
            }
        }

        window.testGetSelectionText = function() {
            try {
                const text = getSafeSelectionText()
                if (text) {
                    log(`✓ 选择的文本: "${text}"`, 'success')
                } else {
                    log('⚠ 没有选择任何文本', 'warning')
                }
            } catch (error) {
                log(`✗ 获取选择文本时发生错误: ${error.message}`, 'error')
            }
        }

        window.testClearSelection = function() {
            try {
                safeClearSelection()
                log('✓ 已清除选择状态', 'success')
            } catch (error) {
                log(`✗ 清除选择时发生错误: ${error.message}`, 'error')
            }
        }

        window.simulateGetRangeAtError = function() {
            log('模拟getRangeAt错误...', 'warning')
            
            // 模拟错误场景
            const result = safeExecute(() => {
                throw new DOMException('0 is not a valid index', 'IndexSizeError')
            }, 'fallback-value')
            
            log(`✓ 错误已被安全处理，返回值: ${result}`, 'success')
        }

        window.simulateNoSelectionError = function() {
            log('模拟无选择错误...', 'warning')
            
            const result = safeExecute(() => {
                const selection = window.getSelection()
                if (!selection || selection.rangeCount === 0) {
                    throw new Error('No selection available')
                }
                return selection.getRangeAt(0)
            }, null)
            
            log(`✓ 无选择错误已被处理，返回值: ${result}`, 'success')
        }

        window.simulateExtensionConflict = function() {
            log('模拟扩展冲突...', 'warning')
            window.extensionDetector.reportConflict('test-extension-conflict')
            log('✓ 扩展冲突已报告', 'success')
        }

        window.testErrorRecovery = function() {
            log('测试错误恢复机制...', 'warning')
            
            // 创建一个会出错的函数
            const problematicFunction = createExtensionSafeWrapper(
                () => {
                    throw new DOMException('Simulated extension conflict', 'InvalidStateError')
                },
                'recovered-value'
            )
            
            const result = problematicFunction()
            log(`✓ 错误恢复成功，返回值: ${result}`, 'success')
        }

        window.checkExtensionConflicts = function() {
            const hasConflicts = window.extensionDetector.hasConflicts()
            const conflicts = window.extensionDetector.getConflictingExtensions()
            
            log(`扩展冲突检查: ${hasConflicts ? '发现冲突' : '无冲突'}`, hasConflicts ? 'warning' : 'success')
            if (conflicts.length > 0) {
                log(`冲突扩展: ${conflicts.join(', ')}`)
            }
        }

        window.testSafeWrapper = function() {
            log('测试安全包装器...', 'warning')
            
            const unsafeFunction = (shouldFail) => {
                if (shouldFail) {
                    throw new Error('Intentional error for testing')
                }
                return 'success'
            }
            
            const safeFunction = createExtensionSafeWrapper(unsafeFunction, 'safe-fallback')
            
            // 测试正常情况
            const result1 = safeFunction(false)
            log(`正常执行结果: ${result1}`, 'success')
            
            // 测试错误情况
            const result2 = safeFunction(true)
            log(`错误处理结果: ${result2}`, 'success')
        }

        window.reportFakeConflict = function() {
            window.extensionDetector.reportConflict('demo-fake-extension')
            log('✓ 已报告虚假扩展冲突用于演示', 'success')
        }

        window.initEditor = function() {
            log('正在初始化编辑器...', 'warning')
            // 这里应该初始化canvas-editor实例
            // 由于演示环境限制，我们只是模拟
            log('✓ 编辑器初始化完成（模拟）', 'success')
        }

        window.testEditorSelection = function() {
            log('测试编辑器选择功能...', 'warning')
            // 模拟编辑器选择测试
            window.selectionHandler.handleSelection((range) => {
                if (range) {
                    log('✓ 编辑器选择处理成功', 'success')
                } else {
                    log('⚠ 编辑器中无选择内容', 'warning')
                }
            })
        }

        window.getEditorStats = function() {
            const stats = window.errorHandler.getErrorStats()
            const errorLog = window.errorHandler.getErrorLog()
            
            log('=== 错误统计 ===')
            Object.entries(stats).forEach(([type, count]) => {
                if (count > 0) {
                    log(`${type}: ${count}`, 'warning')
                }
            })
            
            log(`总错误数: ${errorLog.length}`)
            
            if (errorLog.length > 0) {
                log('最近的错误:')
                errorLog.slice(-3).forEach((error, index) => {
                    log(`${index + 1}. ${error.type}: ${error.message}`, 'error')
                })
            }
        }

        window.clearLog = function() {
            document.getElementById('log-area').innerHTML = ''
        }

        window.exportLog = function() {
            const logContent = document.getElementById('log-area').textContent
            const blob = new Blob([logContent], { type: 'text/plain' })
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `selection-api-log-${new Date().toISOString().slice(0, 19)}.txt`
            a.click()
            URL.revokeObjectURL(url)
            log('✓ 日志已导出', 'success')
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('Selection API 修复演示页面已加载', 'success')
            log('请选择测试区域中的文本，然后点击相应按钮进行测试')
            
            // 自动检查扩展冲突
            setTimeout(() => {
                checkExtensionConflicts()
            }, 1000)
        })
    </script>
</body>
</html>
