import { Editor } from '../../editor'
import { Dialog } from '../dialog/Dialog'
import { IElement } from '../../editor/interface/Element'
import html from './CodeBlockButton.html'
import './CodeBlockButton.css'

export class CodeBlockButton {
  private dom: HTMLDivElement
  private instance: Editor

  constructor(instance: Editor) {
    this.instance = instance
    
    // 创建DOM元素
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html
    this.dom = tempDiv.firstElementChild as HTMLDivElement
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.openCodeBlockDialog()
    }
  }

  private openCodeBlockDialog(): void {
    new Dialog({
      title: '代码块',
      data: [
        {
          type: 'textarea',
          name: 'codeblock',
          placeholder: '请输入代码',
          width: 500,
          height: 300
        }
      ],
      onConfirm: payload => {
        const codeblock = payload.find(p => p.name === 'codeblock')?.value
        if (!codeblock) return
        
        // 这里使用prism处理代码高亮，如果在项目中不存在，可能需要添加依赖
        // 简化实现，直接插入代码文本
        const elementList: IElement[] = []
        
        // 添加换行符作为开始
        elementList.push({
          value: '\n'
        })
        
        // 添加代码内容
        elementList.push({
          value: codeblock
        })
        
        this.instance.command.executeInsertElementList(elementList)
      }
    })
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 