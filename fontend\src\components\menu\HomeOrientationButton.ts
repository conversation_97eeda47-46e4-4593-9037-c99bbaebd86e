import { CanvasEditor } from '../../editor'
import { PaperDirection } from '../../editor/dataset/enum/Editor'
import html from './HomeOrientationButton.html'
import './HomeOrientationButton.css'

export class HomeOrientationButton {
  private dom: HTMLDivElement
  private orientationOptionDom: HTMLDivElement
  private instance: CanvasEditor
  private documentClickHandler: (e: MouseEvent) => void

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.orientationOptionDom = this.dom.querySelector<HTMLDivElement>('.options')!
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation()
      
      const isVisible = this.orientationOptionDom.classList.contains('visible')
      this.hideAllDropdowns()
      
      if (!isVisible) {
        this.showDropdown()
      }
    }

    this.orientationOptionDom.onclick = (evt) => {
      evt.stopPropagation()
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        const orientation = li.dataset.orientation!
        this.setOrientation(orientation)
        this.hideDropdown()
      }
    }

    this.documentClickHandler = (e) => {
      const target = e.target as Node
      if (!this.dom.contains(target) && !this.orientationOptionDom.contains(target)) {
        this.hideDropdown()
      }
    }
    document.addEventListener('click', this.documentClickHandler)
  }

  private setOrientation(orientation: string): void {
    const direction = orientation === 'portrait' ? PaperDirection.VERTICAL : PaperDirection.HORIZONTAL
    
    // 设置纸张方向
    this.instance.command.executePaperDirection(direction)
    
    // 更新显示的方向
    const selectSpan = this.dom.querySelector('.select') as HTMLSpanElement
    selectSpan.textContent = orientation === 'portrait' ? '纵向' : '横向'
  }

  private showDropdown(): void {
    this.orientationOptionDom.style.position = 'fixed'
    this.orientationOptionDom.style.zIndex = '999999'
    this.orientationOptionDom.classList.add('visible')
    
    requestAnimationFrame(() => {
      this.positionDropdown()
    })
  }

  private positionDropdown(): void {
    const rect = this.dom.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    let left = rect.left
    let top = rect.bottom + 4
    
    if (left + 100 > viewportWidth) {
      left = viewportWidth - 100 - 10
    }
    if (left < 10) {
      left = 10
    }
    
    if (top + 100 > viewportHeight) {
      top = rect.top - 100 - 4
    }
    if (top < 10) {
      top = 10
    }
    
    this.orientationOptionDom.style.left = left + 'px'
    this.orientationOptionDom.style.top = top + 'px'
  }

  private hideDropdown(): void {
    this.orientationOptionDom.classList.remove('visible')
  }

  private hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options.visible')
    allDropdowns.forEach(dropdown => {
      dropdown.classList.remove('visible')
    })
  }

  public destroy(): void {
    document.removeEventListener('click', this.documentClickHandler)
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}
