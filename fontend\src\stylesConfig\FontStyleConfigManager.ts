/**
 * 字体样式配置管理器
 * 负责加载、管理和更新文本样式配置
 */

import { TitleLevel } from '../editor/dataset/enum/Title'

/**
 * 文本样式接口定义
 */
export interface ITextStyle {
  /** 样式名称 */
  name: string
  /** 字体名称 */
  font: string
  /** 字体大小 */
  size: number
  /** 字体颜色 */
  color: string
  /** 是否加粗 */
  bold: boolean
  /** 是否斜体 */
  italic: boolean
  /** 是否下划线 */
  underline: boolean
  /** 是否删除线 */
  strikeout: boolean
  /** 行高 */
  lineHeight: number
  /** 字符间距 */
  letterSpacing: number
  /** 文本对齐方式 */
  textAlign: 'left' | 'center' | 'right' | 'justify'
  /** 上边距 */
  marginTop: number
  /** 下边距 */
  marginBottom: number
}

/**
 * 配置文件结构接口
 */
export interface IFontStyleConfig {
  /** 文本样式集合 */
  textStyles: {
    normal: ITextStyle
    title1: ITextStyle
    title2: ITextStyle
    title3: ITextStyle
    title4: ITextStyle
    title5: ITextStyle
    title6: ITextStyle
  }
  /** 配置版本 */
  version: string
  /** 最后修改时间 */
  lastModified: string
  /** 配置描述 */
  description: string
}

/**
 * 字体样式配置管理器类
 */
export class FontStyleConfigManager {
  private static instance: FontStyleConfigManager
  private config: IFontStyleConfig | null = null
  private configPath = '/src/stylesConfig/book-fontPrographSet.json'

  /**
   * 获取单例实例
   */
  public static getInstance(): FontStyleConfigManager {
    if (!FontStyleConfigManager.instance) {
      FontStyleConfigManager.instance = new FontStyleConfigManager()
    }
    return FontStyleConfigManager.instance
  }

  /**
   * 私有构造函数，确保单例模式
   */
  private constructor() {
    this.loadConfig()
  }

  /**
   * 加载配置文件
   */
  private async loadConfig(): Promise<void> {
    try {
      // 使用fetch加载JSON配置文件
      const response = await fetch(this.configPath)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      this.config = await response.json()
      console.log('✅ 字体样式配置加载成功:', this.config)
    } catch (error) {
      console.error('❌ 加载字体样式配置失败:', error)
      // 使用默认配置作为后备
      this.config = this.getDefaultConfig()
      console.log('🔄 使用默认配置作为后备')
    }
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): IFontStyleConfig {
    return {
      textStyles: {
        normal: {
          name: '正文',
          font: '微软雅黑',
          size: 14,
          color: '#000000',
          bold: false,
          italic: false,
          underline: false,
          strikeout: false,
          lineHeight: 1.5,
          letterSpacing: 0,
          textAlign: 'left',
          marginTop: 0,
          marginBottom: 6
        },
        title1: {
          name: '标题1',
          font: '微软雅黑',
          size: 26,
          color: '#000000',
          bold: true,
          italic: false,
          underline: false,
          strikeout: false,
          lineHeight: 1.3,
          letterSpacing: 0,
          textAlign: 'left',
          marginTop: 24,
          marginBottom: 12
        },
        title2: {
          name: '标题2',
          font: '微软雅黑',
          size: 24,
          color: '#000000',
          bold: true,
          italic: false,
          underline: false,
          strikeout: false,
          lineHeight: 1.3,
          letterSpacing: 0,
          textAlign: 'left',
          marginTop: 18,
          marginBottom: 10
        },
        title3: {
          name: '标题3',
          font: '微软雅黑',
          size: 22,
          color: '#000000',
          bold: true,
          italic: false,
          underline: false,
          strikeout: false,
          lineHeight: 1.3,
          letterSpacing: 0,
          textAlign: 'left',
          marginTop: 16,
          marginBottom: 8
        },
        title4: {
          name: '标题4',
          font: '微软雅黑',
          size: 20,
          color: '#000000',
          bold: true,
          italic: false,
          underline: false,
          strikeout: false,
          lineHeight: 1.3,
          letterSpacing: 0,
          textAlign: 'left',
          marginTop: 14,
          marginBottom: 7
        },
        title5: {
          name: '标题5',
          font: '微软雅黑',
          size: 18,
          color: '#000000',
          bold: true,
          italic: false,
          underline: false,
          strikeout: false,
          lineHeight: 1.3,
          letterSpacing: 0,
          textAlign: 'left',
          marginTop: 12,
          marginBottom: 6
        },
        title6: {
          name: '标题6',
          font: '微软雅黑',
          size: 16,
          color: '#000000',
          bold: true,
          italic: false,
          underline: false,
          strikeout: false,
          lineHeight: 1.3,
          letterSpacing: 0,
          textAlign: 'left',
          marginTop: 10,
          marginBottom: 5
        }
      },
      version: '1.0.0',
      lastModified: new Date().toISOString(),
      description: '默认文本样式配置'
    }
  }

  /**
   * 获取完整配置
   */
  public getConfig(): IFontStyleConfig | null {
    return this.config
  }

  /**
   * 根据标题级别获取样式
   */
  public getStyleByTitleLevel(level: TitleLevel | null): ITextStyle | null {
    if (!this.config) return null

    const styleMap: Record<TitleLevel, keyof IFontStyleConfig['textStyles']> = {
      [TitleLevel.FIRST]: 'title1',
      [TitleLevel.SECOND]: 'title2',
      [TitleLevel.THIRD]: 'title3',
      [TitleLevel.FOURTH]: 'title4',
      [TitleLevel.FIFTH]: 'title5',
      [TitleLevel.SIXTH]: 'title6'
    }

    if (level === null) {
      return this.config.textStyles.normal
    }

    const styleKey = styleMap[level]
    return this.config.textStyles[styleKey] || null
  }

  /**
   * 获取正文样式
   */
  public getNormalStyle(): ITextStyle | null {
    return this.config?.textStyles.normal || null
  }

  /**
   * 获取标题样式
   */
  public getTitleStyle(level: TitleLevel): ITextStyle | null {
    return this.getStyleByTitleLevel(level)
  }

  /**
   * 重新加载配置文件
   */
  public async reloadConfig(): Promise<void> {
    console.log('🔄 重新加载字体样式配置...')
    await this.loadConfig()
  }

  /**
   * 获取标题大小映射（兼容原有代码）
   */
  public getTitleSizeMapping(): Record<TitleLevel, number> {
    if (!this.config) {
      // 返回默认值
      return {
        [TitleLevel.FIRST]: 26,
        [TitleLevel.SECOND]: 24,
        [TitleLevel.THIRD]: 22,
        [TitleLevel.FOURTH]: 20,
        [TitleLevel.FIFTH]: 18,
        [TitleLevel.SIXTH]: 16
      }
    }

    return {
      [TitleLevel.FIRST]: this.config.textStyles.title1.size,
      [TitleLevel.SECOND]: this.config.textStyles.title2.size,
      [TitleLevel.THIRD]: this.config.textStyles.title3.size,
      [TitleLevel.FOURTH]: this.config.textStyles.title4.size,
      [TitleLevel.FIFTH]: this.config.textStyles.title5.size,
      [TitleLevel.SIXTH]: this.config.textStyles.title6.size
    }
  }
}

/**
 * 导出单例实例
 */
export const fontStyleConfigManager = FontStyleConfigManager.getInstance()
