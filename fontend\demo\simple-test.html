<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Selection API 修复简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-area {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            min-height: 100px;
            border: 1px solid #ddd;
        }
        
        .button-group {
            margin: 10px 0;
        }
        
        .button-group button {
            margin: 5px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .button-group button:hover {
            background: #0056b3;
        }
        
        .log-area {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .error {
            color: #ff6b6b;
        }
        
        .success {
            color: #51cf66;
        }
        
        .warning {
            color: #ffd43b;
        }
    </style>
</head>
<body>
    <h1>Selection API 修复简单测试</h1>
    <p>这个页面用于测试Selection API错误修复功能。</p>

    <div class="test-area" contenteditable="true">
        这是一个可编辑的测试区域。请选择一些文本，然后点击下面的按钮测试Selection API功能。
        你可以尝试选择这段文字的任意部分来测试各种Selection操作。
        如果你安装了浏览器扩展（如翻译扩展），可能会看到我们的修复如何处理潜在的冲突。
    </div>

    <div class="button-group">
        <button onclick="testSafeGetRange()">测试安全获取Range</button>
        <button onclick="testHasValidSelection()">测试选择状态检查</button>
        <button onclick="testGetSelectionText()">获取选择文本</button>
        <button onclick="simulateError()">模拟getRangeAt错误</button>
        <button onclick="clearLog()">清除日志</button>
    </div>

    <div id="log-area" class="log-area"></div>

    <script>
        // 简化版的安全Selection工具函数
        function getSafeRange() {
            try {
                const selection = window.getSelection()
                
                if (!selection) {
                    return null
                }
                
                if (selection.rangeCount === 0) {
                    return null
                }
                
                return selection.getRangeAt(0)
            } catch (error) {
                log('获取选择范围时发生错误: ' + error.message, 'error')
                return null
            }
        }

        function hasValidSelection() {
            try {
                const selection = window.getSelection()
                return !!(selection && selection.rangeCount > 0 && !selection.isCollapsed)
            } catch (error) {
                log('检查选择状态时发生错误: ' + error.message, 'error')
                return false
            }
        }

        function getSafeSelectionText() {
            try {
                const selection = window.getSelection()
                if (!selection || selection.rangeCount === 0) {
                    return ''
                }
                return selection.toString()
            } catch (error) {
                log('获取选择文本时发生错误: ' + error.message, 'error')
                return ''
            }
        }

        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area')
            const timestamp = new Date().toLocaleTimeString()
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : ''
            logArea.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`
            logArea.scrollTop = logArea.scrollHeight
            console.log(`[${type.toUpperCase()}] ${message}`)
        }

        // 测试函数
        function testSafeGetRange() {
            try {
                const range = getSafeRange()
                if (range) {
                    log('✓ 成功获取选择范围', 'success')
                    log(`范围信息: ${range.toString()}`)
                } else {
                    log('⚠ 当前没有选择范围', 'warning')
                }
            } catch (error) {
                log(`✗ 获取范围时发生错误: ${error.message}`, 'error')
            }
        }

        function testHasValidSelection() {
            try {
                const hasSelection = hasValidSelection()
                log(`选择状态: ${hasSelection ? '有选择' : '无选择'}`, hasSelection ? 'success' : 'warning')
            } catch (error) {
                log(`✗ 检查选择状态时发生错误: ${error.message}`, 'error')
            }
        }

        function testGetSelectionText() {
            try {
                const text = getSafeSelectionText()
                if (text) {
                    log(`✓ 选择的文本: "${text}"`, 'success')
                } else {
                    log('⚠ 没有选择任何文本', 'warning')
                }
            } catch (error) {
                log(`✗ 获取选择文本时发生错误: ${error.message}`, 'error')
            }
        }

        function simulateError() {
            log('模拟getRangeAt错误...', 'warning')
            
            try {
                // 模拟常见的错误场景
                const selection = window.getSelection()
                if (selection) {
                    // 清除所有选择，然后尝试获取范围
                    selection.removeAllRanges()
                    
                    // 这应该会抛出错误，但我们的安全函数会处理它
                    const range = getSafeRange()
                    
                    if (range === null) {
                        log('✓ 错误已被安全处理，返回null', 'success')
                    } else {
                        log('⚠ 意外获得了范围对象', 'warning')
                    }
                }
            } catch (error) {
                log(`✗ 模拟错误时发生异常: ${error.message}`, 'error')
            }
        }

        function clearLog() {
            document.getElementById('log-area').innerHTML = ''
        }

        // 设置Selection API保护（简化版）
        function setupSelectionProtection() {
            const originalGetRangeAt = Selection.prototype.getRangeAt

            Selection.prototype.getRangeAt = function(index) {
                try {
                    if (this.rangeCount === 0) {
                        throw new DOMException('No ranges available', 'IndexSizeError')
                    }
                    
                    if (index < 0 || index >= this.rangeCount) {
                        throw new DOMException(`Index ${index} is not valid`, 'IndexSizeError')
                    }
                    
                    return originalGetRangeAt.call(this, index)
                } catch (error) {
                    log('Selection.getRangeAt 错误已被捕获: ' + error.message, 'warning')
                    
                    // 创建一个空的Range作为fallback
                    const range = document.createRange()
                    range.collapse(true)
                    return range
                }
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('Selection API 修复测试页面已加载', 'success')
            log('正在设置Selection API保护...', 'info')
            
            // 设置保护
            setupSelectionProtection()
            
            log('✓ Selection API保护已启用', 'success')
            log('请选择测试区域中的文本，然后点击相应按钮进行测试', 'info')
            
            // 测试是否有扩展冲突的迹象
            setTimeout(() => {
                const scripts = document.querySelectorAll('script[src*="extension"]')
                const links = document.querySelectorAll('link[href*="extension"]')
                
                if (scripts.length > 0 || links.length > 0) {
                    log('⚠ 检测到可能的浏览器扩展注入内容', 'warning')
                } else {
                    log('✓ 未检测到明显的扩展冲突', 'success')
                }
            }, 1000)
        })

        // 监听选择变化
        document.addEventListener('selectionchange', function() {
            // 延迟执行以避免频繁触发
            setTimeout(() => {
                const hasSelection = hasValidSelection()
                if (hasSelection) {
                    const text = getSafeSelectionText()
                    log(`选择变化: "${text.substring(0, 20)}${text.length > 20 ? '...' : ''}"`, 'info')
                }
            }, 100)
        })

        // 全局错误处理
        window.addEventListener('error', function(event) {
            if (event.message && event.message.toLowerCase().includes('selection')) {
                log('捕获到Selection相关错误: ' + event.message, 'error')
            }
        })
    </script>
</body>
</html>
