import Editor, { EditorMode } from '../../editor'
import html from './ViewDesignModeButton.html'
import './ViewEditModeButton.css'

/**
 * 视图菜单设计模式按钮组件
 * 用于切换到设计模式
 * 与底部菜单的编辑模式功能完全一致
 */
export class ViewDesignModeButton {
  private dom: HTMLDivElement
  private instance: Editor

  constructor(instance: Editor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    this.bindEvents()
    this.updateState()
  }

  /**
   * 绑定事件处理器
   */
  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 切换到设计模式
      this.instance.command.executeMode(EditorMode.DESIGN)

      // 更新底部菜单显示
      this.updateBottomMenuDisplay('设计模式')

      // 更新菜单栏权限视觉反馈
      this.updateMenuPermissions(EditorMode.DESIGN)

      // 更新所有编辑模式按钮的状态
      this.updateAllModeButtons()
    }
  }

  /**
   * 更新底部菜单的编辑模式显示
   */
  private updateBottomMenuDisplay(modeName: string): void {
    const bottomModeElement = document.querySelector('.editor-mode')
    if (bottomModeElement) {
      bottomModeElement.textContent = modeName
    }
  }

  /**
   * 更新菜单栏权限视觉反馈
   */
  private updateMenuPermissions(mode: EditorMode): void {
    const isReadonly = mode === EditorMode.READONLY
    const enableMenuList = ['search', 'print']

    document.querySelectorAll<HTMLDivElement>('.menu-item>div').forEach(dom => {
      const menu = dom.dataset.menu

      // 视图编辑模式按钮在只读模式下不应被禁用，用户需要这些按钮来切换模式
      if (dom.classList.contains('view-editor-mode-btn')) {
        return // 跳过视图编辑模式按钮的禁用处理
      }

      if (isReadonly && (!menu || !enableMenuList.includes(menu))) {
        dom.classList.add('disable')
      } else {
        dom.classList.remove('disable')
      }
    })
  }

  /**
   * 更新所有编辑模式按钮的状态
   */
  private updateAllModeButtons(): void {
    // 移除所有按钮的激活状态
    document.querySelectorAll('.view-editor-mode-btn').forEach(btn => {
      btn.classList.remove('active')
    })

    // 激活当前按钮
    this.dom.classList.add('active')
  }

  /**
   * 更新按钮状态
   */
  private updateState(): void {
    const currentMode = this.instance.command.getOptions().mode
    if (currentMode === EditorMode.DESIGN) {
      this.dom.classList.add('active')
    } else {
      this.dom.classList.remove('active')
    }
  }

  /**
   * 获取DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
}