import { CanvasEditor } from '../../editor'
import html from './SearchButton.html'
import './SearchButton.css'

export class SearchButton {
  private dom: HTMLDivElement
  private searchDom: HTMLDivElement
  private searchCollapseDom: HTMLDivElement
  private searchInputDom: HTMLInputElement
  private replaceInputDom: HTMLInputElement
  private searchResultDom: HTMLLabelElement
  private isApple: boolean
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.isApple = /Mac OS X/i.test(navigator.userAgent)
    
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    
    // 获取各个DOM元素
    this.searchDom = this.dom.querySelector<HTMLDivElement>('.menu-item__search')!
    this.searchCollapseDom = this.dom.querySelector<HTMLDivElement>('.menu-item__search__collapse')!
    this.searchInputDom = this.searchCollapseDom.querySelector<HTMLInputElement>('.menu-item__search__collapse__search input')!
    this.replaceInputDom = this.searchCollapseDom.querySelector<HTMLInputElement>('.menu-item__search__collapse__replace input')!
    this.searchResultDom = this.searchCollapseDom.querySelector<HTMLLabelElement>('.search-result')!
    
    // 设置标题
    this.searchDom.title = `搜索与替换(${this.isApple ? '⌘' : 'Ctrl'}+F)`
    
    this.bindEvents()
  }

  private bindEvents(): void {
    // 搜索按钮点击事件
    this.searchDom.onclick = () => {
      this.searchCollapseDom.style.display = 'block'
      
      // 调整位置
      const bodyRect = document.body.getBoundingClientRect()
      const searchRect = this.searchDom.getBoundingClientRect()
      const searchCollapseRect = this.searchCollapseDom.getBoundingClientRect()
      
      if (searchRect.left + searchCollapseRect.width > bodyRect.width) {
        this.searchCollapseDom.style.right = '0px'
        this.searchCollapseDom.style.left = 'unset'
      } else {
        this.searchCollapseDom.style.right = 'unset'
      }
      
      this.searchInputDom.focus()
    }
    
    // 关闭搜索面板
    this.searchCollapseDom.querySelector<HTMLSpanElement>('span')!.onclick = () => {
      this.searchCollapseDom.style.display = 'none'
      this.searchInputDom.value = ''
      this.replaceInputDom.value = ''
      this.instance.command.executeSearch(null)
      this.setSearchResult()
    }
    
    // 输入搜索内容
    this.searchInputDom.oninput = () => {
      this.instance.command.executeSearch(this.searchInputDom.value || null)
      this.setSearchResult()
    }
    
    // 回车搜索
    this.searchInputDom.onkeydown = (evt) => {
      if (evt.key === 'Enter') {
        this.instance.command.executeSearch(this.searchInputDom.value || null)
        this.setSearchResult()
      }
    }
    
    // 替换按钮点击
    this.searchCollapseDom.querySelector<HTMLButtonElement>('button')!.onclick = () => {
      const searchValue = this.searchInputDom.value
      const replaceValue = this.replaceInputDom.value
      
      if (searchValue && replaceValue && searchValue !== replaceValue) {
        this.instance.command.executeReplace(replaceValue)
      }
    }
    
    // 上一个搜索结果
    this.searchCollapseDom.querySelector<HTMLDivElement>('.arrow-left')!.onclick = () => {
      this.instance.command.executeSearchNavigatePre()
      this.setSearchResult()
    }
    
    // 下一个搜索结果
    this.searchCollapseDom.querySelector<HTMLDivElement>('.arrow-right')!.onclick = () => {
      this.instance.command.executeSearchNavigateNext()
      this.setSearchResult()
    }
  }
  
  private setSearchResult(): void {
    const result = this.instance.command.getSearchNavigateInfo()
    if (result) {
      const { index, count } = result
      this.searchResultDom.innerText = `${index}/${count}`
    } else {
      this.searchResultDom.innerText = ''
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 