import Editor from '../../editor'
import html from './Footer.html'
import './Footer.css'

// 导入所有底部菜单子组件
import { CatalogModeButton } from './CatalogModeButton'
import { PageModeButton } from './PageModeButton'
import { PageScaleMinusButton } from './PageScaleMinusButton'
import { PageScalePercentageButton } from './PageScalePercentageButton'
import { PageScaleAddButton } from './PageScaleAddButton'
import { PaperSizeButton } from './PaperSizeButton'
import { PaperDirectionButton } from './PaperDirectionButton'
import { PaperMarginButton } from './PaperMarginButton'
import { FullscreenButton } from './FullscreenButton'
import { EditorOptionButton } from './EditorOptionButton'
import { EditorModeButton } from './EditorModeButton'

export class Footer {
  private dom: HTMLDivElement
  private instance: Editor

  constructor(instance: Editor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    // 初始化所有子组件
    this.initComponents()
  }

  private initComponents(): void {
    // 初始化目录模式按钮
    const catalogModeBtn = new CatalogModeButton(this.instance)
    const catalogModeElement = this.dom.querySelector('.catalog-mode')!
    catalogModeElement.replaceWith(catalogModeBtn.getElement())

    // 初始化页面模式按钮
    const pageModeBtn = new PageModeButton(this.instance)
    const pageModeElement = this.dom.querySelector('.page-mode')!
    pageModeElement.replaceWith(pageModeBtn.getElement())

    // 初始化页面缩放相关按钮
    const pageScaleMinusBtn = new PageScaleMinusButton(this.instance)
    const pageScaleMinusElement = this.dom.querySelector('.page-scale-minus')!
    pageScaleMinusElement.replaceWith(pageScaleMinusBtn.getElement())

    const pageScalePercentageBtn = new PageScalePercentageButton(this.instance)
    const pageScalePercentageElement = this.dom.querySelector('.page-scale-percentage')!
    pageScalePercentageElement.replaceWith(pageScalePercentageBtn.getElement())

    const pageScaleAddBtn = new PageScaleAddButton(this.instance)
    const pageScaleAddElement = this.dom.querySelector('.page-scale-add')!
    pageScaleAddElement.replaceWith(pageScaleAddBtn.getElement())

    // 初始化纸张相关按钮
    const paperSizeBtn = new PaperSizeButton(this.instance)
    const paperSizeElement = this.dom.querySelector('.paper-size')!
    paperSizeElement.replaceWith(paperSizeBtn.getElement())

    const paperDirectionBtn = new PaperDirectionButton(this.instance)
    const paperDirectionElement = this.dom.querySelector('.paper-direction')!
    paperDirectionElement.replaceWith(paperDirectionBtn.getElement())

    const paperMarginBtn = new PaperMarginButton(this.instance)
    const paperMarginElement = this.dom.querySelector('.paper-margin')!
    paperMarginElement.replaceWith(paperMarginBtn.getElement())

    // 初始化全屏按钮
    const fullscreenBtn = new FullscreenButton(this.instance)
    const fullscreenElement = this.dom.querySelector('.fullscreen')!
    fullscreenElement.replaceWith(fullscreenBtn.getElement())

    // 初始化编辑器选项按钮
    const editorOptionBtn = new EditorOptionButton(this.instance)
    const editorOptionElement = this.dom.querySelector('.editor-option')!
    editorOptionElement.replaceWith(editorOptionBtn.getElement())

    // 初始化编辑器模式按钮
    const editorModeBtn = new EditorModeButton(this.instance)
    const editorModeElement = this.dom.querySelector('.editor-mode')!
    editorModeElement.replaceWith(editorModeBtn.getElement())
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}