# Canvas Editor 核心架构和主要模块详细报告

## 📋 项目概述

Canvas Editor 是一个基于 HTML5 Canvas 的现代化富文本编辑器，采用模块化架构设计，提供了完整的文档编辑功能。本报告详细介绍编辑器的核心架构、主要模块的组成、逻辑关系、功能参数、调用方法和具体案例。

## 🏗️ 核心架构概览

### 架构设计原则

Canvas Editor 采用分层架构设计，遵循以下核心原则：

1. **分层解耦**: 每层职责明确，依赖关系清晰
2. **命令模式**: 所有操作都封装为命令，支持撤销重做
3. **事件驱动**: 模块间通过事件总线通信，降低耦合度
4. **插件化**: 核心功能与扩展功能分离，支持第三方插件
5. **渐进式渲染**: 支持懒加载和增量渲染，优化性能

### 架构层次结构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Editor    │  │   Plugin    │  │  Register   │         │
│  │   主入口     │  │   插件系统   │  │   注册系统   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    控制层 (Control Layer)                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Command   │  │ ContextMenu │  │  Shortcut   │         │
│  │   命令系统   │  │  右键菜单    │  │   快捷键    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    业务层 (Business Layer)                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    Draw     │  │  Position   │  │    Zone     │         │
│  │   渲染引擎   │  │  位置管理    │  │   区域管理   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    基础层 (Foundation Layer)                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  EventBus   │  │  Listener   │  │  Override   │         │
│  │   事件总线   │  │   监听器    │  │   重写系统   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 主要模块详解

### 1. 编辑器主入口 (Editor)

**位置**: `src/editor/index.ts`
**功能**: 编辑器的主入口类，负责初始化和协调各个模块

#### 核心属性和方法

```typescript
export default class Editor {
  public command: Command           // 命令系统
  public listener: Listener         // 事件监听器
  public eventBus: EventBus        // 事件总线
  public register: Register        // 注册系统
  public use: Plugin['use']        // 插件使用方法
  public destroy: () => void       // 销毁方法

  constructor(
    container: HTMLDivElement,      // 容器元素
    data: IEditorData | IElement[], // 编辑器数据
    options: IEditorOption = {}     // 配置选项
  )
}
```

#### 初始化流程

```typescript
// 1. 数据预处理
const editorOptions = deepClone(defaultOptions)
Object.assign(editorOptions, options)

// 2. 格式化元素列表
const headerElementList = formatElementList(header, {
  editorOptions: editorOptions
})
const mainElementList = formatElementList(main, {
  editorOptions: editorOptions
})
const footerElementList = formatElementList(footer, {
  editorOptions: editorOptions
})

// 3. 初始化核心模块
this.listener = new Listener()
this.eventBus = new EventBus<EventBusMap>()
this.override = new Override()

// 4. 启动渲染引擎
const draw = new Draw(
  container,
  editorOptions,
  {
    header: headerElementList,
    main: mainElementList,
    footer: footerElementList
  },
  this.listener,
  this.eventBus,
  this.override
)

// 5. 初始化控制系统
this.command = new Command(new CommandAdapt(draw))
const contextMenu = new ContextMenu(draw, this.command)
const shortcut = new Shortcut(draw, this.command)

// 6. 注册系统
this.register = new Register({
  contextMenu,
  shortcut,
  i18n: draw.getI18n()
})

// 7. 插件系统
const plugin = new Plugin(this)
this.use = plugin.use.bind(plugin)
```

#### 使用示例

```typescript
import Editor from '@hufe921/canvas-editor'

// 基础初始化
const container = document.querySelector('.canvas-editor') as HTMLDivElement
const data = [{ value: 'Hello World' }]
const options = {
  mode: EditorMode.EDIT,
  defaultFont: 'Microsoft YaHei',
  defaultSize: 16
}

const instance = new Editor(container, data, options)

// 使用命令系统
instance.command.executeBold()
instance.command.executeFont('Arial')
instance.command.executeSize(18)

// 监听事件
instance.listener.contentChange = () => {
  console.log('内容发生变化')
}

// 注册插件
instance.use(myCustomPlugin, { option1: 'value1' })

// 销毁编辑器
instance.destroy()
```

### 2. 渲染引擎 (Draw)

**位置**: `src/editor/core/draw/Draw.ts`
**功能**: 核心渲染引擎，负责Canvas管理、布局计算和元素渲染

#### 核心属性

```typescript
export class Draw {
  private container: HTMLDivElement           // 容器元素
  private pageContainer: HTMLDivElement       // 页面容器
  private pageList: HTMLCanvasElement[]       // Canvas页面列表
  private ctxList: CanvasRenderingContext2D[] // 渲染上下文列表
  private pageNo: number                      // 当前页码
  private mode: EditorMode                    // 编辑模式
  private options: DeepRequired<IEditorOption> // 配置选项
  private position: Position                  // 位置管理器
  private zone: Zone                         // 区域管理器
  private elementList: IElement[]            // 元素列表
  private listener: Listener                 // 监听器
  private eventBus: EventBus<EventBusMap>    // 事件总线
}
```

#### 主要方法

```typescript
// 渲染方法
public render(payload?: IDrawOption): void
public renderPage(pageNo: number): void

// 布局计算
public computeRowList(payload?: IComputeRowListPayload): IRow[][]
public computePageList(): void

// 元素操作
public insertElementList(elementList: IElement[]): number
public spliceElementList(start: number, deleteCount: number, ...items: IElement[]): IElement[]

// 获取信息
public getValue(options?: IGetValueOption): IEditorResult
public getImage(options?: IGetImageOption): Promise<string[]>
public getHTML(): string

// 页面管理
public getPageNo(): number
public getPageCount(): number
public getInnerWidth(): number
public getInnerHeight(): number
```

#### 渲染流程

```typescript
// 渲染主流程
public render(payload: IDrawOption = {}) {
  const {
    isSetCursor = true,
    isSubmitHistory = true,
    isCompute = true,
    isLazy = true,
    isInit = false
  } = payload

  // 1. 计算布局
  if (isCompute) {
    this.computeRowList()
    this.computePageList()
  }

  // 2. 计算位置
  this.position.computePositionList()

  // 3. 渲染页面
  if (isLazy) {
    this.lazyRender()
  } else {
    this.immediateRender()
  }

  // 4. 设置光标
  if (isSetCursor) {
    this.cursor.drawCursor()
  }

  // 5. 提交历史
  if (isSubmitHistory) {
    this.historyManager.submitHistory()
  }
}
```

#### 使用示例

```typescript
// 获取Draw实例（通常在插件或扩展中使用）
const draw = editor.command.getDraw() // 假设有这个方法

// 强制重新渲染
draw.render({
  isSetCursor: true,
  isSubmitHistory: false,
  isCompute: true
})

// 获取文档数据
const result = draw.getValue({
  pageNo: 0,
  includeHeader: true,
  includeFooter: true
})

// 获取页面图片
const images = await draw.getImage({
  pixelRatio: 2,
  mode: 'png'
})

// 插入元素
const newElements = [
  { value: '新插入的文本', bold: true },
  { value: '\n' }
]
draw.insertElementList(newElements)
```

### 3. 命令系统 (Command)

**位置**: `src/editor/core/command/Command.ts`
**功能**: 实现命令模式，提供统一的操作接口和撤销重做功能

#### 架构设计

```typescript
// Command类作为外观模式的入口
export class Command {
  // 通过CommandAdapt中转避免直接暴露编辑器上下文
  public executeMode: CommandAdapt['mode']
  public executeCut: CommandAdapt['cut']
  public executeCopy: CommandAdapt['copy']
  public executePaste: CommandAdapt['paste']
  // ... 更多命令

  constructor(adapt: CommandAdapt) {
    // 绑定所有命令方法
    this.executeMode = adapt.mode.bind(adapt)
    this.executeCut = adapt.cut.bind(adapt)
    this.executeCopy = adapt.copy.bind(adapt)
    // ...
  }
}

// CommandAdapt实现具体的命令逻辑
export class CommandAdapt {
  private draw: Draw
  private range: RangeManager
  private position: Position
  private historyManager: HistoryManager
  // ... 其他依赖

  constructor(draw: Draw) {
    this.draw = draw
    this.range = draw.getRange()
    this.position = draw.getPosition()
    // ... 初始化依赖
  }
}
```

#### 主要命令分类

**1. 基础编辑命令**

```typescript
// 剪切、复制、粘贴
instance.command.executeCut()
instance.command.executeCopy()
instance.command.executePaste()

// 撤销、重做
instance.command.executeUndo()
instance.command.executeRedo()

// 全选、删除
instance.command.executeSelectAll()
instance.command.executeBackspace()
```

**2. 格式化命令**

```typescript
// 字体格式
instance.command.executeBold()                    // 加粗
instance.command.executeItalic()                 // 斜体
instance.command.executeUnderline()              // 下划线
instance.command.executeStrikeout()              // 删除线
instance.command.executeFont('Arial')            // 字体
instance.command.executeSize(18)                 // 字号
instance.command.executeColor('#FF0000')         // 字体颜色
instance.command.executeHighlight('#FFFF00')     // 背景色
```

**3. 段落命令**

```typescript
// 段落对齐
instance.command.executeRowFlex(RowFlex.LEFT)    // 左对齐
instance.command.executeRowFlex(RowFlex.CENTER)  // 居中
instance.command.executeRowFlex(RowFlex.RIGHT)   // 右对齐
instance.command.executeRowFlex(RowFlex.JUSTIFY) // 两端对齐

// 行间距
instance.command.executeRowMargin(10)            // 设置行间距

// 标题
instance.command.executeTitle(TitleLevel.FIRST)  // 一级标题
instance.command.executeTitle(TitleLevel.SECOND) // 二级标题

// 列表
instance.command.executeList(ListType.UL)        // 无序列表
instance.command.executeList(ListType.OL)        // 有序列表
```

**4. 插入命令**

```typescript
// 插入表格
instance.command.executeInsertTable(3, 4)        // 3行4列表格

// 插入图片
const imageData = {
  value: '',
  type: ElementType.IMAGE,
  src: 'data:image/png;base64,...'
}
instance.command.executeImage(imageData)

// 插入超链接
instance.command.executeHyperlink('https://example.com', '链接文本')

// 插入分页符
instance.command.executePageBreak()

// 插入日期
instance.command.executeDate('YYYY-MM-DD')
```

**5. 模式和视图命令**

```typescript
// 切换编辑模式
instance.command.executeMode(EditorMode.EDIT)     // 编辑模式
instance.command.executeMode(EditorMode.READONLY) // 只读模式
instance.command.executeMode(EditorMode.PRINT)    // 打印模式

// 页面模式
instance.command.executePageMode(PageMode.PAGING)     // 分页模式
instance.command.executePageMode(PageMode.CONTINUITY) // 连续页模式

// 缩放
instance.command.executePageScale(1.5)            // 150%缩放
```

**6. 获取数据命令**

```typescript
// 获取文档数据
const result = instance.command.getValue({
  pageNo: 0,                    // 指定页面
  includeHeader: true,          // 包含页眉
  includeFooter: true           // 包含页脚
})

// 获取选中内容
const selection = instance.command.getRangeText()

// 获取页面图片
const images = await instance.command.getImage({
  pixelRatio: 2,               // 像素比
  mode: 'png'                  // 图片格式
})

// 获取配置
const options = instance.command.getOptions()
```

#### 命令执行流程

```typescript
// 命令执行的典型流程
public executeBold() {
  // 1. 检查权限
  const isDisabled = this.draw.isReadonly() || this.draw.isDisabled()
  if (isDisabled) return

  // 2. 获取当前状态
  const range = this.range.getRange()
  if (!range) return

  // 3. 执行操作
  const { startIndex, endIndex } = range
  const elementList = this.draw.getElementList()

  for (let i = startIndex; i <= endIndex; i++) {
    const element = elementList[i]
    element.bold = !element.bold
  }

  // 4. 触发重新渲染
  this.draw.render({
    isSetCursor: true,
    isSubmitHistory: true
  })

  // 5. 触发事件
  this.eventBus.emit('rangeStyleChange', this.getRangeStyle())
}
```

#### 使用示例

```typescript
// 基础使用
const editor = new Editor(container, data, options)

// 格式化文本
editor.command.executeBold()
editor.command.executeFont('Arial')
editor.command.executeSize(18)
editor.command.executeColor('#FF0000')

// 段落操作
editor.command.executeRowFlex(RowFlex.CENTER)
editor.command.executeTitle(TitleLevel.FIRST)

// 插入内容
editor.command.executeInsertTable(3, 4)
editor.command.executeHyperlink('https://example.com', '链接')

// 获取数据
const documentData = editor.command.getValue()
const images = await editor.command.getImage()

// 模式切换
editor.command.executeMode(EditorMode.READONLY)
```

### 4. 位置管理系统 (Position)

**位置**: `src/editor/core/position/Position.ts`
**功能**: 管理光标位置、元素位置计算和坐标转换

#### 核心属性

```typescript
export class Position {
  private cursorPosition: IElementPosition | null     // 光标位置
  private positionContext: IPositionContext          // 位置上下文
  private positionList: IElementPosition[]           // 位置列表
  private floatPositionList: IFloatPosition[]        // 浮动位置列表

  private draw: Draw                                  // 渲染引擎引用
  private eventBus: EventBus<EventBusMap>            // 事件总线
  private options: DeepRequired<IEditorOption>       // 配置选项
}
```

#### 主要方法

```typescript
// 位置计算
public computePositionList(): void                  // 计算所有元素位置
public computePageRowPosition(payload: any): void   // 计算页面行位置

// 位置获取
public getPositionList(): IElementPosition[]        // 获取位置列表
public getFloatPositionList(): IFloatPosition[]     // 获取浮动位置列表
public getCursorPosition(): IElementPosition | null // 获取光标位置

// 坐标转换
public getPositionByXY(payload: IGetPositionByXYPayload): ICurrentPosition
public getXYByPosition(position: IElementPosition): { x: number, y: number }

// 位置设置
public setCursorPosition(position: IElementPosition | null): void
public setPositionContext(context: IPositionContext): void
```

#### 位置计算流程

```typescript
public computePositionList() {
  // 1. 置空原位置信息
  this.positionList = []

  // 2. 按每页行计算
  const pageRowList = this.draw.getPageRowList()
  const margins = this.draw.getMargins()
  const startX = margins[3]
  const startY = margins[0] + this.draw.getHeader().getExtraHeight()

  let startRowIndex = 0
  for (let i = 0; i < pageRowList.length; i++) {
    const rowList = pageRowList[i]

    // 3. 计算每页的行位置
    this.computePageRowPosition({
      positionList: this.positionList,
      rowList,
      pageNo: i,
      startRowIndex,
      startX,
      startY,
      innerWidth: this.draw.getInnerWidth()
    })

    startRowIndex += rowList.length
  }
}
```

#### 使用示例

```typescript
// 获取位置管理器（通常在内部使用）
const position = draw.getPosition()

// 计算位置
position.computePositionList()

// 根据坐标获取位置
const currentPosition = position.getPositionByXY({
  x: 100,
  y: 200,
  pageNo: 0
})

// 根据位置获取坐标
const elementPosition = position.getPositionList()[10]
const { x, y } = position.getXYByPosition(elementPosition)

// 设置光标位置
position.setCursorPosition({
  index: 10,
  isTable: false,
  isControl: false
})
```

### 5. 区域管理系统 (Zone)

**位置**: `src/editor/core/zone/Zone.ts`
**功能**: 管理编辑器的页眉、正文、页脚区域切换

#### 核心属性

```typescript
export class Zone {
  private draw: Draw                          // 渲染引擎引用
  private options: Required<IEditorOption>    // 配置选项
  private i18n: I18n                         // 国际化
  private container: HTMLDivElement          // 容器元素
  private currentZone: EditorZone            // 当前区域
  private indicatorContainer: HTMLDivElement | null // 指示器容器
}
```

#### 主要方法

```typescript
// 区域判断
public isHeaderActive(): boolean            // 是否在页眉区域
public isMainActive(): boolean              // 是否在正文区域
public isFooterActive(): boolean            // 是否在页脚区域

// 区域设置
public setZone(zone: EditorZone): void      // 设置当前区域
public getZone(): EditorZone                // 获取当前区域
public getZoneByY(y: number): EditorZone    // 根据Y坐标获取区域

// 区域指示器
public drawZoneIndicator(): void            // 绘制区域指示器
public clearZoneIndicator(): void           // 清除区域指示器
```

#### 区域切换逻辑

```typescript
public setZone(payload: EditorZone) {
  const { header, footer } = this.options

  // 1. 检查区域是否可编辑
  if (
    (!header.editable && payload === EditorZone.HEADER) ||
    (!footer.editable && payload === EditorZone.FOOTER)
  ) {
    return
  }

  // 2. 检查是否需要切换
  if (this.currentZone === payload) return

  // 3. 切换区域
  this.currentZone = payload

  // 4. 清除选区
  this.draw.getRange().clearRange()

  // 5. 重新渲染
  this.draw.render({
    isSubmitHistory: false,
    isSetCursor: false,
    isCompute: false
  })

  // 6. 绘制指示器
  this.drawZoneIndicator()

  // 7. 触发事件
  this.eventBus.emit('zoneChange', payload)
}
```

#### 使用示例

```typescript
// 获取区域管理器
const zone = draw.getZone()

// 检查当前区域
if (zone.isMainActive()) {
  console.log('当前在正文区域')
}

// 切换到页眉区域
zone.setZone(EditorZone.HEADER)

// 根据坐标判断区域
const targetZone = zone.getZoneByY(150)
if (targetZone === EditorZone.FOOTER) {
  console.log('点击位置在页脚区域')
}
```

### 6. 事件系统 (EventBus & Listener)

#### EventBus 事件总线

**位置**: `src/editor/core/event/eventbus/EventBus.ts`
**功能**: 提供发布-订阅模式的事件通信机制

```typescript
export class EventBus<EventMap> {
  private eventHub: Map<string, Set<Function>>

  // 订阅事件
  public on<K extends string & keyof EventMap>(
    eventName: K,
    callback: EventMap[K]
  ): void

  // 触发事件
  public emit<K extends string & keyof EventMap>(
    eventName: K,
    payload?: EventMap[K] extends (payload: infer P) => void ? P : never
  ): void

  // 取消订阅
  public off<K extends string & keyof EventMap>(
    eventName: K,
    callback: EventMap[K]
  ): void

  // 检查是否有订阅
  public isSubscribe(eventName: string): boolean
}
```

#### Listener 监听器

**位置**: `src/editor/core/listener/Listener.ts`
**功能**: 提供简单的事件监听接口（已废弃，推荐使用EventBus）

```typescript
export class Listener {
  public rangeStyleChange: IRangeStyleChange | null           // 选区样式变化
  public visiblePageNoListChange: IVisiblePageNoListChange | null // 可见页面变化
  public intersectionPageNoChange: IIntersectionPageNoChange | null // 当前页变化
  public pageSizeChange: IPageSizeChange | null               // 页面大小变化
  public pageScaleChange: IPageScaleChange | null             // 页面缩放变化
  public saved: ISaved | null                                 // 保存事件
  public contentChange: IContentChange | null                 // 内容变化
  public controlChange: IControlChange | null                 // 控件变化
  public controlContentChange: IControlContentChange | null   // 控件内容变化
  public pageModeChange: IPageModeChange | null               // 页面模式变化
  public zoneChange: IZoneChange | null                       // 区域变化
}
```

#### 事件类型定义

```typescript
// 事件映射接口
export interface EventBusMap {
  rangeStyleChange: IRangeStyleChange
  visiblePageNoListChange: IVisiblePageNoListChange
  intersectionPageNoChange: IIntersectionPageNoChange
  pageSizeChange: IPageSizeChange
  pageScaleChange: IPageScaleChange
  saved: ISaved
  contentChange: IContentChange
  controlChange: IControlChange
  controlContentChange: IControlContentChange
  pageModeChange: IPageModeChange
  zoneChange: IZoneChange
  mousemove: IMouseEventChange
  mouseleave: IMouseEventChange
  mouseenter: IMouseEventChange
  mousedown: IMouseEventChange
  mouseup: IMouseEventChange
  click: IMouseEventChange
  positionContextChange: IPositionContextChange
  imageSizeChange: IImageSizeChange
}
```

#### 使用示例

```typescript
// EventBus使用
const eventBus = editor.eventBus

// 订阅事件
eventBus.on('contentChange', () => {
  console.log('文档内容发生变化')
})

eventBus.on('rangeStyleChange', (styleInfo) => {
  console.log('选区样式变化:', styleInfo)
  // 更新工具栏状态
  updateToolbarState(styleInfo)
})

eventBus.on('zoneChange', (zone) => {
  console.log('区域切换到:', zone)
  if (zone === EditorZone.HEADER) {
    console.log('现在在页眉区域')
  }
})

// 触发自定义事件
eventBus.emit('contentChange')

// 取消订阅
const handler = (data) => console.log(data)
eventBus.on('saved', handler)
eventBus.off('saved', handler)

// Listener使用（不推荐，但仍可用）
const listener = editor.listener

listener.contentChange = () => {
  console.log('内容变化 - 通过Listener')
}

listener.rangeStyleChange = (payload) => {
  console.log('样式变化 - 通过Listener:', payload)
}
```

### 7. 注册系统 (Register)

**位置**: `src/editor/core/register/Register.ts`
**功能**: 提供统一的注册接口，用于注册右键菜单、快捷键、语言包等

#### 核心属性和方法

```typescript
export class Register {
  public contextMenuList: (payload: IRegisterContextMenu[]) => void  // 注册右键菜单
  public getContextMenuList: () => IRegisterContextMenu[]            // 获取右键菜单列表
  public shortcutList: (payload: IRegisterShortcut[]) => void        // 注册快捷键
  public langMap: (locale: string, lang: DeepPartial<ILang>) => void // 注册语言包

  constructor(payload: IRegisterPayload) {
    const { contextMenu, shortcut, i18n } = payload

    // 绑定方法
    this.contextMenuList = contextMenu.registerContextMenuList.bind(contextMenu)
    this.getContextMenuList = contextMenu.getContextMenuList.bind(contextMenu)
    this.shortcutList = shortcut.registerShortcutList.bind(shortcut)
    this.langMap = i18n.registerLangMap.bind(i18n)
  }
}
```

#### 右键菜单注册

```typescript
// 右键菜单项接口
interface IRegisterContextMenu {
  key?: string                    // 菜单项唯一标识
  isDivider?: boolean            // 是否为分割线
  icon?: string                  // 图标
  name?: string                  // 菜单名称（支持%s占位符）
  shortCut?: string              // 快捷键显示
  disable?: boolean              // 是否禁用
  when?: (payload: IContextMenuContext) => boolean  // 显示条件
  callback?: (command: Command, context: IContextMenuContext) => any // 回调函数
  childMenus?: IRegisterContextMenu[]  // 子菜单
}

// 使用示例
editor.register.contextMenuList([
  {
    key: 'customSearch',
    name: '搜索：%s',           // %s会被选中文本替换
    shortCut: 'Ctrl + F',
    when: (payload) => {
      return payload.editorHasSelection  // 只在有选中内容时显示
    },
    callback: (command, context) => {
      const selectedText = context.selectedText
      console.log('搜索:', selectedText)
      // 执行搜索逻辑
    }
  },
  {
    isDivider: true  // 分割线
  },
  {
    key: 'customFormat',
    name: '自定义格式化',
    icon: 'format-icon',
    callback: (command, context) => {
      // 执行格式化逻辑
      command.executeBold()
      command.executeColor('#FF0000')
    }
  }
])
```

#### 快捷键注册

```typescript
// 快捷键接口
interface IRegisterShortcut {
  key: string                    // 按键
  ctrl?: boolean                 // 是否需要Ctrl
  meta?: boolean                 // 是否需要Meta(Cmd)
  shift?: boolean                // 是否需要Shift
  alt?: boolean                  // 是否需要Alt
  mod?: boolean                  // 是否需要修饰键(Ctrl/Cmd)
  disable?: boolean              // 是否禁用
  callback?: (command: Command) => void  // 回调函数
}

// 使用示例
editor.register.shortcutList([
  {
    key: 'S',
    ctrl: true,
    shift: true,
    callback: (command) => {
      console.log('执行自定义保存')
      // 执行保存逻辑
      const data = command.getValue()
      saveToServer(data)
    }
  },
  {
    key: 'F1',
    callback: (command) => {
      console.log('显示帮助')
      showHelpDialog()
    }
  },
  {
    key: 'B',
    mod: true,  // 跨平台修饰键(Windows: Ctrl, Mac: Cmd)
    callback: (command) => {
      command.executeBold()
    }
  }
])
```

#### 语言包注册

```typescript
// 注册中文语言包
editor.register.langMap('zh', {
  contextmenu: {
    global: {
      cut: '剪切',
      copy: '复制',
      paste: '粘贴',
      selectAll: '全选'
    },
    table: {
      insertRowTop: '在上方插入行',
      insertRowBottom: '在下方插入行',
      insertColLeft: '在左侧插入列',
      insertColRight: '在右侧插入列'
    }
  },
  toolbar: {
    bold: '加粗',
    italic: '斜体',
    underline: '下划线'
  }
})

// 注册英文语言包
editor.register.langMap('en', {
  contextmenu: {
    global: {
      cut: 'Cut',
      copy: 'Copy',
      paste: 'Paste',
      selectAll: 'Select All'
    }
  }
})
```

### 8. 插件系统 (Plugin)

**位置**: `src/editor/core/plugin/Plugin.ts`
**功能**: 提供可扩展的插件架构，支持第三方功能扩展

#### 核心实现

```typescript
export class Plugin {
  private editor: Editor

  constructor(editor: Editor) {
    this.editor = editor
  }

  public use<Options>(
    pluginFunction: PluginFunction<Options>,
    options?: Options
  ) {
    pluginFunction(this.editor, options)
  }
}

// 插件函数类型
type PluginFunction<Options = any> = (
  editor: Editor,
  options?: Options
) => void
```

#### 插件开发示例

```typescript
// 自定义插件接口
interface MyPluginOptions {
  enabled?: boolean
  theme?: 'light' | 'dark'
  customSettings?: Record<string, any>
}

// 插件实现
export function myCustomPlugin(editor: Editor, options: MyPluginOptions = {}) {
  const { enabled = true, theme = 'light', customSettings = {} } = options

  // 1. 检查插件是否启用
  if (!enabled) {
    console.log('插件已禁用')
    return
  }

  console.log('初始化自定义插件')

  // 2. 扩展命令系统
  const command = editor.command as any
  command.executeMyCustomFunction = (payload: any) => {
    try {
      console.log('执行自定义功能:', payload)

      // 实现自定义逻辑
      const currentData = editor.command.getValue()
      // 处理数据...

      // 触发重新渲染
      editor.command.executeForceUpdate()

      // 触发自定义事件
      editor.eventBus.emit('myCustomEvent', payload)
    } catch (error) {
      console.error('插件功能执行失败:', error)
    }
  }

  // 3. 注册事件监听
  editor.eventBus.on('contentChange', () => {
    console.log('插件监听到内容变化')
    handleContentChange()
  })

  // 4. 注册快捷键
  editor.register.shortcutList([
    {
      key: 'M',
      ctrl: true,
      shift: true,
      callback: (command) => {
        command.executeMyCustomFunction({ source: 'shortcut' })
      }
    }
  ])

  // 5. 注册右键菜单
  editor.register.contextMenuList([
    {
      key: 'myPluginMenu',
      name: '我的插件功能',
      when: (payload) => {
        return payload.editorHasSelection
      },
      callback: (command, context) => {
        command.executeMyCustomFunction({
          source: 'contextMenu',
          context
        })
      }
    }
  ])

  // 6. 插件清理函数
  const cleanup = () => {
    console.log('清理插件')
    // 移除事件监听
    // 清理DOM元素
    // 清理定时器等
  }

  // 7. 返回插件API（可选）
  return {
    name: 'myCustomPlugin',
    version: '1.0.0',
    cleanup,
    executeMyCustomFunction: command.executeMyCustomFunction
  }

  function handleContentChange() {
    // 处理内容变化
    if (theme === 'dark') {
      // 暗色主题特殊处理
    }
  }
}

// 使用插件
const editor = new Editor(container, data, options)

// 注册插件
editor.use(myCustomPlugin, {
  enabled: true,
  theme: 'dark',
  customSettings: {
    autoSave: true,
    interval: 5000
  }
})

// 调用插件功能
const command = editor.command as any
command.executeMyCustomFunction({ data: 'test' })
```

#### 插件最佳实践

```typescript
// 1. 类型安全的插件
interface EditorWithPlugin extends Editor {
  command: Editor['command'] & {
    executeMyFunction: (data: any) => void
  }
}

function typeSafePlugin(editor: Editor): EditorWithPlugin {
  const extendedCommand = editor.command as any
  extendedCommand.executeMyFunction = (data: any) => {
    // 实现逻辑
  }
  return editor as EditorWithPlugin
}

// 2. 插件组合
function combinedPlugin(editor: Editor) {
  // 使用多个子插件
  editor.use(pluginA)
  editor.use(pluginB, { option: 'value' })
  editor.use(pluginC)
}

// 3. 条件插件
function conditionalPlugin(editor: Editor, options: { condition: boolean }) {
  if (options.condition) {
    // 只在满足条件时启用
    editor.use(somePlugin)
  }
}
```

### 9. 上下文菜单系统 (ContextMenu)

**位置**: `src/editor/core/contextmenu/ContextMenu.ts`
**功能**: 管理右键菜单的显示、隐藏和交互

#### 核心属性

```typescript
export class ContextMenu {
  private options: DeepRequired<IEditorOption>        // 配置选项
  private draw: Draw                                  // 渲染引擎引用
  private command: Command                            // 命令系统引用
  private range: RangeManager                         // 选区管理器
  private position: Position                          // 位置管理器
  private i18n: I18n                                 // 国际化
  private container: HTMLDivElement                   // 容器元素
  private context: IContextMenuContext | null        // 菜单上下文
  private contextMenuList: IRegisterContextMenu[]    // 菜单项列表
  private contextMenuContainerList: HTMLDivElement[] // 菜单容器列表
}
```

#### 主要方法

```typescript
// 菜单管理
public registerContextMenuList(payload: IRegisterContextMenu[]): void  // 注册菜单项
public getContextMenuList(): IRegisterContextMenu[]                    // 获取菜单项列表
public dispose(): void                                                  // 销毁菜单

// 内部方法
private _getContext(): IContextMenuContext                             // 获取菜单上下文
private _filterMenuList(menuList: IRegisterContextMenu[]): IRegisterContextMenu[] // 过滤菜单项
private _render(payload: IRenderContextMenuPayload): void              // 渲染菜单
```

#### 菜单上下文接口

```typescript
interface IContextMenuContext {
  startElement: IElement                    // 起始元素
  endElement: IElement                      // 结束元素
  editorHasSelection: boolean              // 编辑器是否有选区
  editorTextFocus: boolean                 // 编辑器文本是否聚焦
  isInTable: boolean                       // 是否在表格中
  isInControl: boolean                     // 是否在控件中
  isInHyperlink: boolean                   // 是否在超链接中
  isInImage: boolean                       // 是否在图片中
  selectedText: string                     // 选中的文本
  // ... 更多上下文信息
}
```

#### 使用示例

```typescript
// 注册自定义右键菜单
editor.register.contextMenuList([
  {
    key: 'translate',
    name: '翻译：%s',
    when: (context) => {
      // 只在选中文本且文本长度大于0时显示
      return context.editorHasSelection && context.selectedText.length > 0
    },
    callback: (command, context) => {
      const text = context.selectedText
      translateText(text).then(result => {
        console.log('翻译结果:', result)
      })
    }
  },
  {
    key: 'tableOperations',
    name: '表格操作',
    when: (context) => context.isInTable,
    childMenus: [
      {
        key: 'insertRow',
        name: '插入行',
        callback: (command) => {
          command.executeInsertTableRow()
        }
      },
      {
        key: 'deleteRow',
        name: '删除行',
        callback: (command) => {
          command.executeDeleteTableRow()
        }
      }
    ]
  }
])
```

### 10. 快捷键系统 (Shortcut)

**位置**: `src/editor/core/shortcut/Shortcut.ts`
**功能**: 管理全局和编辑器快捷键

#### 核心属性

```typescript
export class Shortcut {
  private command: Command                        // 命令系统引用
  private globalShortcutList: IRegisterShortcut[] // 全局快捷键列表
  private agentShortcutList: IRegisterShortcut[]  // 编辑器快捷键列表
}
```

#### 主要方法

```typescript
// 快捷键注册
public registerShortcutList(payload: IRegisterShortcut[]): void  // 注册快捷键

// 事件处理
private _globalKeydown(evt: KeyboardEvent): void                 // 全局按键处理
private _agentKeydown(evt: KeyboardEvent): void                  // 编辑器按键处理
private _execute(evt: KeyboardEvent, shortCutList: IRegisterShortcut[]): void // 执行快捷键

// 清理
public removeEvent(): void                                       // 移除事件监听
```

#### 快捷键执行逻辑

```typescript
private _execute(evt: KeyboardEvent, shortCutList: IRegisterShortcut[]) {
  for (let s = 0; s < shortCutList.length; s++) {
    const shortCut = shortCutList[s]

    // 检查修饰键匹配
    const modMatch = shortCut.mod
      ? isMod(evt) === !!shortCut.mod
      : evt.ctrlKey === !!shortCut.ctrl && evt.metaKey === !!shortCut.meta

    // 检查所有按键匹配
    if (
      modMatch &&
      evt.shiftKey === !!shortCut.shift &&
      evt.altKey === !!shortCut.alt &&
      evt.key.toLowerCase() === shortCut.key.toLowerCase()
    ) {
      // 执行快捷键回调
      if (!shortCut.disable) {
        shortCut?.callback?.(this.command)
        evt.preventDefault()
      }
      break
    }
  }
}
```

#### 内置快捷键

```typescript
// 富文本快捷键
const richtextKeys: IRegisterShortcut[] = [
  { key: 'b', mod: true, callback: command => command.executeBold() },
  { key: 'i', mod: true, callback: command => command.executeItalic() },
  { key: 'u', mod: true, callback: command => command.executeUnderline() },
  { key: 'z', mod: true, callback: command => command.executeUndo() },
  { key: 'y', mod: true, callback: command => command.executeRedo() },
  { key: 'a', mod: true, callback: command => command.executeSelectAll() },
  { key: 'c', mod: true, callback: command => command.executeCopy() },
  { key: 'x', mod: true, callback: command => command.executeCut() },
  { key: 'v', mod: true, callback: command => command.executePaste() }
]

// 标题快捷键
const titleKeys: IRegisterShortcut[] = [
  { key: '1', mod: true, callback: command => command.executeTitle(TitleLevel.FIRST) },
  { key: '2', mod: true, callback: command => command.executeTitle(TitleLevel.SECOND) },
  { key: '3', mod: true, callback: command => command.executeTitle(TitleLevel.THIRD) }
]

// 列表快捷键
const listKeys: IRegisterShortcut[] = [
  { key: 'l', mod: true, shift: true, callback: command => command.executeList(ListType.UL) },
  { key: 'o', mod: true, shift: true, callback: command => command.executeList(ListType.OL) }
]
```

## 🔄 模块间交互流程

### 典型操作流程

```mermaid
graph TD
    A[用户操作] --> B[事件捕获]
    B --> C[Command命令]
    C --> D[CommandAdapt适配器]
    D --> E[Draw渲染引擎]
    E --> F[Position位置计算]
    F --> G[Canvas渲染]
    G --> H[EventBus事件通知]
    H --> I[Listener回调]

    E --> J[HistoryManager历史管理]
    J --> K[提交历史记录]
```

### 数据流向

```
用户输入 → 事件处理 → 命令执行 → 数据更新 → 位置计算 → 渲染更新 → 事件通知
```

## 📊 架构特点总结

### 1. **分层解耦**
- 每层职责明确，依赖关系清晰
- 上层可以调用下层，下层通过事件通知上层
- 便于单元测试和模块替换

### 2. **命令模式**
- 所有操作都封装为命令
- 支持撤销重做、宏录制等高级功能
- 便于权限控制和操作审计

### 3. **事件驱动**
- 模块间通过事件总线通信
- 降低模块间的直接依赖
- 支持插件和扩展的灵活接入

### 4. **渐进式渲染**
- 支持懒加载和增量渲染
- 优化大文档的性能表现
- 提供流畅的用户体验

### 5. **插件化架构**
- 核心功能与扩展功能分离
- 支持第三方插件开发
- 便于功能的定制和扩展

## 🎯 开发建议

### 1. **扩展编辑器功能**
- 使用插件系统进行功能扩展
- 通过Register系统注册自定义菜单和快捷键
- 利用EventBus进行模块间通信

### 2. **性能优化**
- 合理使用渲染参数控制重绘范围
- 避免频繁的DOM操作
- 使用事件节流和防抖

### 3. **错误处理**
- 在插件中添加适当的错误处理
- 使用try-catch包装关键操作
- 提供用户友好的错误提示

### 4. **类型安全**
- 使用TypeScript进行开发
- 定义清晰的接口和类型
- 利用类型检查避免运行时错误

---

## 📚 参考资源

- [Canvas Editor 官方文档](https://hufe.club/canvas-editor-docs/)
- [GitHub 仓库](https://github.com/Hufe921/canvas-editor)
- [API 参考手册](https://hufe.club/canvas-editor-docs/guide/command-execute.html)
- [插件开发指南](https://hufe.club/canvas-editor-docs/guide/plugin.html)
