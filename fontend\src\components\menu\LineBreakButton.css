﻿/* 段落结束标记按钮样式 */
.menu-item__line-break { width: 35px !important; height: 30px !important; display: flex !important; align-items: center !important; justify-content: center !important; cursor: pointer !important; border-radius: 4px !important; transition: background-color 0.2s ease !important; position: relative !important; padding: 5px !important; }
.menu-item__line-break i { width: 24px !important; height: 24px !important; display: inline-block !important; position: relative !important; font-style: normal !important; }
.menu-item__line-break i::before { content: '\00B6' !important; font-size: 22px !important; font-weight: bold !important; position: absolute !important; top: 50% !important; left: 50% !important; transform: translate(-50%, -50%) !important; color: #3d4757 !important; font-family: 'Segoe UI', Arial, sans-serif !important; }
.menu-item__line-break:hover { background-color: #f0f0f0 !important; } .menu-item__line-break:hover i::before { color: #1e88e5 !important; } .menu-item__line-break.active { background-color: #e6f7ff !important; border: 1px solid #91d5ff !important; } .menu-item__line-break.active i::before { color: #1890ff !important; font-weight: 700 !important; }
