import Editor from '../../editor'
import { PageMode } from '../../editor'
import html from './LayoutPageModeButton.html'

/**
 * 布局菜单页面模式按钮组件
 * 用于在布局菜单中选择页面模式（分页、连页）
 */
export class LayoutPageModeButton {
  private dom: HTMLDivElement
  private instance: Editor
  private optionsElement: HTMLDivElement
  private isVisible = false

  constructor(instance: Editor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    this.optionsElement = this.dom.querySelector('.options') as HTMLDivElement

    this.bindEvents()
    this.setupClickOutside()
  }

  /**
   * 绑定事件处理器
   */
  private bindEvents(): void {
    // 点击图标切换下拉菜单
    const iconElement = this.dom.querySelector('i')!
    iconElement.onclick = (e) => {
      e.stopPropagation()
      this.toggleOptions()
    }

    // 点击选项
    this.optionsElement.onclick = (e) => {
      e.stopPropagation()
      const target = e.target as HTMLElement

      if (target.tagName === 'LI') {
        const pageMode = target.dataset.pageMode! as PageMode
        this.selectPageMode(pageMode, target as HTMLLIElement)
      }
    }
  }

  /**
   * 设置点击外部关闭下拉菜单
   */
  private setupClickOutside(): void {
    document.addEventListener('click', (e) => {
      if (!this.dom.contains(e.target as Node) && this.isVisible) {
        this.hideOptions()
      }
    })
  }

  /**
   * 切换下拉选项显示状态
   */
  private toggleOptions(): void {
    if (this.isVisible) {
      this.hideOptions()
    } else {
      this.showOptions()
    }
  }

  /**
   * 显示下拉选项
   */
  private showOptions(): void {
    this.optionsElement.classList.add('visible')
    this.isVisible = true
    this.positionOptions()
  }

  /**
   * 定位下拉选项
   */
  private positionOptions(): void {
    const iconElement = this.dom.querySelector('i')!
    const rect = iconElement.getBoundingClientRect()

    // 计算下拉框位置：图标正下方
    const left = rect.left
    const top = rect.bottom + 2 // 图标下方2px间距

    console.log(`页面模式下拉框定位: left=${left}, top=${top}`)

    // 应用绝对定位
    this.optionsElement.style.position = 'fixed'
    this.optionsElement.style.left = left + 'px'
    this.optionsElement.style.top = top + 'px'
    this.optionsElement.style.zIndex = '9999'

    console.log(`页面模式下拉框定位: left=${left}, top=${top}`)
  }

  /**
   * 隐藏下拉选项
   */
  private hideOptions(): void {
    this.optionsElement.classList.remove('visible')
    this.isVisible = false
  }

  /**
   * 选择页面模式
   */
  private selectPageMode(pageMode: PageMode, liElement: HTMLLIElement): void {
    try {
      console.log(`设置页面模式: ${pageMode}`)

      // 执行页面模式切换命令
      this.instance.command.executePageMode(pageMode)

      // 更新选中状态
      this.updateActiveState(liElement)

      // 关闭下拉菜单
      this.hideOptions()

      console.log('页面模式设置完成')
    } catch (error) {
      console.error('设置页面模式失败:', error)
    }
  }

  /**
   * 更新选中状态
   */
  private updateActiveState(selectedElement: HTMLLIElement): void {
    // 移除所有选项的active状态
    this.optionsElement.querySelectorAll('li').forEach(li => {
      li.classList.remove('active')
    })

    // 添加当前选项的active状态
    selectedElement.classList.add('active')
  }

  /**
   * 设置页面模式（外部调用）
   */
  public setPageMode(pageMode: PageMode): void {
    const targetLi = this.optionsElement.querySelector(`li[data-page-mode="${pageMode}"]`) as HTMLLIElement
    if (targetLi) {
      this.selectPageMode(pageMode, targetLi)
    }
  }

  /**
   * 获取当前选中的页面模式
   */
  public getCurrentPageMode(): PageMode | null {
    const activeElement = this.optionsElement.querySelector('li.active') as HTMLLIElement
    return activeElement ? activeElement.dataset.pageMode! as PageMode : null
  }

  /**
   * 获取DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
}
