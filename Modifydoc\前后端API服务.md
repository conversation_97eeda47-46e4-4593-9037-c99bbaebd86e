# Canvas Editor 前后端API服务完整配置文档

## 📋 项目概述

**项目名称**: Canvas Editor
**项目路径**: `D:\canvas-editor`
**创建时间**: 2025年6月15日
**技术栈**: Django + TypeScript + Vite

### 项目结构
```
D:\canvas-editor\
├── backend/                    # Django后端服务
│   ├── .venv/                 # Python虚拟环境
│   ├── book_editor_backend/   # Django项目主目录
│   ├── api/                   # API应用
│   ├── manage.py              # Django管理脚本
│   ├── requirements.txt       # Python依赖
│   └── db.sqlite3            # SQLite数据库
├── fontend/                   # 前端应用
│   ├── src/
│   │   ├── api/              # API模块
│   │   ├── components/       # 组件
│   │   └── init/             # 初始化
│   ├── vite.config.ts        # Vite配置
│   └── package.json          # 前端依赖
└── Modifydoc/                # 文档目录
```

## 🔧 后端服务配置

### 1. Django项目配置

#### 基础信息
- **Django版本**: 5.0.7
- **Python版本**: 3.13.3
- **数据库**: SQLite (开发环境)
- **服务端口**: 8000
- **服务地址**: http://127.0.0.1:8000

#### 虚拟环境配置
```bash
# 虚拟环境路径
D:\canvas-editor\backend\.venv\

# 激活命令 (Windows)
.venv\Scripts\activate

# 激活命令 (PowerShell)
.\.venv\Scripts\Activate.ps1
```

#### 已安装的Python包
```txt
Django==5.0.7
djangorestframework==3.15.2
django-cors-headers==4.4.0
PyMySQL==1.1.1
asgiref==3.8.1
sqlparse==0.5.1
tzdata==2024.2
```

### 2. Django设置配置

#### 主要设置 (`book_editor_backend/settings.py`)
```python
# 基础配置
DEBUG = True
ALLOWED_HOSTS = ['127.0.0.1', 'localhost']

# 应用配置
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',      # Django REST Framework
    'corsheaders',         # CORS处理
    'api',                 # API应用
]

# 中间件配置
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# CORS配置
CORS_ALLOWED_ORIGINS = [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
]
CORS_ALLOW_CREDENTIALS = True

# REST Framework配置
REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20
}

# 数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}
```

### 3. API应用配置

#### API应用结构
```
backend/api/
├── __init__.py
├── admin.py              # 管理后台配置
├── apps.py               # 应用配置
├── models.py             # 数据模型
├── serializers.py        # 序列化器
├── views.py              # 视图函数
├── urls.py               # URL路由
└── migrations/           # 数据库迁移文件
```

#### 数据模型 (`api/models.py`)
```python
from django.db import models
from django.contrib.auth.models import User

class Document(models.Model):
    title = models.CharField(max_length=200, verbose_name='标题')
    content = models.JSONField(verbose_name='内容')
    author = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='作者')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    is_public = models.BooleanField(default=False, verbose_name='是否公开')

    class Meta:
        verbose_name = '文档'
        verbose_name_plural = '文档'
        ordering = ['-updated_at']

class DocumentVersion(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='versions')
    version_number = models.PositiveIntegerField(verbose_name='版本号')
    content = models.JSONField(verbose_name='内容')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    comment = models.TextField(blank=True, verbose_name='版本说明')

    class Meta:
        verbose_name = '文档版本'
        verbose_name_plural = '文档版本'
        unique_together = ['document', 'version_number']
        ordering = ['-version_number']
```

#### API端点配置 (`api/urls.py`)
```python
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'documents', views.DocumentViewSet)

urlpatterns = [
    path('health/', views.health_check, name='health_check'),
    path('', include(router.urls)),
]
```

### 4. 数据库配置

#### 已应用的迁移
```bash
# 系统迁移 (19个)
admin: 0001_initial, 0002_logentry_remove_auto_add, 0003_logentry_add_action_flag_choices
api: 0001_initial
auth: 0001_initial ~ 0012_alter_user_first_name_max_length
contenttypes: 0001_initial, 0002_remove_content_type_name
sessions: 0001_initial

# 迁移状态: ✅ 全部已应用
```

#### 数据库表结构
```sql
-- 主要数据表
api_document              # 文档表
api_documentversion       # 文档版本表
auth_user                 # 用户表
django_session           # 会话表
```

## 🌐 前端服务配置

### 1. 基础配置

#### 技术栈信息
- **构建工具**: Vite 5.4.10
- **TypeScript**: 5.6.2
- **开发端口**: 3000
- **服务地址**: http://localhost:3000

#### 项目依赖 (`package.json`)
```json
{
  "name": "canvas-editor",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "@hufe921/canvas-editor": "^0.9.84"
  },
  "devDependencies": {
    "typescript": "^5.6.2",
    "vite": "^5.4.10"
  }
}
```

### 2. Vite配置 (`vite.config.ts`)

#### 开发服务器配置
```typescript
export default defineConfig(({ command, mode }) => {
  const name = 'canvas-editor'

  return {
    base: `/${name}/`,
    server: {
      host: '0.0.0.0',
      port: 3000,
      // API代理配置 - 关键配置
      proxy: {
        '/api': {
          target: 'http://127.0.0.1:8000',    // Django后端地址
          changeOrigin: true,                  // 改变请求头中的host
          secure: false,                       // 不验证SSL证书
          configure: (proxy, _options) => {
            // 代理事件监听
            proxy.on('error', (err, _req, _res) => {
              console.log('proxy error', err)
            })
            proxy.on('proxyReq', (proxyReq, req, _res) => {
              console.log('Sending Request to the Target:', req.method, req.url)
            })
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log('Received Response from the Target:', proxyRes.statusCode, req.url)
            })
          }
        }
      }
    },
    // 其他配置...
  }
})
```

### 3. API模块配置

#### API模块结构
```
fontend/src/api/
├── config.ts              # API配置文件 ⭐
├── http-client.ts          # HTTP客户端封装 ⭐
├── services.ts             # API服务类 ⭐
├── types.ts                # TypeScript类型定义
├── utils.ts                # API工具函数
└── index.ts                # API模块入口
```

#### 核心配置 (`api/config.ts`)
```typescript
// 环境配置
export const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production'
} as const

// API基础配置
export const API_CONFIG = {
  // 开发环境 - 使用Vite代理
  DEVELOPMENT: {
    BASE_URL: '',           // 空字符串使用当前域名
    API_PREFIX: '/api'      // API前缀
  },
  // 生产环境
  PRODUCTION: {
    BASE_URL: 'https://your-production-domain.com',
    API_PREFIX: '/api'
  }
} as const

// HTTP请求配置
export const HTTP_CONFIG = {
  TIMEOUT: 30000,                    // 30秒超时
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
} as const

// API端点定义
export const API_ENDPOINTS = {
  HEALTH: '/health/',                                    # 健康检查
  DOCUMENTS: '/documents/',                              # 文档列表
  DOCUMENT_DETAIL: (id) => `/documents/${id}/`,         # 文档详情
  DOCUMENT_VERSIONS: (id) => `/documents/${id}/versions/`, # 文档版本
  // 更多端点...
} as const
```

### 4. 编辑器集成配置

#### 集成组件 (`components/api/EditorApiIntegration.ts`)
```typescript
export class EditorApiIntegration {
  private editor: any                    // Canvas Editor实例
  private currentDocumentId: string | number | null = null
  private autoSaveEnabled: boolean = true
  private autoSaveInterval: number = 30000  // 30秒自动保存

  // 主要功能
  async loadDocument(documentId)         // 加载文档
  async saveCurrentDocument()            // 保存当前文档
  async createNewDocument(title)         // 创建新文档
  async uploadImage(file)                // 上传图片
  startAutoSave()                        // 启动自动保存
  stopAutoSave()                         // 停止自动保存
}
```

#### 初始化集成 (`init/index.ts`)
```typescript
// API集成初始化
async function initApiIntegration(instance: Editor): Promise<void> {
  try {
    console.log('🚀 开始初始化API集成...')

    // 初始化API模块
    await initApiModule({ enableDebug: true })

    // 创建编辑器API集成实例
    editorApiIntegration = createEditorApiIntegration(instance)

    // 暴露到全局对象
    Reflect.set(window, 'editorApiIntegration', editorApiIntegration)

    // 开发环境初始化测试面板
    if (process.env.NODE_ENV === 'development') {
      const apiTestPanel = getApiTestPanel()
      Reflect.set(window, 'apiTestPanel', apiTestPanel)
    }

    console.log('✅ API集成初始化完成')
  } catch (error) {
    console.error('❌ API集成初始化失败:', error)
  }
}
```

## 🔗 API接口文档

### 1. 健康检查接口

**端点**: `GET /api/health/`
**描述**: 检查API服务健康状态
**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-06-15T16:40:00Z",
  "version": "1.0.0",
  "database": "sqlite",
  "debug": true
}
```

### 2. 文档管理接口

#### 获取文档列表
**端点**: `GET /api/documents/`
**参数**:
- `page`: 页码 (可选)
- `page_size`: 每页数量 (可选)
- `search`: 搜索关键词 (可选)
- `ordering`: 排序字段 (可选)

**响应示例**:
```json
{
  "count": 5,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "title": "我的文档",
      "content": {"main": [...]},
      "created_at": "2025-06-15T16:40:00Z",
      "updated_at": "2025-06-15T16:45:00Z",
      "is_public": false
    }
  ]
}
```

#### 创建文档
**端点**: `POST /api/documents/`
**请求体**:
```json
{
  "title": "新文档",
  "content": {"main": []},
  "is_public": false
}
```

#### 更新文档
**端点**: `PATCH /api/documents/{id}/`
**请求体**:
```json
{
  "title": "更新的标题",
  "content": {"main": [...]},
  "is_public": true
}
```

#### 删除文档
**端点**: `DELETE /api/documents/{id}/`
**响应**: `204 No Content`

### 3. 文档版本接口

#### 获取文档版本列表
**端点**: `GET /api/documents/{id}/versions/`

#### 创建文档版本
**端点**: `POST /api/documents/{id}/versions/`
**请求体**:
```json
{
  "content": {"main": [...]},
  "comment": "版本说明"
}
```

## 🚀 服务启动和部署

### 1. 开发环境启动

#### 后端服务启动
```bash
# 1. 进入后端目录
cd D:\canvas-editor\backend

# 2. 激活虚拟环境
.venv\Scripts\activate

# 3. 启动Django开发服务器
python manage.py runserver 8000

# 服务地址: http://127.0.0.1:8000
# 管理后台: http://127.0.0.1:8000/admin/
# API根路径: http://127.0.0.1:8000/api/
```

#### 前端服务启动
```bash
# 1. 进入前端目录
cd D:\canvas-editor\fontend

# 2. 安装依赖 (首次)
npm install

# 3. 启动开发服务器
npm run dev

# 服务地址: http://localhost:3000
# 编辑器地址: http://localhost:3000/canvas-editor/
```

### 2. 服务验证

#### 后端服务验证
```bash
# 健康检查
curl http://127.0.0.1:8000/api/health/

# 文档列表
curl http://127.0.0.1:8000/api/documents/
```

#### 前端服务验证
```javascript
// 浏览器控制台
// 1. 显示API测试面板
showApiTestPanel()

// 2. 测试API连接
await window.editorApiIntegration.testApiConnection()

// 3. 保存文档测试
await window.editorApiIntegration.saveCurrentDocument()
```

## 🔧 调试和监控

### 1. 日志配置

#### 后端日志
```python
# Django设置中的日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'api': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

#### 前端日志
```typescript
// API配置中的日志设置
export const LOG_CONFIG = {
  ENABLE_REQUEST_LOG: true,    // 请求日志
  ENABLE_RESPONSE_LOG: true,   // 响应日志
  ENABLE_ERROR_LOG: true,      // 错误日志
} as const
```

### 2. 开发工具

#### API测试面板
- **位置**: 浏览器控制台调用 `showApiTestPanel()`
- **功能**:
  - ✅ 健康检查测试
  - ✅ 文档管理测试
  - ✅ 连接状态测试
  - ✅ 实时结果显示

#### 全局调试对象
```javascript
// 浏览器控制台可用的全局对象
window.editor                 // Canvas Editor实例
window.editorApiIntegration   // API集成实例
window.apiTestPanel          // API测试面板
window.showApiTestPanel      // 显示测试面板函数
```

## 📊 性能和优化

### 1. 自动保存优化
- **防抖延迟**: 5秒 (内容变化后5秒触发保存)
- **自动保存间隔**: 30秒 (定时自动保存)
- **保存状态提示**: 实时显示保存状态

### 2. 请求优化
- **超时设置**: 30秒
- **重试机制**: 支持自动重试
- **错误处理**: 完善的错误提示

### 3. 缓存策略
- **本地存储**: 支持文档ID缓存
- **会话存储**: 临时数据存储
- **内存缓存**: API响应缓存

## 🔒 安全配置

### 1. CORS配置
```python
# Django CORS设置
CORS_ALLOWED_ORIGINS = [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
]
CORS_ALLOW_CREDENTIALS = True
```

### 2. 认证配置
```python
# REST Framework认证设置
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',  # 开发环境
    ],
}
```

### 3. 数据验证
- ✅ 前端TypeScript类型检查
- ✅ 后端Django模型验证
- ✅ API序列化器验证

## 📝 维护和更新

### 1. 依赖更新
```bash
# 后端依赖更新
pip list --outdated
pip install --upgrade package_name

# 前端依赖更新
npm outdated
npm update package_name
```

### 2. 数据库迁移
```bash
# 创建迁移文件
python manage.py makemigrations

# 应用迁移
python manage.py migrate

# 查看迁移状态
python manage.py showmigrations
```

### 3. 配置备份
- **数据库备份**: 定期备份 `db.sqlite3`
- **配置文件备份**: 备份所有配置文件
- **代码版本控制**: 使用Git管理代码

## ✅ 配置检查清单

### 后端服务检查
- [x] Django服务正常启动 (端口8000)
- [x] 数据库迁移已应用 (19个迁移)
- [x] API端点正常响应
- [x] CORS配置正确
- [x] 虚拟环境激活

### 前端服务检查
- [x] Vite服务正常启动 (端口3000)
- [x] API代理配置正确
- [x] 编辑器正常加载
- [x] API集成初始化成功
- [x] 测试面板可用

### 集成功能检查
- [x] 前后端通信正常
- [x] 文档保存功能正常
- [x] 自动保存功能正常
- [x] 错误处理正常
- [x] 日志记录正常

## 🛠️ 故障排除

### 1. 常见问题及解决方案

#### 问题1: API连接失败
**现象**: 前端无法连接到后端API，控制台显示网络错误
**原因**:
- 后端服务未启动
- 端口冲突
- 代理配置错误

**解决方案**:
```bash
# 1. 检查后端服务状态
cd D:\canvas-editor\backend
.venv\Scripts\activate
python manage.py runserver 8000

# 2. 检查端口占用
netstat -ano | findstr :8000

# 3. 验证API响应
curl http://127.0.0.1:8000/api/health/
```

#### 问题2: CORS跨域错误
**现象**: 浏览器控制台显示CORS错误
**原因**: CORS配置不正确

**解决方案**:
```python
# 检查 backend/book_editor_backend/settings.py
CORS_ALLOWED_ORIGINS = [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
]
CORS_ALLOW_CREDENTIALS = True
```

#### 问题3: 自动保存失败
**现象**: 编辑器内容变化但不自动保存
**原因**:
- API集成未正确初始化
- 文档ID未设置
- 网络连接问题

**解决方案**:
```javascript
// 浏览器控制台检查
console.log(window.editorApiIntegration)
console.log(window.editorApiIntegration.getCurrentDocumentId())

// 手动测试保存
await window.editorApiIntegration.saveCurrentDocument()
```

#### 问题4: 数据库迁移错误
**现象**: Django启动时提示未应用迁移
**解决方案**:
```bash
cd D:\canvas-editor\backend
.venv\Scripts\activate
python manage.py makemigrations
python manage.py migrate
```

### 2. 性能监控

#### 后端性能监控
```python
# 在 views.py 中添加性能日志
import time
import logging

logger = logging.getLogger(__name__)

def performance_monitor(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
        return result
    return wrapper
```

#### 前端性能监控
```typescript
// API请求性能监控
const performanceMonitor = {
  logApiCall: (endpoint: string, duration: number) => {
    console.log(`API调用 ${endpoint} 耗时: ${duration}ms`)
    if (duration > 5000) {
      console.warn(`API调用 ${endpoint} 响应较慢: ${duration}ms`)
    }
  }
}
```

## 📈 扩展功能

### 1. 用户认证系统

#### JWT认证配置
```bash
# 安装JWT包
pip install djangorestframework-simplejwt
```

```python
# settings.py 配置
from datetime import timedelta

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
}

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
}
```

#### 前端认证集成
```typescript
// 认证服务
export class AuthService {
  static async login(username: string, password: string) {
    const response = await httpClient.post('/auth/login/', {
      username, password
    })

    // 保存令牌
    const { access, refresh } = response.data
    localStorage.setItem('access_token', access)
    localStorage.setItem('refresh_token', refresh)

    // 设置默认认证头
    httpClient.setAuthToken(access)

    return response.data
  }

  static logout() {
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    httpClient.removeAuthToken()
  }
}
```

### 2. 实时协作功能

#### WebSocket配置
```bash
# 安装WebSocket支持
pip install channels
pip install channels-redis
```

```python
# settings.py
INSTALLED_APPS = [
    # ...
    'channels',
]

ASGI_APPLICATION = 'book_editor_backend.asgi.application'

CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [('127.0.0.1', 6379)],
        },
    },
}
```

#### 前端WebSocket集成
```typescript
export class CollaborationService {
  private ws: WebSocket | null = null

  connect(documentId: string) {
    const wsUrl = `ws://127.0.0.1:8000/ws/document/${documentId}/`
    this.ws = new WebSocket(wsUrl)

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleCollaborationEvent(data)
    }
  }

  sendChange(change: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'document_change',
        data: change
      }))
    }
  }
}
```

### 3. 文件存储优化

#### 云存储配置
```python
# 安装云存储支持
# pip install django-storages boto3

# settings.py
DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
AWS_ACCESS_KEY_ID = 'your-access-key'
AWS_SECRET_ACCESS_KEY = 'your-secret-key'
AWS_STORAGE_BUCKET_NAME = 'your-bucket-name'
```

#### 本地文件存储优化
```python
# settings.py
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# urls.py
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # ... 其他URL配置
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
```

## 🔄 CI/CD配置

### 1. GitHub Actions配置

#### 后端测试配置 (`.github/workflows/backend.yml`)
```yaml
name: Backend Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.13

    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt

    - name: Run tests
      run: |
        cd backend
        python manage.py test

    - name: Run migrations check
      run: |
        cd backend
        python manage.py makemigrations --check
```

#### 前端测试配置 (`.github/workflows/frontend.yml`)
```yaml
name: Frontend Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'

    - name: Install dependencies
      run: |
        cd fontend
        npm install

    - name: Build project
      run: |
        cd fontend
        npm run build

    - name: Type check
      run: |
        cd fontend
        npx tsc --noEmit
```

### 2. Docker配置

#### 后端Dockerfile
```dockerfile
# backend/Dockerfile
FROM python:3.13-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
```

#### 前端Dockerfile
```dockerfile
# fontend/Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=0 /app/dist /usr/share/nginx/html

EXPOSE 80
```

#### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
    volumes:
      - ./backend:/app

  frontend:
    build: ./fontend
    ports:
      - "3000:80"
    depends_on:
      - backend

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
```

## 📊 监控和日志

### 1. 应用监控

#### 健康检查端点扩展
```python
# api/views.py
import psutil
import django
from django.db import connection

@api_view(['GET'])
def health_check_detailed(request):
    """详细的健康检查"""
    try:
        # 数据库连接检查
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")

        # 系统资源检查
        cpu_percent = psutil.cpu_percent()
        memory_percent = psutil.virtual_memory().percent

        return Response({
            'status': 'healthy',
            'timestamp': timezone.now().isoformat(),
            'version': '1.0.0',
            'django_version': django.get_version(),
            'database': 'connected',
            'system': {
                'cpu_usage': f"{cpu_percent}%",
                'memory_usage': f"{memory_percent}%"
            },
            'debug': settings.DEBUG
        })
    except Exception as e:
        return Response({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)
```

#### 前端错误监控
```typescript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error)

  // 发送错误报告到后端
  fetch('/api/error-report/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      message: event.error.message,
      stack: event.error.stack,
      url: window.location.href,
      timestamp: new Date().toISOString()
    })
  }).catch(console.error)
})

// API错误监控
const apiErrorMonitor = {
  reportError: (error: ApiError, context: string) => {
    console.error(`API错误 [${context}]:`, error)

    // 统计错误频率
    const errorKey = `api_error_${error.code}`
    const errorCount = parseInt(localStorage.getItem(errorKey) || '0') + 1
    localStorage.setItem(errorKey, errorCount.toString())

    // 错误频率过高时警告
    if (errorCount > 10) {
      console.warn(`API错误 ${error.code} 发生频率过高: ${errorCount}次`)
    }
  }
}
```

### 2. 日志管理

#### 结构化日志配置
```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/django.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'api': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## 📋 部署清单

### 生产环境部署准备

#### 1. 环境变量配置
```bash
# .env 文件
DEBUG=False
SECRET_KEY=your-production-secret-key
DATABASE_URL=postgresql://user:password@localhost/dbname
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
```

#### 2. 数据库配置
```python
# 生产环境数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DB_NAME'),
        'USER': os.environ.get('DB_USER'),
        'PASSWORD': os.environ.get('DB_PASSWORD'),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '5432'),
    }
}
```

#### 3. 静态文件配置
```python
# settings.py
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# 收集静态文件
# python manage.py collectstatic
```

#### 4. 安全配置
```python
# 生产环境安全设置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
```

---

**文档创建时间**: 2025年6月15日
**最后更新时间**: 2025年6月15日
**配置状态**: ✅ 完成并验证
**维护负责人**: 系统管理员

## 📞 技术支持

**项目路径**: `D:\canvas-editor`
**文档位置**: `D:\canvas-editor\Modifydoc\前后端API服务.md`
**配置文件**:
- 后端: `backend/book_editor_backend/settings.py`
- 前端: `fontend/vite.config.ts`, `fontend/src/api/config.ts`

**快速命令**:
```bash
# 启动后端
cd D:\canvas-editor\backend && .venv\Scripts\activate && python manage.py runserver 8000

# 启动前端
cd D:\canvas-editor\fontend && npm run dev

# API测试
# 浏览器控制台: showApiTestPanel()
```
