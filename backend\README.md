# Canvas Editor Backend

这是 Canvas Editor 项目的 Django 5 后端服务，用于处理数据存储和 API 服务。

## 🚀 项目特性

- **Django 5.0.7**: 最新的 Django 框架
- **多数据库支持**: 支持 MySQL（远程）和 SQLite（本地）
- **REST API**: 基于 Django REST Framework
- **CORS 支持**: 支持跨域请求
- **API 文档**: 自动生成的 Swagger/ReDoc 文档
- **环境配置**: 使用 .env 文件管理配置

## 📋 环境要求

- Python 3.8+
- Django 5.0.7
- MySQL 8.0+ (远程数据库)

## 🛠️ 安装和配置

### 1. 设置虚拟环境（推荐）

#### 方法一：自动化设置
```bash
cd backend
python setup_venv.py
```

#### 方法二：手动设置
```bash
cd backend
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

#### 方法三：使用便捷脚本
```bash
# Windows
activate_venv.bat

# Linux/Mac
source activate_venv.sh
```

### 2. 传统安装（不推荐）

```bash
cd backend
pip install -r requirements.txt
```

### 3. 环境配置

复制 `.env` 文件并根据需要修改配置：

```bash
# 数据库类型：'mysql' 或 'sqlite'
DATABASE_TYPE=sqlite

# MySQL 配置（当 DATABASE_TYPE=mysql 时使用）
MYSQL_NAME=book_editor
MYSQL_USER=book_editor
MYSQL_PASSWORD=eN2eB5mFKpA2PDmB
MYSQL_HOST=***********
MYSQL_PORT=3306
```

### 4. 数据库迁移

```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. 创建超级用户

```bash
python manage.py createsuperuser
```

### 6. 启动开发服务器

```bash
python manage.py runserver 8000
```

## 📚 API 文档

启动服务器后，可以访问以下地址查看 API 文档：

- **Swagger UI**: http://127.0.0.1:8000/api/docs/
- **ReDoc**: http://127.0.0.1:8000/api/redoc/
- **API Schema**: http://127.0.0.1:8000/api/schema/

## 🔗 主要端点

- **管理后台**: http://127.0.0.1:8000/admin/
- **健康检查**: http://127.0.0.1:8000/api/health/
- **API 根路径**: http://127.0.0.1:8000/api/

## 🗄️ 数据库配置

### SQLite（本地开发）
默认使用 SQLite 数据库，数据文件位于 `db.sqlite3`。

### MySQL（远程生产）
配置信息：
- 主机: ***********
- 端口: 3306
- 数据库: book_editor
- 用户: book_editor
- 密码: eN2eB5mFKpA2PDmB

## 📁 项目结构

```
backend/
├── book_editor_backend/    # Django 项目配置
│   ├── settings.py        # 主配置文件
│   ├── urls.py           # 主 URL 配置
│   └── wsgi.py           # WSGI 配置
├── api/                  # API 应用
│   ├── views.py         # API 视图
│   ├── urls.py          # API URL 配置
│   └── models.py        # 数据模型
├── requirements.txt      # Python 依赖
├── .env                 # 环境变量配置
├── manage.py           # Django 管理脚本
└── README.md           # 项目文档
```

## 🔧 开发指南

### 添加新的 API 端点

1. 在 `api/views.py` 中添加视图函数或类
2. 在 `api/urls.py` 中添加 URL 路由
3. 运行测试确保功能正常

### 数据库模型

在 `api/models.py` 中定义数据模型，然后运行：

```bash
python manage.py makemigrations
python manage.py migrate
```

## 🚀 部署

### 生产环境配置

1. 设置 `DEBUG=False`
2. 配置 `ALLOWED_HOSTS`
3. 使用 MySQL 数据库
4. 配置静态文件服务
5. 使用 Gunicorn 或 uWSGI

### 使用 Gunicorn

```bash
pip install gunicorn
gunicorn book_editor_backend.wsgi:application
```

## 📝 注意事项

- 确保 MySQL 服务器可访问
- 生产环境中请更改默认的 SECRET_KEY
- 定期备份数据库
- 监控服务器性能和日志
