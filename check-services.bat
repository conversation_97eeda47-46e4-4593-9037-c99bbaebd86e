@echo off
echo ========================================
echo    Canvas Editor 服务状态检查
echo ========================================
echo.

REM 设置窗口标题
title Canvas Editor Service Check

echo 🔍 检查服务状态...
echo.

REM 检查后端服务
echo 📡 检查后端服务 (http://127.0.0.1:8000)...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://127.0.0.1:8000/api/health/' -Method Get -TimeoutSec 5; Write-Host '✅ 后端服务: 正常运行' -ForegroundColor Green; Write-Host '   状态: ' $response.status -ForegroundColor Cyan; Write-Host '   版本: ' $response.version -ForegroundColor Cyan; Write-Host '   数据库: ' $response.database -ForegroundColor Cyan; Write-Host '   时间: ' $response.timestamp -ForegroundColor Cyan } catch { Write-Host '❌ 后端服务: 连接失败' -ForegroundColor Red; Write-Host '   错误: ' $_.Exception.Message -ForegroundColor Yellow }"

echo.

REM 检查前端服务
echo 🌐 检查前端服务 (http://localhost:3000)...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000/Book-Editor/' -Method Get -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ 前端服务: 正常运行' -ForegroundColor Green; Write-Host '   状态码: ' $response.StatusCode -ForegroundColor Cyan } else { Write-Host '⚠️ 前端服务: 响应异常' -ForegroundColor Yellow; Write-Host '   状态码: ' $response.StatusCode -ForegroundColor Yellow } } catch { Write-Host '❌ 前端服务: 连接失败' -ForegroundColor Red; Write-Host '   错误: ' $_.Exception.Message -ForegroundColor Yellow }"

echo.

REM 检查端口占用
echo 🔌 检查端口占用情况...
echo.
echo 端口 8000 (后端):
netstat -an | findstr ":8000 " > nul
if %errorlevel% == 0 (
    echo ✅ 端口 8000 正在使用
    netstat -ano | findstr ":8000 " | findstr "LISTENING"
) else (
    echo ❌ 端口 8000 未被占用
)

echo.
echo 端口 3000 (前端):
netstat -an | findstr ":3000 " > nul
if %errorlevel% == 0 (
    echo ✅ 端口 3000 正在使用
    netstat -ano | findstr ":3000 " | findstr "LISTENING"
) else (
    echo ❌ 端口 3000 未被占用
)

echo.
echo ========================================
echo 📋 访问地址:
echo    登录页面:     http://localhost:3001/login/
echo    主编辑器:     http://localhost:3001/Book-Editor/
echo    API测试面板:  http://localhost:3001/Book-Editor/API
echo    后端管理:     http://127.0.0.1:8000/admin/
echo    API文档:      http://127.0.0.1:8000/api/docs/
echo    健康检查:     http://127.0.0.1:8000/api/health/
echo ========================================
echo.

REM 提供快速操作选项
echo 🔧 快速操作:
echo    1. 打开主编辑器
echo    2. 打开API测试面板
echo    3. 打开后端管理
echo    4. 重新检查服务状态
echo    5. 退出
echo.

set /p choice="请选择操作 (1-5): "

if "%choice%"=="1" (
    echo 🚀 打开主编辑器...
    start http://localhost:3001/Book-Editor/
    goto end
)

if "%choice%"=="2" (
    echo 🚀 打开API测试面板...
    start http://localhost:3001/Book-Editor/API
    goto end
)

if "%choice%"=="3" (
    echo 🚀 打开后端管理...
    start http://127.0.0.1:8000/admin/
    goto end
)

if "%choice%"=="4" (
    echo 🔄 重新检查...
    echo.
    goto :eof
    call "%~f0"
    goto end
)

if "%choice%"=="5" (
    goto end
)

echo ❌ 无效选择，请重新运行脚本
echo.

:end
echo.
echo 按任意键退出...
pause > nul
