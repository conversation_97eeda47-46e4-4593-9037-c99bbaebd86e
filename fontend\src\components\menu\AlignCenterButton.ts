import { RowFlex } from '../../editor/dataset/enum/Row'
import { CanvasEditor } from '../../editor'
import html from './AlignCenterButton.html'
import './AlignCenterButton.css'

export class AlignCenterButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    // 设置快捷键提示
    const isApple = /Mac|iPod|iPhone|iPad/.test(navigator.platform)
    this.dom.title = `居中对齐(${isApple ? '⌘' : 'Ctrl'}+E)`
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executeRowFlex(RowFlex.CENTER)
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 