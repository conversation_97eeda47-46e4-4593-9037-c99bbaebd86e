import { IPageNumber } from '../../interface/PageNumber'
import { NumberType } from '../enum/Common'
import { RowFlex } from '../enum/Row'
import { FORMAT_PLACEHOLDER } from './Common'

export const PAGE_NUMBER_STYLE = 'page-number'

export const defaultPageNumberOption: Readonly<Required<IPageNumber>> = {
  bottom: 60,
  size: 12,
  font: 'Microsoft YaHei',
  color: '#000000',
  rowFlex: RowFlex.CENTER,
  format: FORMAT_PLACEHOLDER.PAGE_NO,
  numberType: NumberType.ARABIC,
  disabled: false,
  startPageNo: 1,
  fromPageNo: 1,
  maxPageNo: null
}