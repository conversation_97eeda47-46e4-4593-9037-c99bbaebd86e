import Editor from '../../editor'
import html from './RightTools.html'
import './RightTools.css'
import FormulaTools from './formula'
import { TypesetTools } from './typeset'

export class RightTools {
  private dom: HTMLDivElement
  private instance: Editor
  private activeTab = 'typography' // 默认激活排版标签页
  private activePrimaryTab = 'book-layout' // 默认激活一级标签页
  private isVisible = false
  private formulaToolsInstance: FormulaTools | null = null // 公式工具实例
  private typesetToolsInstance: TypesetTools | null = null // 排版工具实例

  constructor(instance: Editor) {
    this.instance = instance

    // 创建DOM元素
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html

    // 从模板中获取根元素
    const rootElement = tempDiv.firstElementChild as HTMLDivElement
    if (rootElement) {
      this.dom = rootElement
    } else {
      console.error('RightTools: 无法解析HTML模板')
      this.dom = document.createElement('div')
      this.dom.className = 'right-tools'
      this.dom.setAttribute('editor-component', 'right-tools')
    }

    // 初始化组件
    this.init()
  }

  /**
   * 初始化工具栏组件
   * 设置基本事件和初始状态
   */
  private init(): void {
    try {
      // 绑定关闭按钮事件
      this.bindCloseEvent()

      // 初始化公式工具组件
      this.initFormulaTools()

      // 初始化排版工具组件
      this.initTypesetTools()

      // 绑定一级标签页切换事件
      this.bindPrimaryTabEvents()

      // 绑定二级标签页切换事件
      this.bindTabEvents()

      // 初始化标签状态（移除图标相关逻辑）
      this.switchTabState(this.activeTab)

      // 修改：默认显示工具栏，不再设置隐藏
      this.isVisible = true
      this.dom.style.display = 'block'

    } catch (error) {
      console.error('RightTools初始化失败:', error)
    }
  }

  /**
   * 初始化公式工具组件
   */
  private initFormulaTools(): void {
    try {
      // 查找公式内容区域
      const formulaContent = this.dom.querySelector('.right-tools__content[data-tab="formula"]')

      if (formulaContent) {
        // 清空占位内容
        formulaContent.innerHTML = ''

        // 创建公式工具实例
        this.formulaToolsInstance = new FormulaTools(this.instance)

        // 将公式工具组件添加到内容区域
        formulaContent.appendChild(this.formulaToolsInstance.getElement())
      } else {
        console.error('未找到公式内容区域')
      }
    } catch (error) {
      console.error('初始化公式工具组件失败:', error)
    }
  }

  /**
   * 初始化排版工具组件
   * 创建TypesetTools实例并将其添加到排版标签页中
   */
  private initTypesetTools(): void {
    try {
      // 查找排版工具容器
      const typesetContainer = this.dom.querySelector('.typeset-container')
      if (typesetContainer) {
        // 清空容器
        typesetContainer.innerHTML = ''

        // 创建TypesetTools实例
        this.typesetToolsInstance = new TypesetTools(this.instance)

        // 将排版工具组件添加到容器中
        typesetContainer.appendChild(this.typesetToolsInstance.getElement())
      } else {
        console.error('未找到排版工具容器')
      }
    } catch (error) {
      console.error('初始化排版工具组件失败:', error)
    }
  }

  /**
   * 绑定关闭按钮事件
   * 点击关闭按钮时隐藏工具栏
   */
  private bindCloseEvent(): void {
    try {
      const closeContainer = this.dom.querySelector('.right-tools__header__close')
      if (closeContainer) {
        closeContainer.addEventListener('click', () => {
          this.hide()
        })
      } else {
        console.warn('RightTools: 未找到关闭按钮容器')
      }
    } catch (error) {
      console.error('RightTools: 绑定关闭按钮事件失败', error)
    }
  }

  /**
   * 绑定一级标签页切换事件
   * 处理图书排版等一级分类的切换
   */
  private bindPrimaryTabEvents(): void {
    try {
      const primaryTabs = this.dom.querySelectorAll('.right-tools__primary-tab')

      if (primaryTabs.length === 0) {
        console.warn('RightTools: 未找到一级标签页元素')
        return
      }

      // 绑定一级标签事件
      primaryTabs.forEach(tab => {
        const tabElement = tab as HTMLElement
        tabElement.addEventListener('click', (e) => {
          e.preventDefault()
          e.stopPropagation()

          const primaryTabId = tabElement.getAttribute('data-primary-tab')
          if (!primaryTabId) return

          // 切换一级标签激活状态
          const allPrimaryTabs = this.dom.querySelectorAll('.right-tools__primary-tab')
          allPrimaryTabs.forEach(t => {
            (t as HTMLElement).classList.remove('active')
            const span = t.querySelector('span')
            if (span) {
              (span as HTMLElement).style.color = '#4991f2';
              (span as HTMLElement).style.fontWeight = '600';
            }
          })

          // 激活当前一级标签
          tabElement.classList.add('active')
          const span = tabElement.querySelector('span')
          if (span) {
            (span as HTMLElement).style.color = '#4991f2';
            (span as HTMLElement).style.fontWeight = 'bold';
            (span as HTMLElement).style.fontSize = '15px';
          }

          // 设置当前活动一级标签
          this.activePrimaryTab = primaryTabId

          // 切换一级标签对应的内容区域
          this.switchPrimaryTabContent(primaryTabId)

          console.log(`切换到一级标签: ${primaryTabId}`)
        })
      })

      // 激活默认一级标签
      this.activatePrimaryTab(this.activePrimaryTab)
    } catch (error) {
      console.error('RightTools: 绑定一级标签页切换事件失败', error)
    }
  }

  /**
   * 激活指定一级标签页
   * @param primaryTabId 一级标签页ID
   */
  public activatePrimaryTab(primaryTabId: string): void {
    try {
      const primaryTab = this.dom.querySelector(`.right-tools__primary-tab[data-primary-tab="${primaryTabId}"]`)
      if (primaryTab) {
        // 模拟点击事件
        setTimeout(() => {
          (primaryTab as HTMLElement).click()
        }, 50)
      } else {
        console.error(`未找到一级标签页: ${primaryTabId}`)
      }
    } catch (error) {
      console.error(`RightTools: 激活一级标签页 ${primaryTabId} 失败`, error)
    }
  }

  /**
   * 切换一级标签对应的内容区域
   * @param primaryTabId 一级标签页ID
   */
  private switchPrimaryTabContent(primaryTabId: string): void {
    try {
      // 获取所有一级标签内容区域
      const primaryContents = this.dom.querySelectorAll('.right-tools__content[data-primary-tab]')

      // 隐藏所有一级标签内容
      primaryContents.forEach(content => {
        (content as HTMLElement).classList.remove('active')
        ;(content as HTMLElement).style.display = 'none'
      })

      // 显示当前一级标签对应的内容
      const activeContent = this.dom.querySelector(`.right-tools__content[data-primary-tab="${primaryTabId}"]`)
      if (activeContent) {
        (activeContent as HTMLElement).classList.add('active')
        ;(activeContent as HTMLElement).style.display = 'block'

        // 如果是图书编排标签，需要重新初始化二级标签事件
        if (primaryTabId === 'book-layout') {
          // 延迟执行以确保DOM已更新
          setTimeout(() => {
            this.bindTabEvents()
          }, 100)
        }
      } else {
        console.warn(`未找到一级标签内容区域: ${primaryTabId}`)
      }
    } catch (error) {
      console.error(`RightTools: 切换一级标签内容失败`, error)
    }
  }

  /**
   * 切换标签页状态（移除图标相关逻辑）
   * @param activeTabId 当前激活的标签页ID
   */
  private switchTabState(activeTabId: string): void {
    try {
      // 只在图书编排内容区域内查找二级标签
      const bookLayoutContent = this.dom.querySelector('.right-tools__content[data-primary-tab="book-layout"]')
      if (!bookLayoutContent) {
        console.warn('RightTools: 未找到图书编排内容区域')
        return
      }

      // 获取图书编排区域内的所有二级标签
      const tabs = bookLayoutContent.querySelectorAll('.right-tools__tab')

      tabs.forEach((tab) => {
        const tabElement = tab as HTMLElement
        const tabId = tabElement.getAttribute('data-tab')
        const spanElement = tabElement.querySelector('span')

        if (tabId && spanElement) {
          // 重置所有标签状态
          tabElement.classList.remove('active')
          spanElement.style.color = '#606266';
          spanElement.style.fontWeight = '500';

          // 设置激活标签状态
          if (tabId === activeTabId) {
            tabElement.classList.add('active')
            spanElement.style.color = '#4991f2';
            spanElement.style.fontWeight = 'bold';
          }
        }
      })
    } catch (error) {
      console.error('RightTools: 切换标签状态失败', error)
    }
  }

  /**
   * 绑定标签页切换事件
   * 点击标签时切换显示对应内容区域
   */
  private bindTabEvents(): void {
    try {
      // 只在图书编排内容区域内查找二级标签
      const bookLayoutContent = this.dom.querySelector('.right-tools__content[data-primary-tab="book-layout"]')
      if (!bookLayoutContent) {
        console.warn('RightTools: 未找到图书编排内容区域')
        return
      }

      const tabs = bookLayoutContent.querySelectorAll('.right-tools__tab')
      const tabContents = bookLayoutContent.querySelectorAll('.right-tools__content[data-tab]')

      if (tabs.length === 0) {
        console.warn('RightTools: 未找到二级标签页元素')
        return
      }

      // 移除所有现有事件监听器，防止重复绑定
      tabs.forEach(tab => {
        const tabElement = tab as HTMLElement
        const newTab = tabElement.cloneNode(true) as HTMLElement
        tabElement.parentNode?.replaceChild(newTab, tabElement)
      })

      // 重新获取标签元素
      const newTabs = this.dom.querySelectorAll('.right-tools__tab')

      // 绑定新的事件监听器
      newTabs.forEach(tab => {
        const tabElement = tab as HTMLElement
        tabElement.addEventListener('click', (e) => {
          e.preventDefault()
          e.stopPropagation()

          const tabId = tabElement.getAttribute('data-tab')
          if (!tabId) return

          // 切换标签激活状态 - 重新获取最新的标签列表（只在图书编排区域内）
          const bookLayoutContent = this.dom.querySelector('.right-tools__content[data-primary-tab="book-layout"]')
          if (!bookLayoutContent) return

          const allCurrentTabs = bookLayoutContent.querySelectorAll('.right-tools__tab')
          allCurrentTabs.forEach(t => {
            (t as HTMLElement).classList.remove('active')
            // 移除旧的指示器
            const oldIndicator = t.querySelector('.tab-indicator')
            if (oldIndicator) {
              oldIndicator.remove()
            }

            // 重置样式
            (t as HTMLElement).style.background = '#f0f0f0';
            (t as HTMLElement).style.borderBottom = '1px solid #e2e6ed';

            // 重置文字样式
            const span = t.querySelector('span')
            if (span) {
              (span as HTMLElement).style.color = '#606266';
              (span as HTMLElement).style.fontWeight = 'normal';
            }

            // 图标相关代码已移除
          })

          tabElement.classList.add('active')

          // 添加新的指示器
          const indicator = document.createElement('div')
          indicator.classList.add('tab-indicator')
          indicator.style.position = 'absolute'
          indicator.style.top = '0'
          indicator.style.left = '0'
          indicator.style.width = '100%'
          indicator.style.height = '2px'
          indicator.style.background = '#4991f2';
          tabElement.appendChild(indicator)

          // 更改tab样式
          tabElement.style.background = '#fff';
          tabElement.style.borderBottom = '1px solid #fff';

          // 更新文字样式
          const span = tabElement.querySelector('span')
          if (span) {
            (span as HTMLElement).style.color = '#4991f2';
            (span as HTMLElement).style.fontWeight = 'bold';
          }

          // 图标相关代码已移除

          // 切换内容区域显示（二级标签内容）
          tabContents.forEach(content => {
            content.classList.remove('active');
            (content as HTMLElement).style.display = 'none'
            if (content.getAttribute('data-tab') === tabId) {
              content.classList.add('active');
              (content as HTMLElement).style.display = 'block'
            }
          })

          // 切换标签状态
          this.switchTabState(tabId)

          this.activeTab = tabId
        })
      })

      // 激活默认标签页
      this.activateTab(this.activeTab)
    } catch (error) {
      console.error('RightTools: 绑定标签页切换事件失败', error)
    }
  }

  /**
   * 激活指定标签页
   * @param tabId 标签页ID
   */
  public activateTab(tabId: string): void {
    try {
      // 只在图书编排内容区域内查找二级标签
      const bookLayoutContent = this.dom.querySelector('.right-tools__content[data-primary-tab="book-layout"]')
      if (!bookLayoutContent) {
        console.warn('RightTools: 未找到图书编排内容区域')
        return
      }

      const tab = bookLayoutContent.querySelector(`.right-tools__tab[data-tab="${tabId}"]`)
      if (tab) {
        // 模拟点击事件
        setTimeout(() => {
          (tab as HTMLElement).click()
        }, 100)
      } else {
        console.error(`未找到标签页: ${tabId}`)
        // 列出所有标签
        const tabs = bookLayoutContent.querySelectorAll('.right-tools__tab')
        console.log(`可用标签页:`, Array.from(tabs).map(t => t.getAttribute('data-tab')))

        // 强制显示第一个标签内容
        if (tabs.length > 0 && bookLayoutContent.querySelectorAll('.right-tools__content[data-tab]').length > 0) {
          const firstTabId = tabs[0].getAttribute('data-tab')
          if (firstTabId) {

            (tabs[0] as HTMLElement).classList.add('active');
            (tabs[0] as HTMLElement).style.background = '#fff';
            (tabs[0] as HTMLElement).style.borderBottom = '1px solid #fff';

            const span = tabs[0].querySelector('span')
            if (span) {
              (span as HTMLElement).style.color = '#4991f2';
              (span as HTMLElement).style.fontWeight = 'bold';
            }

            const contents = bookLayoutContent.querySelectorAll('.right-tools__content[data-tab]')
            contents.forEach(content => {
              if (content.getAttribute('data-tab') === firstTabId) {
                content.classList.add('active');
                (content as HTMLElement).style.display = 'block'
              }
            })
          }
        }
      }
    } catch (error) {
      console.error(`RightTools: 激活标签页 ${tabId} 失败`, error)
    }
  }

  /**
   * 显示工具栏
   */
  public show(): void {
    // 获取容器并检查状态
    // const container = document.querySelector('.right-tools-container')

    // if (container) {
    //   // 强制设置容器样式
    //   container.setAttribute('style', 'display:block !important; width:300px !important; background:#fff; position:fixed; right:0; top:70px; bottom:30px; z-index:1500; overflow:hidden;');
    // }

    // 确保DOM元素存在于页面中
    if (!this.dom.isConnected) {
      const container = document.querySelector('.right-tools-container')
      if (container) {
        container.innerHTML = '' // 清空容器
        container.appendChild(this.dom)
      }
    }

    // 强制设置显示样式 - TabControl风格
    this.dom.style.cssText = 'display:flex !important; flex-direction:column !important; width:100% !important; height:100% !important; overflow:hidden !important;'
    this.isVisible = true

    // 设置header样式
    const header = this.dom.querySelector('.right-tools__header')
    if (header) {
      (header as HTMLElement).style.cssText = 'height:38px; min-height:38px; display:flex !important; align-items:center; justify-content:space-between; border-bottom:1px solid #e2e6ed; padding:0 5px; background:#f5f7fa; box-sizing:border-box;'

      const headerTitle = header.querySelector('span')
      if (headerTitle) {
        (headerTitle as HTMLElement).style.cssText = 'color:#3d4757; font-size:14px; font-weight:bold; display:inline-block;'
      }

      const closeBtn = header.querySelector('.right-tools__header__close')
      if (closeBtn) {
        (closeBtn as HTMLElement).style.cssText = 'width:24px; height:24px; display:flex; align-items:center; justify-content:center; cursor:pointer;'
      }
    }

    // 设置tab样式 - 水平排列
    const tabsContainer = this.dom.querySelector('.right-tools__tabs')
    if (tabsContainer) {
      (tabsContainer as HTMLElement).style.cssText = 'display:flex !important; flex-direction:row !important; background:#f5f7fa; border-bottom:1px solid #e2e6ed; height:40px; min-height:40px; width:100%; flex-shrink:0;'
    }

    // 重新绑定事件监听器
    const tabs = this.dom.querySelectorAll('.right-tools__tab')
    tabs.forEach(tab => {
      const tabElement = tab as HTMLElement

      // 删除原有的事件监听器
      const newTab = tabElement.cloneNode(true) as HTMLElement
      if (tabElement.parentNode) {
        tabElement.parentNode.replaceChild(newTab, tabElement)
      }

      // 给标签添加强制点击事件
      newTab.addEventListener('click', (event) => {
        event.preventDefault()
        event.stopPropagation()

        const tabId = newTab.getAttribute('data-tab')

        if (!tabId) return

        // 更新所有标签状态 - 重新获取最新的标签列表（只在图书编排区域内）
        const bookLayoutContent = this.dom.querySelector('.right-tools__content[data-primary-tab="book-layout"]')
        if (!bookLayoutContent) return

        const allTabs = bookLayoutContent.querySelectorAll('.right-tools__tab')
        allTabs.forEach(t => {
          (t as HTMLElement).classList.remove('active');
          (t as HTMLElement).style.background = '#f0f0f0';
          (t as HTMLElement).style.borderBottom = '1px solid #e2e6ed';

          const tspan = t.querySelector('span')
          if (tspan) {
            (tspan as HTMLElement).style.color = '#606266';
            (tspan as HTMLElement).style.fontWeight = 'normal';
          }

          // 图标相关代码已移除
        })

        // 激活当前标签
        newTab.classList.add('active')
        newTab.style.background = '#fff';
        newTab.style.borderBottom = '1px solid #fff';

        const span = newTab.querySelector('span')
        if (span) {
          (span as HTMLElement).style.color = '#4991f2';
          (span as HTMLElement).style.fontWeight = 'bold';
        }

        // 图标相关代码已移除

        // 切换内容区域（只在图书编排区域内的二级内容）
        const bookLayoutContentForSwitch = this.dom.querySelector('.right-tools__content[data-primary-tab="book-layout"]')
        if (!bookLayoutContentForSwitch) return

        const contents = bookLayoutContentForSwitch.querySelectorAll('.right-tools__content[data-tab]')
        contents.forEach(content => {
          (content as HTMLElement).classList.remove('active');
          (content as HTMLElement).style.display = 'none'

          if (content.getAttribute('data-tab') === tabId) {
            (content as HTMLElement).classList.add('active');
            (content as HTMLElement).style.display = 'block'
          }
        })

        // 设置当前活动标签
        this.activeTab = tabId
      })

      // 设置样式（调整内边距以适应无图标布局，高度改为30px）
      newTab.style.cssText = 'display:flex !important; flex-direction:row !important; align-items:center !important; justify-content:center !important; padding:0 20px; height:30px; background:#f0f0f0; border-right:1px solid #e2e6ed; border-bottom:1px solid #e2e6ed; margin-bottom:-1px; flex:1; cursor:pointer;'

      // 调整文字样式（移除图标相关样式）
      const span = newTab.querySelector('span')
      if (span) {
        (span as HTMLElement).style.cssText = 'font-size:13px; color:#606266; display:inline-block !important; white-space:nowrap; font-weight:500;'
      }
    })

    // 设置内容区样式 - 更新高度计算以包含一级标签
    const contentsContainer = this.dom.querySelector('.right-tools__contents')
    if (contentsContainer) {
      (contentsContainer as HTMLElement).style.cssText = 'flex:1; position:relative; overflow:hidden; background:#fff; width:100%; height:calc(100% - 103px);'
    }

    // 设置一级标签内容区域样式
    const primaryContents = this.dom.querySelectorAll('.right-tools__content[data-primary-tab]')
    primaryContents.forEach(content => {
      (content as HTMLElement).style.cssText = 'position:absolute; top:0; left:0; width:100%; height:100%; padding:5px; box-sizing:border-box; overflow-y:auto; background:#fff; display:none;'

      // 设置占位样式
      const placeholder = content.querySelector('.right-tools__placeholder')
      if (placeholder) {
        (placeholder as HTMLElement).style.cssText = 'color:#909399; text-align:center; padding:5px; font-size:14px; border:1px dashed #dcdfe6; margin:20px 0; border-radius:4px; background:#fafafa; display:block !important;'
      }
    })

    // 设置图书编排区域内的二级标签内容样式
    const bookLayoutContentForStyle = this.dom.querySelector('.right-tools__content[data-primary-tab="book-layout"]')
    if (bookLayoutContentForStyle) {
      const tabContentsContainer = bookLayoutContentForStyle.querySelector('.right-tools__tab-contents')
      if (tabContentsContainer) {
        (tabContentsContainer as HTMLElement).style.cssText = 'position:relative; width:100%; height:calc(100% - 40px); overflow:hidden;'
      }

      const secondaryContents = bookLayoutContentForStyle.querySelectorAll('.right-tools__content[data-tab]')
      secondaryContents.forEach(content => {
        (content as HTMLElement).style.cssText = 'position:absolute; top:0; left:0; width:100%; height:100%; padding:5px; box-sizing:border-box; overflow-y:auto; background:#fff; display:none;'

        // 设置占位样式
        const placeholder = content.querySelector('.right-tools__placeholder')
        if (placeholder) {
          (placeholder as HTMLElement).style.cssText = 'color:#909399; text-align:center; padding:5px; font-size:14px; border:1px dashed #dcdfe6; margin:20px 0; border-radius:4px; background:#fafafa; display:block !important;'
        }
      })
    }

    // 设置图书编排区域内的排版工具样式
    primaryContents.forEach(content => {
      // 设置排版工具样式
      const typographyTools = content.querySelector('.typography-tools')
      if (typographyTools) {
        (typographyTools as HTMLElement).style.cssText = 'padding:10px; width:100%; height:100%; box-sizing:border-box;'

        // 设置排版区域样式
        const typographySection = typographyTools.querySelector('.typography-section')
        if (typographySection) {
          (typographySection as HTMLElement).style.cssText = 'margin-bottom:15px;'

          // 设置标题样式
          const sectionTitle = typographySection.querySelector('.typography-section-title')
          if (sectionTitle) {
            (sectionTitle as HTMLElement).style.cssText = 'font-size:14px; font-weight:bold; color:#3d4757; margin-bottom:8px; padding-bottom:5px; border-bottom:1px solid #e2e6ed;'
          }

          // 设置按钮容器样式
          const buttonsContainer = typographySection.querySelector('.typography-buttons')
          if (buttonsContainer) {
            (buttonsContainer as HTMLElement).style.cssText = 'display:flex; flex-wrap:wrap; gap:8px;'

            // 设置样式1按钮样式 - 纯文字按钮
            const style1Button = buttonsContainer.querySelector('.style1-button')
            if (style1Button) {
              (style1Button as HTMLElement).style.cssText = 'display:flex; align-items:center; justify-content:center; width:80px; height:36px; border:1px solid #d4d7de; border-radius:6px; background:#ffffff; cursor:pointer; transition:all 0.2s ease; box-shadow:0 1px 2px rgba(0,0,0,0.05); padding:0 12px;'

              // 设置按钮文字样式
              const buttonText = style1Button.querySelector('.typography-button-text')
              if (buttonText) {
                (buttonText as HTMLElement).style.cssText = 'font-size:14px; color:#333; text-align:center; line-height:1; font-weight:500; white-space:nowrap;'
              }

              // 添加悬停效果
              style1Button.addEventListener('mouseenter', () => {
                (style1Button as HTMLElement).style.background = '#f5f7fa';
                (style1Button as HTMLElement).style.borderColor = '#c0c4cc'
              })

              style1Button.addEventListener('mouseleave', () => {
                (style1Button as HTMLElement).style.background = '#ffffff';
                (style1Button as HTMLElement).style.borderColor = '#d4d7de'
              })
            }
          }
        }
      }
    })

    // 激活默认一级标签页
    this.activatePrimaryTab(this.activePrimaryTab)

    // 激活默认二级标签页（仅在图书编排区域）
    setTimeout(() => {
      const bookLayoutContent = this.dom.querySelector('.right-tools__content[data-primary-tab="book-layout"]')
      if (bookLayoutContent) {
        const activeTab = bookLayoutContent.querySelector(`.right-tools__tab[data-tab="${this.activeTab}"]`)
        if (activeTab) {
          (activeTab as HTMLElement).click()
        } else {
          const firstTab = bookLayoutContent.querySelector('.right-tools__tab')
          if (firstTab) {
            (firstTab as HTMLElement).click()
          }
        }
      }
    }, 200)
  }

  /**
   * 隐藏工具栏
   */
  public hide(): void {
    this.isVisible = false
    this.dom.style.display = 'none'
  }

  /**
   * 切换工具栏显示状态
   */
  public toggle(): void {
    if (this.isVisible) {
      this.hide()
    } else {
      this.show()
    }
  }

  /**
   * 获取工具栏显示状态
   * @returns 是否显示
   */
  public isShown(): boolean {
    return this.isVisible
  }

  /**
   * 获取工具栏DOM元素
   * @returns 工具栏DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }

  /**
   * 获取公式工具实例
   * @returns 公式工具实例
   */
  public getFormulaToolsInstance(): FormulaTools | null {
    return this.formulaToolsInstance
  }
}