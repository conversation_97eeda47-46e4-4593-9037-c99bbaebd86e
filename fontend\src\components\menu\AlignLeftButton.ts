import { RowFlex } from '../../editor/dataset/enum/Row'
import { CanvasEditor } from '../../editor'
import html from './AlignLeftButton.html'
import './AlignLeftButton.css'

export class AlignLeftButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    // 设置快捷键提示
    const isApple = /Mac|iPod|iPhone|iPad/.test(navigator.platform)
    this.dom.title = `左对齐(${isApple ? '⌘' : 'Ctrl'}+L)`
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executeRowFlex(RowFlex.LEFT)
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 