import { Editor } from '../../editor'
import html from './LineBreakButton.html'
import './LineBreakButton.css'

/**
 * 段落结束符号显示按钮组件
 * 用于控制段落结束符号的显示与隐藏
 * 支持状态持久化存储
 */
export class LineBreakButton {
  private dom: HTMLDivElement
  private instance: Editor
  private isActive = false
  private readonly storageKey = 'canvas-editor-line-break-display'

  constructor(instance: Editor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    // 从本地存储恢复状态
    this.loadStateFromStorage()

    // 初始化按钮状态
    this.initializeButtonState()

    this.bindEvents()
  }

  /**
   * 从本地存储加载状态
   */
  private loadStateFromStorage(): void {
    try {
      const savedState = localStorage.getItem(this.storageKey)
      if (savedState !== null) {
        this.isActive = JSON.parse(savedState)
      } else {
        // 如果没有保存状态，则从编辑器选项中获取
        const options = this.instance.command.getOptions()
        this.isActive = options.lineBreak ? !options.lineBreak.disabled : true
      }
    } catch (error) {
      console.warn('加载段落结束标记显示状态失败:', error)
      // 使用默认状态
      this.isActive = true
    }
  }

  /**
   * 保存状态到本地存储
   */
  private saveStateToStorage(): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.isActive))
    } catch (error) {
      console.warn('保存段落结束标记显示状态失败:', error)
    }
  }

  /**
   * 初始化按钮状态
   */
  private initializeButtonState(): void {
    // 获取当前所有选项，保持水印、页码等所有现有配置
    const options = this.instance.command.getOptions()
    const newOptions = {
      ...options,  // 保持所有现有配置，包括水印、页码等
      lineBreak: {
        ...options.lineBreak,
        disabled: !this.isActive
      }
    }

    // 静默更新选项，不触发重绘
    this.instance.command.executeUpdateOptions(newOptions)

    // 更新按钮视觉状态
    this.updateButtonState()
  }

  /**
   * 绑定事件处理器
   */
  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 切换段落结束符显示状态
      this.isActive = !this.isActive

      // 保存状态到本地存储
      this.saveStateToStorage()

      // 获取当前所有选项，保持水印、页码等所有现有配置
      const options = this.instance.command.getOptions()
      const newOptions = {
        ...options,  // 保持所有现有配置，包括水印、页码等
        lineBreak: {
          ...options.lineBreak,
          disabled: !this.isActive
        }
      }

      // 执行更新选项命令
      this.instance.command.executeUpdateOptions(newOptions)

      // 更新按钮状态
      this.updateButtonState()

      // 同步更新开始菜单中的按钮状态
      const homeButton = document.querySelector('.menu-item__home-line-break') as HTMLElement | null
      if (homeButton) {
        if (this.isActive) {
          homeButton.classList.add('active')
        } else {
          homeButton.classList.remove('active')
        }
      }
    }

    // 添加工具提示更新
    this.dom.addEventListener('mouseenter', () => {
      this.updateTooltip()
    })
  }

  /**
   * 更新工具提示
   */
  private updateTooltip(): void {
    const iconElement = this.dom.querySelector('i')
    if (iconElement) {
      iconElement.title = this.isActive ? '隐藏段落结束标记' : '显示段落结束标记'
    }
  }

  /**
   * 更新按钮的激活状态
   */
  private updateButtonState(): void {
    if (this.isActive) {
      this.dom.classList.add('active')
    } else {
      this.dom.classList.remove('active')
    }

    // 更新工具提示
    this.updateTooltip()
  }

  /**
   * 获取当前状态
   */
  public getState(): boolean {
    return this.isActive
  }

  /**
   * 设置状态（供外部调用）
   */
  public setState(active: boolean): void {
    if (this.isActive !== active) {
      this.isActive = active
      this.saveStateToStorage()
      this.updateButtonState()

      // 获取当前所有选项，保持水印、页码等所有现有配置
      const options = this.instance.command.getOptions()
      const newOptions = {
        ...options,  // 保持所有现有配置，包括水印、页码等
        lineBreak: {
          ...options.lineBreak,
          disabled: !this.isActive
        }
      }

      this.instance.command.executeUpdateOptions(newOptions)
    }
  }

  /**
   * 获取DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
}