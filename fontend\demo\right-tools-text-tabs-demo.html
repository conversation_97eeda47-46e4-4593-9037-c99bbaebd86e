<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>右侧工具栏纯文本标签演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .demo-header {
            background: #4991f2;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .demo-content {
            display: flex;
            height: 600px;
        }

        .main-area {
            flex: 1;
            padding: 20px;
            background: #fafafa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #666;
        }

        /* 模拟右侧工具栏样式 */
        .right-tools {
            width: 300px;
            background: #fff;
            border-left: 1px solid #e2e6ed;
            display: flex;
            flex-direction: column;
        }

        .right-tools__header {
            height: 38px;
            background: #f8f9fa;
            border-bottom: 1px solid #e2e6ed;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .right-tools__tabs {
            display: flex;
            background: #f0f0f0;
            border-bottom: 1px solid #e2e6ed;
        }

        .right-tools__tab {
            padding: 0 20px; /* 增加左右内边距以适应无图标布局 */
            height: 30px; /* 降低高度从40px到30px */
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all .2s;
            position: relative;
            background: #f0f0f0;
            border-right: 1px solid #e2e6ed;
            margin-bottom: -1px;
            user-select: none;
            flex: 1;
        }

        .right-tools__tab span {
            font-size: 13px;
            color: #606266;
            display: inline-block;
            white-space: nowrap;
            font-weight: 500;
        }

        .right-tools__tab.active {
            background: #fff;
            z-index: 1;
        }

        .right-tools__tab.active::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: #4991f2;
        }

        .right-tools__tab.active span {
            color: #4991f2;
            font-weight: bold;
            font-size: 14px;
        }

        /* 悬停效果 */
        .right-tools__tab:hover {
            background: #e8f4ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .right-tools__tab:hover span {
            color: #4991f2;
            font-weight: 600;
        }

        .right-tools__tab.active:hover {
            transform: none;
            box-shadow: none;
        }

        .right-tools__contents {
            flex: 1;
            position: relative;
            overflow: hidden;
            background: #fff;
        }

        .right-tools__content {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            overflow-y: auto;
            display: none;
            background: #fff;
        }

        .right-tools__content.active {
            display: block;
        }

        .content-placeholder {
            color: #909399;
            text-align: center;
            padding: 40px 20px;
            font-size: 14px;
            border: 1px dashed #dcdfe6;
            border-radius: 4px;
            background: #fafafa;
        }

        .comparison {
            margin: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .comparison h3 {
            margin-top: 0;
            color: #333;
        }

        .comparison-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #4991f2;
        }

        .before {
            border-left-color: #f56c6c;
        }

        .after {
            border-left-color: #67c23a;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>右侧工具栏纯文本标签演示</h1>
            <p>展示移除图标后的纯文本标签效果</p>
        </div>

        <div class="demo-content">
            <div class="main-area">
                <div>
                    <h2>主编辑区域</h2>
                    <p>右侧是修改后的纯文本标签工具栏</p>
                    <p>请点击右侧标签页查看效果</p>
                </div>
            </div>

            <div class="right-tools">
                <div class="right-tools__header">
                    工具栏
                </div>

                <div class="right-tools__tabs">
                    <div class="right-tools__tab active" data-tab="typography">
                        <span>排版</span>
                    </div>
                    <div class="right-tools__tab" data-tab="formula">
                        <span>公式</span>
                    </div>
                    <div class="right-tools__tab" data-tab="writing">
                        <span>编写</span>
                    </div>
                    <div class="right-tools__tab" data-tab="consulting">
                        <span>咨询</span>
                    </div>
                </div>

                <div class="right-tools__contents">
                    <div class="right-tools__content active" data-content="typography">
                        <div class="content-placeholder">
                            <h3>排版工具</h3>
                            <p>这里是排版相关的工具和选项</p>
                            <ul style="text-align: left; display: inline-block;">
                                <li>字体设置</li>
                                <li>段落格式</li>
                                <li>对齐方式</li>
                                <li>行间距</li>
                            </ul>
                        </div>
                    </div>

                    <div class="right-tools__content" data-content="formula">
                        <div class="content-placeholder">
                            <h3>公式编辑</h3>
                            <p>这里是数学公式编辑工具</p>
                            <ul style="text-align: left; display: inline-block;">
                                <li>LaTeX 公式</li>
                                <li>符号库</li>
                                <li>函数模板</li>
                                <li>公式预览</li>
                            </ul>
                        </div>
                    </div>

                    <div class="right-tools__content" data-content="writing">
                        <div class="content-placeholder">
                            <h3>编写助手</h3>
                            <p>这里是写作辅助工具</p>
                            <ul style="text-align: left; display: inline-block;">
                                <li>语法检查</li>
                                <li>拼写检查</li>
                                <li>词汇建议</li>
                                <li>写作模板</li>
                            </ul>
                        </div>
                    </div>

                    <div class="right-tools__content" data-content="consulting">
                        <div class="content-placeholder">
                            <h3>咨询服务</h3>
                            <p>这里是咨询相关功能</p>
                            <ul style="text-align: left; display: inline-block;">
                                <li>在线帮助</li>
                                <li>使用指南</li>
                                <li>常见问题</li>
                                <li>联系支持</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="comparison">
        <h3>修改对比</h3>

        <div class="comparison-item before">
            <strong>修改前：</strong>带图标的标签页
            <ul>
                <li>每个标签包含图标和文字</li>
                <li>需要管理图标状态切换</li>
                <li>图标文件依赖</li>
                <li>复杂的样式管理</li>
            </ul>
        </div>

        <div class="comparison-item after">
            <strong>修改后：</strong>纯文本标签页
            <ul>
                <li>简洁的纯文本标签</li>
                <li>更大的点击区域</li>
                <li>更好的可访问性</li>
                <li>简化的代码维护</li>
                <li>增强的悬停效果</li>
            </ul>
        </div>
    </div>

    <script>
        // 标签页切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.right-tools__tab')
            const contents = document.querySelectorAll('.right-tools__content')

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab')

                    // 移除所有激活状态
                    tabs.forEach(t => t.classList.remove('active'))
                    contents.forEach(c => c.classList.remove('active'))

                    // 设置当前标签为激活状态
                    this.classList.add('active')

                    // 显示对应内容
                    const targetContent = document.querySelector(`[data-content="${tabId}"]`)
                    if (targetContent) {
                        targetContent.classList.add('active')
                    }
                })
            })
        })
    </script>
</body>
</html>
