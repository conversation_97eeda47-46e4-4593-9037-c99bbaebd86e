<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签页高度测试 - 30px</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: #4991f2;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .demo-area {
            padding: 20px;
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .comparison-title {
            background: #f8f9fa;
            padding: 10px;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px solid #ddd;
        }
        
        /* 原始40px高度样式 */
        .tabs-40px {
            display: flex;
            background: #f0f0f0;
            border-bottom: 1px solid #e2e6ed;
        }
        
        .tab-40px {
            padding: 0 20px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background: #f0f0f0;
            border-right: 1px solid #e2e6ed;
            flex: 1;
            font-size: 13px;
            color: #606266;
            font-weight: 500;
        }
        
        .tab-40px.active {
            background: #fff;
            color: #4991f2;
            font-weight: bold;
        }
        
        /* 新的30px高度样式 */
        .tabs-30px {
            display: flex;
            background: #f0f0f0;
            border-bottom: 1px solid #e2e6ed;
        }
        
        .tab-30px {
            padding: 0 20px;
            height: 30px; /* 新的高度 */
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background: #f0f0f0;
            border-right: 1px solid #e2e6ed;
            flex: 1;
            font-size: 13px;
            color: #606266;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .tab-30px.active {
            background: #fff;
            color: #4991f2;
            font-weight: bold;
            font-size: 14px;
        }
        
        .tab-30px:hover {
            background: #e8f4ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .tab-30px.active:hover {
            transform: none;
            box-shadow: none;
        }
        
        .content-area {
            padding: 20px;
            background: #fff;
            min-height: 100px;
            text-align: center;
            color: #666;
        }
        
        .measurements {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .measurement-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px solid #ddd;
        }
        
        .measurement-item:last-child {
            border-bottom: none;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>标签页高度对比测试</h1>
            <p>对比40px和30px高度的视觉效果</p>
        </div>
        
        <div class="demo-area">
            <div class="measurements">
                <h3>📏 尺寸对比</h3>
                <div class="measurement-item">
                    <span>原始标签高度:</span>
                    <span class="highlight">40px</span>
                </div>
                <div class="measurement-item">
                    <span>新标签高度:</span>
                    <span class="highlight">30px</span>
                </div>
                <div class="measurement-item">
                    <span>高度减少:</span>
                    <span class="highlight">10px (25%)</span>
                </div>
                <div class="measurement-item">
                    <span>Header高度:</span>
                    <span>38px</span>
                </div>
                <div class="measurement-item">
                    <span>总工具栏高度 (原始):</span>
                    <span>78px (38px + 40px)</span>
                </div>
                <div class="measurement-item">
                    <span>总工具栏高度 (新):</span>
                    <span class="highlight">68px (38px + 30px)</span>
                </div>
            </div>
            
            <div class="comparison">
                <div class="comparison-item">
                    <div class="comparison-title">原始高度 (40px)</div>
                    <div class="tabs-40px">
                        <div class="tab-40px active">排版</div>
                        <div class="tab-40px">公式</div>
                        <div class="tab-40px">编写</div>
                        <div class="tab-40px">咨询</div>
                    </div>
                    <div class="content-area">
                        <p>原始40px高度的标签页</p>
                        <p>较高的标签提供更大的点击区域</p>
                    </div>
                </div>
                
                <div class="comparison-item">
                    <div class="comparison-title">新高度 (30px)</div>
                    <div class="tabs-30px">
                        <div class="tab-30px active">排版</div>
                        <div class="tab-30px">公式</div>
                        <div class="tab-30px">编写</div>
                        <div class="tab-30px">咨询</div>
                    </div>
                    <div class="content-area">
                        <p>新的30px高度的标签页</p>
                        <p>更紧凑的设计，节省垂直空间</p>
                        <p>包含悬停效果增强交互体验</p>
                    </div>
                </div>
            </div>
            
            <div class="measurements">
                <h3>✨ 改进效果</h3>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>节省空间:</strong> 减少10px高度，为内容区域提供更多空间</li>
                    <li><strong>视觉平衡:</strong> 更紧凑的标签页设计，视觉上更加协调</li>
                    <li><strong>保持可用性:</strong> 30px高度仍然提供足够的点击区域</li>
                    <li><strong>增强交互:</strong> 添加了悬停效果，提升用户体验</li>
                    <li><strong>响应式友好:</strong> 在小屏幕设备上更加节省空间</li>
                </ul>
            </div>
            
            <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #155724;">✅ 修改完成</h4>
                <p style="margin-bottom: 0; color: #155724;">
                    右侧工具栏标签页高度已成功从40px调整为30px。
                    内容区域高度计算也已相应更新，确保布局正确。
                </p>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为30px标签添加点击切换效果
            const tabs30px = document.querySelectorAll('.tab-30px');
            tabs30px.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除其他标签的active状态
                    tabs30px.forEach(t => t.classList.remove('active'));
                    // 添加当前标签的active状态
                    this.classList.add('active');
                });
            });
            
            // 为40px标签添加点击切换效果
            const tabs40px = document.querySelectorAll('.tab-40px');
            tabs40px.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除其他标签的active状态
                    tabs40px.forEach(t => t.classList.remove('active'));
                    // 添加当前标签的active状态
                    this.classList.add('active');
                });
            });
            
            console.log('📏 标签页高度测试页面已加载');
            console.log('🎯 点击标签页查看交互效果');
        });
    </script>
</body>
</html>
