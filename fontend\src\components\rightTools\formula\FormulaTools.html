<div class="formula-tools" editor-component="formula-tools">
   <!-- 颜色选择区域 -->
  <div class="formula-color-header">
    <i class="formula-color-icon"></i>
    <span>颜色选择</span>
    <span class="formula-hint">(先选择颜色)</span>
  </div>


    <div class="formula-color-container">
      <div class="formula-color-item black active" data-color="#000000"></div>
      <div class="formula-color-item blue" data-color="#006eff"></div>
      <div class="formula-color-item orange" data-color="#ff6a00"></div>
      <div class="formula-color-item green" data-color="#00ff6e"></div>
      <div class="formula-color-item purple" data-color="#ae00ff"></div>
      <div class="formula-color-item red" data-color="#ff0019"></div>
    </div>


    <!-- 公式输入区域 -->
  <div class="formula-input-header">
    <i class="formula-input-icon"></i>
    <span>LaTeX公式</span>
    <span class="formula-hint">(再修改公式)</span>
  </div>
  <div class="formula-input-section">

    <div class="formula-input-container">
      <textarea class="formula-input" placeholder="\int_{0}^{\infty} e^{-x^2} dx = \frac{\sqrt{\pi}}{2}"></textarea>
    </div>
  </div> <div class="formula-input-footer">
    <span class="formula-input-tips">　　* 支持标准LaTeX语法</span>
  </div>



  <!-- 公式预览区域 -->
  <div class="formula-preview-header">
    <i class="formula-preview-icon"></i>
    <span>公式预览</span>
  </div>
  <div class="formula-preview-section">

    <div class="formula-preview-container">
      <div class="formula-preview-content"></div>
    </div>
  </div>

  <!-- 底部按钮区域 -->
  <div class="formula-actions-section">
    <button class="formula-action-btn add-formula">
      <i class="formula-add-icon"></i>
      <span>添加公式</span>
      <span class="formula-hint-insert" >(最后插入)</span>
    </button>
  </div>
</div>