<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片环绕功能修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f8ff;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #2980b9;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .test-title::before {
            content: "✓";
            background: #27ae60;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 14px;
        }
        .test-steps {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
        .fix-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
        }
        .fix-summary h3 {
            margin-top: 0;
            color: white;
        }
        .fix-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .fix-item::before {
            content: "🔧";
            margin-right: 10px;
            font-size: 16px;
        }
        .expected-result {
            background: #d5f4e6;
            border: 2px solid #27ae60;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .expected-result h4 {
            color: #27ae60;
            margin-top: 0;
        }
        .button-demo {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        .demo-button {
            padding: 8px 16px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .demo-button:hover {
            background: #3498db;
            color: white;
        }
        .demo-button.active {
            background: #2980b9;
            color: white;
            border-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-header">🎯 图片环绕功能修复测试</h1>
        
        <div class="fix-summary">
            <h3>🔧 修复内容总结</h3>
            <div class="fix-item">统一使用 getRangeContext() 方法获取选中元素</div>
            <div class="fix-item">修复元素获取方式不一致的问题</div>
            <div class="fix-item">改进错误处理和用户提示</div>
            <div class="fix-item">与右键菜单保持一致的实现逻辑</div>
        </div>

        <div class="test-section">
            <div class="test-title">测试准备</div>
            <div class="test-steps">
                <ol>
                    <li>确保Canvas Editor已正确加载</li>
                    <li>确保右侧工具栏的图书编排tab可见</li>
                    <li>准备一张测试图片（可以是任意格式的图片）</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">功能测试步骤</div>
            <div class="test-steps">
                <ol>
                    <li><strong>插入图片：</strong>在编辑器中插入一张图片</li>
                    <li><strong>选中图片：</strong>点击图片确保其被选中（应该看到选择框）</li>
                    <li><strong>打开工具栏：</strong>打开右侧工具栏的"图书编排"选项卡</li>
                    <li><strong>测试环绕模式：</strong>依次点击以下按钮测试：</li>
                </ol>
                <div class="button-demo">
                    <button class="demo-button">嵌入型</button>
                    <button class="demo-button">上下型环绕</button>
                    <button class="demo-button">四周型环绕</button>
                    <button class="demo-button">置文字上方</button>
                    <button class="demo-button">置文字下方</button>
                </div>
            </div>
            
            <div class="expected-result">
                <h4>✅ 预期结果</h4>
                <ul>
                    <li><strong>嵌入型：</strong>图片独占一行，文字在图片上下方</li>
                    <li><strong>上下型环绕：</strong>图片与文字在同一行，但左右无文字环绕</li>
                    <li><strong>四周型环绕：</strong>文字围绕图片四周流动显示</li>
                    <li><strong>置文字上方：</strong>图片浮动在文字上方，可自由拖动</li>
                    <li><strong>置文字下方：</strong>图片位于文字下方作为背景</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">验证要点</div>
            <div class="test-steps">
                <ol>
                    <li><strong>按钮状态：</strong>选中图片后，环绕按钮应该变为可用状态</li>
                    <li><strong>即时反馈：</strong>点击按钮后图片布局应立即发生变化</li>
                    <li><strong>状态指示：</strong>当前激活的环绕模式按钮应显示为选中状态</li>
                    <li><strong>错误处理：</strong>未选中图片时点击按钮应显示提示信息</li>
                    <li><strong>一致性：</strong>右侧工具栏的效果应与右键菜单相同</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">故障排除</div>
            <div class="test-steps">
                <p><strong>如果功能仍不正常，请检查：</strong></p>
                <ol>
                    <li>浏览器控制台是否有JavaScript错误</li>
                    <li>图片是否正确选中（有选择框显示）</li>
                    <li>Canvas Editor版本是否支持getRangeContext方法</li>
                    <li>右侧工具栏组件是否正确初始化</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        console.log('🎯 图片环绕功能修复测试页面已加载');
        console.log('修复要点：');
        console.log('1. 使用 getRangeContext() 替代索引方式获取元素');
        console.log('2. 统一右侧工具栏与右键菜单的实现方式');
        console.log('3. 改进错误处理和用户体验');
        
        // 模拟按钮点击效果
        document.querySelectorAll('.demo-button').forEach(button => {
            button.addEventListener('click', function() {
                // 清除其他按钮的激活状态
                document.querySelectorAll('.demo-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                // 激活当前按钮
                this.classList.add('active');
                console.log(`模拟点击: ${this.textContent}`);
            });
        });
    </script>
</body>
</html>
