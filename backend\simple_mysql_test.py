#!/usr/bin/env python
"""
简化的 MySQL 连接测试
"""

import pymysql
import os
import sys

def test_mysql_connection():
    """测试 MySQL 连接"""
    print("🔍 测试 MySQL 远程数据库连接...")
    
    try:
        # 连接参数
        config = {
            'host': '***********',
            'port': 3306,
            'user': 'book_editor',
            'password': 'eN2eB5mFKpA2PDmB',
            'database': 'book_editor',
            'charset': 'utf8mb4'
        }
        
        print(f"📡 连接到: {config['host']}:{config['port']}")
        print(f"🗄️ 数据库: {config['database']}")
        print(f"👤 用户: {config['user']}")
        
        # 建立连接
        connection = pymysql.connect(**config)
        print("✅ MySQL 连接成功！")
        
        # 测试查询
        with connection.cursor() as cursor:
            # 获取 MySQL 版本
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"🔧 MySQL 版本: {version}")
            
            # 获取当前数据库
            cursor.execute("SELECT DATABASE()")
            database = cursor.fetchone()[0]
            print(f"📊 当前数据库: {database}")
            
            # 查看表列表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📋 数据库表数量: {len(tables)}")
            
            if tables:
                print("📝 数据库表列表:")
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                    count = cursor.fetchone()[0]
                    print(f"   - {table[0]}: {count} 条记录")
        
        connection.close()
        print("✅ MySQL 测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ MySQL 连接失败: {e}")
        return False

def test_django_mysql():
    """测试 Django MySQL 配置"""
    print("\n🔍 测试 Django MySQL 配置...")
    
    try:
        # 设置 Django 环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'book_editor_backend.settings')
        
        import django
        django.setup()
        
        from django.db import connection
        from django.conf import settings
        
        # 检查配置
        db_config = settings.DATABASES['default']
        print(f"🔧 Django 数据库引擎: {db_config['ENGINE']}")
        print(f"🌐 Django 数据库主机: {db_config['HOST']}:{db_config['PORT']}")
        print(f"📊 Django 数据库名称: {db_config['NAME']}")
        
        # 测试连接
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print("✅ Django MySQL 连接成功！")
        
        return True
        
    except Exception as e:
        print(f"❌ Django MySQL 连接失败: {e}")
        return False

if __name__ == "__main__":
    print("🗄️ MySQL 远程数据库连接测试")
    print("=" * 40)
    
    # 测试直接连接
    mysql_ok = test_mysql_connection()
    
    # 测试 Django 连接
    django_ok = test_django_mysql()
    
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    print(f"   MySQL 直接连接: {'✅ 成功' if mysql_ok else '❌ 失败'}")
    print(f"   Django MySQL 连接: {'✅ 成功' if django_ok else '❌ 失败'}")
    
    if mysql_ok and django_ok:
        print("\n🎉 恭喜！MySQL 远程数据库配置成功！")
        print("✨ 现在可以使用远程 MySQL 数据库了！")
    else:
        print("\n⚠️ 需要检查 MySQL 配置")
