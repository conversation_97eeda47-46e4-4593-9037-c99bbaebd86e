import { Editor } from '../../editor'
import html from './FirstLineIndentButton.html'
import './FirstLineIndentButton.css'

/**
 * 首行缩进按钮组件
 * 用于将光标所在段落的首行添加2个字符的空格
 */
export class FirstLineIndentButton {
  private dom: HTMLDivElement
  private instance: Editor

  constructor(instance: Editor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    this.bindEvents()
  }

  /**
   * 绑定事件处理器
   */
  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 执行首行缩进命令
      this.executeFirstLineIndent()
    }
  }

  /**
   * 执行首行缩进命令
   */
  private executeFirstLineIndent(): void {
    try {
      // 获取编辑器实例
      const instance = this.instance

      // 获取当前选区
      const range = instance.command.getRange()
      if (!range) return

      // 获取当前光标位置
      const { startIndex } = range
      if (startIndex < 0) return

      // 获取编辑器数据
      const editorData = instance.command.getValue()
      if (!editorData || !editorData.data || !editorData.data.main) return

      // 获取元素列表
      const elementList = editorData.data.main
      if (!elementList.length) return

      // 查找段落起始位置
      let paragraphStartIndex = startIndex
      while (paragraphStartIndex > 0) {
        const element = elementList[paragraphStartIndex - 1]
        // 如果前一个元素是换行符或者是段落开始，则停止
        if (element && (element.value === '\n' || !element.value)) {
          break
        }
        paragraphStartIndex--
      }

      // 在段落起始位置插入两个全角空格（首行缩进）
      instance.command.executeInsertElementList([
        {
          value: '　　' // 两个全角空格
        }
      ])
    } catch (error) {
      console.error('首行缩进执行失败:', error)
    }
  }

  /**
   * 获取DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
}