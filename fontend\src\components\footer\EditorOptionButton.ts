import { CanvasEditor } from '../../editor'
import { Dialog } from '../dialog/Dialog'
import html from './EditorOptionButton.html'
import './EditorOptionButton.css'

export class EditorOptionButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.openEditorOptionDialog()
    }
  }
  
  private openEditorOptionDialog(): void {
    const options = this.instance.command.getOptions()
    
    new Dialog({
      title: '编辑器配置',
      data: [
        {
          type: 'textarea',
          name: 'option',
          width: 350,
          height: 300,
          required: true,
          value: JSON.stringify(options, null, 2),
          placeholder: '请输入编辑器配置'
        }
      ],
      onConfirm: payload => {
        const newOptionValue = payload.find(p => p.name === 'option')?.value
        if (!newOptionValue) return
        
        try {
          const newOption = JSON.parse(newOptionValue)
          this.instance.command.executeUpdateOptions(newOption)
        } catch (error) {
          console.error('解析配置失败:', error)
        }
      }
    })
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 