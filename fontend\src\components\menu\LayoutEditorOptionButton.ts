import Editor from '../../editor'
import { Dialog } from '../dialog/Dialog'
import html from './LayoutEditorOptionButton.html'

/**
 * 布局菜单编辑器设置按钮组件
 * 用于在布局菜单中打开编辑器配置对话框
 */
export class LayoutEditorOptionButton {
  private dom: HTMLDivElement
  private instance: Editor

  constructor(instance: Editor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    this.bindEvents()
  }

  /**
   * 绑定事件处理器
   */
  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation()
      this.openEditorOptionDialog()
    }
  }

  /**
   * 打开编辑器配置对话框
   */
  private openEditorOptionDialog(): void {
    try {
      // 获取当前编辑器配置
      const options = this.instance.command.getOptions()

      // 创建编辑器配置对话框
      new Dialog({
        title: '编辑器配置',
        data: [
          {
            type: 'textarea',
            name: 'option',
            width: 350,
            height: 300,
            required: true,
            value: JSON.stringify(options, null, 2),
            placeholder: '请输入编辑器配置'
          }
        ],
        onConfirm: (payload) => {
          this.updateEditorOptions(payload)
        }
      })
    } catch (error) {
      console.error('打开编辑器配置对话框失败:', error)
    }
  }

  /**
   * 更新编辑器配置
   */
  private updateEditorOptions(payload: Array<{ name: string; value: string }>): void {
    try {
      const newOptionValue = payload.find(p => p.name === 'option')?.value
      if (!newOptionValue) {
        console.warn('未找到配置数据')
        return
      }

      console.log('更新编辑器配置:', newOptionValue)

      // 解析JSON配置
      const newOption = JSON.parse(newOptionValue)
      console.log('解析后的配置:', newOption)

      // 执行更新配置命令
      this.instance.command.executeUpdateOptions(newOption)

      console.log('编辑器配置更新完成')
    } catch (error) {
      console.error('更新编辑器配置失败:', error)

      // 可以在这里添加用户友好的错误提示
      if (error instanceof SyntaxError) {
        console.error('JSON格式错误，请检查配置格式')
      }
    }
  }

  /**
   * 获取DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
}
