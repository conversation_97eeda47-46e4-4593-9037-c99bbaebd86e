import { IRegisterContextMenu } from '../../interface/contextmenu/ContextMenu'
import { IRegisterShortcut } from '../../interface/shortcut/Shortcut'
import { ContextMenu } from '../contextmenu/ContextMenu'
import { Shortcut } from '../shortcut/Shortcut'
import { I18n } from '../i18n/I18n'
import { ILang } from '../../interface/i18n/I18n'
import { DeepPartial } from '../../interface/Common'

interface IRegisterPayload {
  contextMenu: ContextMenu
  shortcut: Shortcut
  i18n: I18n
}

export class Register {
  public contextMenuList: (payload: IRegisterContextMenu[]) => void
  public getContextMenuList: () => IRegisterContextMenu[]
  public shortcutList: (payload: IRegisterShortcut[]) => void
  public langMap: (locale: string, lang: DeepPartial<ILang>) => void

  // 添加缺失的方法
  public renderEngine: (callback: (element: any, previewElement: HTMLElement) => boolean) => void
  public controlChange: (callback: (payload: any, control: any) => void) => void
  public controlComponentRender: (callback: (...args: any[]) => boolean) => void
  public editorModeChange: (callback: (mode: any) => void) => void
  public editorCallback: (callbacks: { afterSetValue?: () => void; afterSetHTML?: () => void }) => void
  public codeTransform: (callback: (prismCode: string, language: string) => any) => void
  public contextMenuProvider: (callback: (point: any, element: any, elementList: any[], event: any) => any[]) => void
  public wordTool: (callback: (editor: any) => any) => void

  constructor(payload: IRegisterPayload) {
    const { contextMenu, shortcut, i18n } = payload
    this.contextMenuList = contextMenu.registerContextMenuList.bind(contextMenu)
    this.getContextMenuList = contextMenu.getContextMenuList.bind(contextMenu)
    this.shortcutList = shortcut.registerShortcutList.bind(shortcut)
    this.langMap = i18n.registerLangMap.bind(i18n)

    // 初始化缺失的方法为空函数，避免运行时错误
    this.renderEngine = () => { /* TODO: 实现渲染引擎注册 */ }
    this.controlChange = () => { /* TODO: 实现控件变化回调注册 */ }
    this.controlComponentRender = () => { /* TODO: 实现控件组件渲染器注册 */ }
    this.editorModeChange = () => { /* TODO: 实现编辑模式变化回调注册 */ }
    this.editorCallback = () => { /* TODO: 实现编辑器回调注册 */ }
    this.codeTransform = () => { /* TODO: 实现代码转换器注册 */ }
    this.contextMenuProvider = () => { /* TODO: 实现右键菜单提供者注册 */ }
    this.wordTool = () => { /* TODO: 实现词汇工具注册 */ }
  }
}
