/* 开始菜单字体按钮容器 */
.menu-item .menu-item__home-font {
  width: 100px;
  position: relative;
}

/* 开始菜单字体选择显示区域 */
.menu-item__home-font .select {
  width: calc(100% - 10px);
  height: 100%;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  
  white-space: nowrap;
  padding-top: 10px;
  padding-left: 10px;
}

/* 开始菜单字体按钮图标 */
.menu-item__home-font i {
  transform: translateX(-5px);
  background-image: url('../../assets/images/font.svg');
}

/* 开始菜单字体选择下拉框 */
.menu-item__home-font .options {
  width: 150px;
  max-height: 300px;
  overflow-y: auto;
  position: fixed !important; /* 使用fixed定位确保浮于最上层 */
  z-index: 999999 !important; /* 确保最高层级 */
  background: #fff;
  border: 1px solid #e2e6ed;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  pointer-events: none; /* 隐藏时不响应鼠标事件 */
}

/* 下拉框显示状态 */
.menu-item__home-font .options.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto; /* 显示时恢复鼠标事件 */
}

/* 开始菜单字体选项样式 */
.menu-item__home-font .options li {
  padding: 8px 12px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 开始菜单字体选项悬停效果 */
.menu-item__home-font .options li:hover {
  background: #f5f7fa;
  color: #409eff;
}

/* 开始菜单字体选项激活状态 */
.menu-item__home-font .options li.active {
  background: #ecf5ff;
  color: #409eff;
  font-weight: 600;
}

/* 滚动条样式 */
.menu-item__home-font .options::-webkit-scrollbar {
  width: 6px;
}

.menu-item__home-font .options::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.menu-item__home-font .options::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.menu-item__home-font .options::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
