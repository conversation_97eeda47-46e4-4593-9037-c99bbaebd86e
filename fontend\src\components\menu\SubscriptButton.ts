import './SubscriptButton.css'

export class SubscriptButton {
  private element: HTMLDivElement;
  private command: any;
  private isApple: boolean;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    this.isApple = typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent)
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.subscript-button') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="subscript-button" title="下标(${this.isApple ? '⌘' : 'Ctrl'}+Shift+.)">
      <i></i>
    </div>`
  }

  private bindEvents(): void {
    this.element.onclick = () => {
      console.log('subscript')
      this.command.executeSubscript()
    }
  }

  // 更新按钮状态
  public updateState(isSubscript: boolean): void {
    if (isSubscript) {
      this.element.classList.add('active')
    } else {
      this.element.classList.remove('active')
    }
  }
} 