<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片环绕功能调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .debug-title {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .test-title {
            font-weight: bold;
            color: #007acc;
            margin-bottom: 10px;
        }

        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }

        .issue-highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .solution-highlight {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <div class="debug-container">
        <h1 class="debug-title">🔍 图片环绕功能问题分析报告</h1>

        <div class="test-section">
            <div class="test-title">📋 问题描述</div>
            <p>右侧工具栏的图片环绕按钮（上下型环绕、四周型环绕、置文字上方、置文字下方）点击后没有起作用。</p>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 代码分析对比</div>

            <h4>右键菜单实现（正常工作）：</h4>
            <div class="code-block">
                // imageMenus.ts - 右键菜单实现
                callback: (command: Command, context: IContextMenuContext) => {
                command.executeChangeImageDisplay(
                context.startElement!, // 直接使用上下文中的元素
                ImageDisplay.INLINE
                )
                }
            </div>

            <h4>右侧工具栏实现（存在问题）：</h4>
            <div class="code-block">
                // typeset.ts - 右侧工具栏实现
                private changeImageDisplayMode(display: ImageDisplay): void {
                const command = this.instance.command
                const range = command.getRange()
                const { startIndex } = range

                const draw = (this.instance as any).draw
                const elementList = draw.getElementList()

                const element = elementList[startIndex] // 通过索引获取元素
                command.executeChangeImageDisplay(element, display)
                }
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">⚠️ 发现的问题</div>
            <div class="issue-highlight">
                <strong>问题1：元素获取方式不一致</strong><br>
                - 右键菜单：直接使用 context.startElement（准确）<br>
                - 右侧工具栏：通过 startIndex 从 elementList 获取（可能不准确）
            </div>

            <div class="issue-highlight">
                <strong>问题2：可能的索引偏移</strong><br>
                - getElementList() 可能返回的是当前上下文的元素列表<br>
                - startIndex 可能是全局索引，与当前上下文列表不匹配
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">✅ 解决方案</div>
            <div class="solution-highlight">
                <strong>方案：统一使用 getRangeContext() 获取元素</strong><br>
                修改右侧工具栏的实现，使用与右键菜单相同的元素获取方式
            </div>

            <h4>修复后的代码：</h4>
            <div class="code-block">
                private changeImageDisplayMode(display: ImageDisplay): void {
                try {
                const command = this.instance.command

                // 使用与右键菜单相同的方式获取元素
                const rangeContext = command.getRangeContext()
                if (!rangeContext || !rangeContext.startElement) {
                console.error('无法获取选中的元素')
                return
                }

                const element = rangeContext.startElement
                if (element.type !== ElementType.IMAGE) {
                console.error('选中的元素不是图片:', element)
                return
                }

                console.log('修改图片布局:', {
                element,
                currentDisplay: element.imgDisplay,
                newDisplay: display
                })

                // 调用Canvas Editor的方法
                command.executeChangeImageDisplay(element, display)

                } catch (error) {
                console.error('改变图片显示模式失败:', error)
                }
                }
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 测试步骤</div>
            <ol>
                <li>在编辑器中插入一张图片</li>
                <li>点击选中图片（确保图片被选中）</li>
                <li>打开右侧工具栏的图书编排tab</li>
                <li>点击不同的环绕模式按钮</li>
                <li>观察图片布局是否发生变化</li>
            </ol>
        </div>

        <div class="test-section">
            <div class="test-title">✅ 修复状态</div>
            <div class="solution-highlight">
                <strong>已完成修复：</strong><br>
                - ✅ 修改了 changeImageDisplayMode 方法<br>
                - ✅ 使用 getRangeContext() 获取选中元素<br>
                - ✅ 统一了与右键菜单相同的实现方式<br>
                - ✅ 增加了更好的错误处理和用户提示
            </div>

            <h4>修复的关键变更：</h4>
            <div class="code-block">
                // 修复前：通过索引获取元素（不可靠）
                const element = elementList[startIndex]

                // 修复后：使用上下文获取元素（可靠）
                const rangeContext = command.getRangeContext()
                const element = rangeContext.startElement
            </div>
        </div>
    </div>

    <script>
        console.log('✅ 图片环绕功能已修复');
        console.log('修复内容：');
        console.log('1. 统一使用 getRangeContext() 获取选中元素');
        console.log('2. 改进错误处理和用户提示');
        console.log('3. 与右键菜单保持一致的实现方式');
        console.log('请按照测试步骤进行验证');
    </script>
</body>

</html>