/**
 * HTTP客户端
 * 封装fetch请求，提供统一的HTTP请求接口
 */

import { 
  HTTP_CONFIG, 
  ERROR_CODES, 
  HTTP_STATUS_CODES, 
  LOG_CONFIG,
  getApiUrl 
} from './config'

// 请求方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

// 请求配置接口
export interface RequestConfig {
  method?: HttpMethod
  headers?: Record<string, string>
  body?: any
  timeout?: number
  credentials?: RequestCredentials
}

// 响应接口
export interface ApiResponse<T = any> {
  data: T
  status: number
  statusText: string
  headers: Headers
}

// 错误接口
export interface ApiError {
  code: string
  message: string
  status?: number
  details?: any
}

/**
 * HTTP客户端类
 */
export class HttpClient {
  private baseHeaders: Record<string, string>
  private timeout: number

  constructor() {
    this.baseHeaders = { ...HTTP_CONFIG.DEFAULT_HEADERS }
    this.timeout = HTTP_CONFIG.TIMEOUT
  }

  /**
   * 设置默认请求头
   */
  setDefaultHeaders(headers: Record<string, string>): void {
    this.baseHeaders = { ...this.baseHeaders, ...headers }
  }

  /**
   * 设置认证令牌
   */
  setAuthToken(token: string): void {
    this.baseHeaders['Authorization'] = `Bearer ${token}`
  }

  /**
   * 移除认证令牌
   */
  removeAuthToken(): void {
    delete this.baseHeaders['Authorization']
  }

  /**
   * 记录请求日志
   */
  private logRequest(url: string, config: RequestConfig): void {
    if (LOG_CONFIG.ENABLE_REQUEST_LOG) {
      console.log(`[API Request] ${config.method || 'GET'} ${url}`, {
        headers: config.headers,
        body: config.body
      })
    }
  }

  /**
   * 记录响应日志
   */
  private logResponse<T>(url: string, response: ApiResponse<T>): void {
    if (LOG_CONFIG.ENABLE_RESPONSE_LOG) {
      console.log(`[API Response] ${response.status} ${url}`, response.data)
    }
  }

  /**
   * 记录错误日志
   */
  private logError(url: string, error: ApiError): void {
    if (LOG_CONFIG.ENABLE_ERROR_LOG) {
      console.error(`[API Error] ${url}`, error)
    }
  }

  /**
   * 处理HTTP错误
   */
  private handleHttpError(response: Response): ApiError {
    let code: string
    let message: string

    switch (response.status) {
      case HTTP_STATUS_CODES.BAD_REQUEST:
        code = ERROR_CODES.BAD_REQUEST
        message = '请求参数错误'
        break
      case HTTP_STATUS_CODES.UNAUTHORIZED:
        code = ERROR_CODES.UNAUTHORIZED
        message = '认证失败，请重新登录'
        break
      case HTTP_STATUS_CODES.FORBIDDEN:
        code = ERROR_CODES.FORBIDDEN
        message = '权限不足'
        break
      case HTTP_STATUS_CODES.NOT_FOUND:
        code = ERROR_CODES.NOT_FOUND
        message = '请求的资源不存在'
        break
      case HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR:
        code = ERROR_CODES.SERVER_ERROR
        message = '服务器内部错误'
        break
      case HTTP_STATUS_CODES.BAD_GATEWAY:
      case HTTP_STATUS_CODES.SERVICE_UNAVAILABLE:
        code = ERROR_CODES.SERVER_ERROR
        message = '服务暂时不可用'
        break
      default:
        code = ERROR_CODES.UNKNOWN_ERROR
        message = `HTTP错误: ${response.status} ${response.statusText}`
    }

    return {
      code,
      message,
      status: response.status
    }
  }

  /**
   * 创建请求超时控制器
   */
  private createTimeoutController(timeout: number): AbortController {
    const controller = new AbortController()
    setTimeout(() => controller.abort(), timeout)
    return controller
  }

  /**
   * 发送HTTP请求
   */
  async request<T = any>(endpoint: string, config: RequestConfig = {}): Promise<ApiResponse<T>> {
    const url = getApiUrl(endpoint)
    const method = config.method || 'GET'
    const timeout = config.timeout || this.timeout

    // 合并请求头
    const headers = {
      ...this.baseHeaders,
      ...config.headers
    }

    // 处理请求体
    let body: string | FormData | undefined
    if (config.body) {
      if (config.body instanceof FormData) {
        body = config.body
        // FormData会自动设置Content-Type，需要删除手动设置的
        delete headers['Content-Type']
      } else {
        body = JSON.stringify(config.body)
      }
    }

    // 创建超时控制器
    const timeoutController = this.createTimeoutController(timeout)

    // 构建fetch配置
    const fetchConfig: RequestInit = {
      method,
      headers,
      body,
      credentials: config.credentials || 'include',
      signal: timeoutController.signal
    }

    // 记录请求日志
    this.logRequest(url, { ...config, headers })

    try {
      const response = await fetch(url, fetchConfig)

      // 检查响应状态
      if (!response.ok) {
        const error = this.handleHttpError(response)
        this.logError(url, error)
        throw error
      }

      // 解析响应数据
      let data: T
      const contentType = response.headers.get('content-type')
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json()
      } else {
        data = await response.text() as any
      }

      const apiResponse: ApiResponse<T> = {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers
      }

      // 记录响应日志
      this.logResponse(url, apiResponse)

      return apiResponse

    } catch (error: any) {
      // 处理网络错误和超时
      let apiError: ApiError

      if (error.name === 'AbortError') {
        apiError = {
          code: ERROR_CODES.TIMEOUT,
          message: '请求超时'
        }
      } else if (error.code) {
        // 已经是ApiError
        apiError = error
      } else {
        apiError = {
          code: ERROR_CODES.NETWORK_ERROR,
          message: '网络连接失败'
        }
      }

      this.logError(url, apiError)
      throw apiError
    }
  }

  /**
   * GET请求
   */
  async get<T = any>(endpoint: string, config: Omit<RequestConfig, 'method' | 'body'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'GET' })
  }

  /**
   * POST请求
   */
  async post<T = any>(endpoint: string, data?: any, config: Omit<RequestConfig, 'method'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'POST', body: data })
  }

  /**
   * PUT请求
   */
  async put<T = any>(endpoint: string, data?: any, config: Omit<RequestConfig, 'method'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PUT', body: data })
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(endpoint: string, data?: any, config: Omit<RequestConfig, 'method'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PATCH', body: data })
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(endpoint: string, config: Omit<RequestConfig, 'method' | 'body'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' })
  }

  /**
   * 上传文件
   */
  async upload<T = any>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    // 添加额外数据
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers: HTTP_CONFIG.UPLOAD_HEADERS
    })
  }
}

// 创建默认的HTTP客户端实例
export const httpClient = new HttpClient()
