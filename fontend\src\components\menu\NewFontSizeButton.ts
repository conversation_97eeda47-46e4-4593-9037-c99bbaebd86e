import './NewFontSizeButton.css'

export class NewFontSizeButton {
  private element: HTMLDivElement;
  private selectElement: HTMLSpanElement;
  private optionsElement: HTMLDivElement;
  private command: any;
  private documentClickHandler: (e: MouseEvent) => void = () => {
    // 处理文档点击事件
  };

  constructor(container: HTMLElement, command: any) {
    this.command = command
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.new-font-size-button') as HTMLDivElement
    this.selectElement = this.element.querySelector('.select') as HTMLSpanElement
    this.optionsElement = this.element.querySelector('.options') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="new-font-size-button">
      <span class="select" title="字号">14</span>
      <div class="options">
        <ul>
          <li data-size="8">8</li>
          <li data-size="9">9</li>
          <li data-size="10">10</li>
          <li data-size="11">11</li>
          <li data-size="12">12</li>
          <li data-size="14" class="active">14</li>
          <li data-size="16">16</li>
          <li data-size="18">18</li>
          <li data-size="20">20</li>
          <li data-size="22">22</li>
          <li data-size="24">24</li>
          <li data-size="26">26</li>
          <li data-size="28">28</li>
          <li data-size="30">30</li>
          <li data-size="32">32</li>
          <li data-size="36">36</li>
          <li data-size="40">40</li>
          <li data-size="44">44</li>
          <li data-size="48">48</li>
          <li data-size="54">54</li>
          <li data-size="60">60</li>
          <li data-size="66">66</li>
          <li data-size="72">72</li>

        </ul>
      </div>
    </div>`
  }

  private bindEvents(): void {
    // 点击按钮切换下拉框显示状态
    this.element.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      console.log('new-font-size')
      
      // 切换显示状态
      const isVisible = this.optionsElement.classList.contains('visible')
      
      // 先隐藏所有其他的下拉框
      this.hideAllDropdowns()
      
      if (!isVisible) {
        // 显示当前下拉框
        this.showDropdown()
      }
    }

    // 点击下拉框选项
    this.optionsElement.onclick = (evt) => {
      evt.stopPropagation() // 阻止事件冒泡
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        const size = li.dataset.size
        if (size) {
          this.command.executeSize(Number(size))
          this.updateSelectedSize(Number(size))
          this.hideDropdown()
        }
      }
    }

    // 阻止下拉框内的点击事件冒泡到document
    this.optionsElement.addEventListener('click', (e) => {
      e.stopPropagation()
    })

    // 点击外部关闭下拉框
    this.documentClickHandler = (e) => {
      const target = e.target as Node
      if (!this.element.contains(target) && !this.optionsElement.contains(target)) {
        this.hideDropdown()
      }
    }
    document.addEventListener('click', this.documentClickHandler)
  }

  // 显示下拉框并定位到最上层
  private showDropdown(): void {
    // 先设置基本样式
    this.optionsElement.style.position = 'fixed'
    this.optionsElement.style.zIndex = '999999'
    
    // 添加visible类
    this.optionsElement.classList.add('visible')
    
    // 等待一帧后计算位置，确保元素已渲染
    requestAnimationFrame(() => {
      this.positionDropdown()
    })
  }

  // 精确定位下拉框
  private positionDropdown(): void {
    const rect = this.element.getBoundingClientRect()
    const optionsRect = this.optionsElement.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    // 默认位置：按钮下方
    let left = rect.left
    let top = rect.bottom + 4
    
    // 水平边界检查
    if (left + 120 > viewportWidth) {
      left = viewportWidth - 120 - 10
    }
    if (left < 10) {
      left = 10
    }
    
    // 垂直边界检查
    if (top + 300 > viewportHeight) {
      top = rect.top - 300 - 4
    }
    if (top < 10) {
      top = 10
    }
    
    // 应用位置
    this.optionsElement.style.left = left + 'px'
    this.optionsElement.style.top = top + 'px'
  }

  // 隐藏下拉框
  private hideDropdown(): void {
    this.optionsElement.classList.remove('visible')
  }

  // 隐藏所有下拉框（避免多个下拉框同时显示）
  private hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options.visible')
    allDropdowns.forEach(dropdown => {
      dropdown.classList.remove('visible')
    })
  }

  // 更新选中的字号显示
  private updateSelectedSize(size: number): void {
    this.selectElement.innerText = size.toString()
    
    // 更新选项的活动状态
    const options = this.optionsElement.querySelectorAll('li')
    options.forEach(li => li.classList.remove('active'))
    
    const selectedOption = this.optionsElement.querySelector(`[data-size='${size}']`)
    if (selectedOption) {
      selectedOption.classList.add('active')
    }
  }

  // 销毁组件时移除全局事件监听
  public destroy(): void {
    document.removeEventListener('click', this.documentClickHandler)
  }

  // 更新按钮状态（外部调用）
  public updateState(size: number): void {
    this.selectElement.innerText = size.toString()
    
    const options = this.optionsElement.querySelectorAll('li')
    options.forEach(li => li.classList.remove('active'))
    
    const curSizeDom = this.optionsElement.querySelector<HTMLLIElement>(`[data-size='${size}']`)
    if (curSizeDom) {
      curSizeDom.classList.add('active')
    }
  }
}
