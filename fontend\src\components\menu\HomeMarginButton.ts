import { CanvasEditor } from '../../editor'
import { Dialog } from '../dialog/Dialog'
import html from './HomeMarginButton.html'
import './HomeMarginButton.css'

export class HomeMarginButton {
  private dom: HTMLDivElement
  private marginOptionDom: HTMLDivElement
  private instance: CanvasEditor
  private documentClickHandler: (e: MouseEvent) => void

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.marginOptionDom = this.dom.querySelector<HTMLDivElement>('.options')!
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation()
      
      const isVisible = this.marginOptionDom.classList.contains('visible')
      this.hideAllDropdowns()
      
      if (!isVisible) {
        this.showDropdown()
      }
    }

    this.marginOptionDom.onclick = (evt) => {
      evt.stopPropagation()
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        const marginType = li.dataset.marginType!
        this.setMargin(marginType)
        this.hideDropdown()
      }
    }

    this.documentClickHandler = (e) => {
      const target = e.target as Node
      if (!this.dom.contains(target) && !this.marginOptionDom.contains(target)) {
        this.hideDropdown()
      }
    }
    document.addEventListener('click', this.documentClickHandler)
  }

  private setMargin(marginType: string): void {
    let margins: [number, number, number, number] // top, right, bottom, left
    
    switch (marginType) {
      case 'narrow':
        margins = [50, 50, 50, 50] // 窄边距
        break
      case 'normal':
        margins = [96, 96, 96, 96] // 普通边距
        break
      case 'wide':
        margins = [144, 144, 144, 144] // 宽边距
        break
      case 'custom':
        // 打开自定义边距对话框
        this.openCustomMarginDialog()
        return
      default:
        margins = [96, 96, 96, 96]
    }
    
    // 设置页边距
    this.instance.command.executeSetPaperMargin(margins)
    
    // 更新显示的边距类型
    const selectSpan = this.dom.querySelector('.select') as HTMLSpanElement
    const marginNames: { [key: string]: string } = {
      'narrow': '窄',
      'normal': '普通',
      'wide': '宽'
    }
    selectSpan.textContent = marginNames[marginType] || '普通'
  }

  private openCustomMarginDialog(): void {
    // 获取当前页边距设置
    const [topMargin, rightMargin, bottomMargin, leftMargin] = this.instance.command.getPaperMargin()

    // 创建自定义边距对话框
    new Dialog({
      title: '自定义页边距',
      data: [
        {
          type: 'text',
          label: '上边距 (像素)',
          name: 'top',
          required: true,
          value: `${topMargin}`,
          placeholder: '请输入上边距'
        },
        {
          type: 'text',
          label: '下边距 (像素)',
          name: 'bottom',
          required: true,
          value: `${bottomMargin}`,
          placeholder: '请输入下边距'
        },
        {
          type: 'text',
          label: '左边距 (像素)',
          name: 'left',
          required: true,
          value: `${leftMargin}`,
          placeholder: '请输入左边距'
        },
        {
          type: 'text',
          label: '右边距 (像素)',
          name: 'right',
          required: true,
          value: `${rightMargin}`,
          placeholder: '请输入右边距'
        }
      ],
      onConfirm: payload => {
        // 获取用户输入的边距值
        const top = payload.find(p => p.name === 'top')?.value
        if (!top) return
        const bottom = payload.find(p => p.name === 'bottom')?.value
        if (!bottom) return
        const left = payload.find(p => p.name === 'left')?.value
        if (!left) return
        const right = payload.find(p => p.name === 'right')?.value
        if (!right) return

        // 验证输入值是否为有效数字
        const topNum = Number(top)
        const rightNum = Number(right)
        const bottomNum = Number(bottom)
        const leftNum = Number(left)

        if (isNaN(topNum) || isNaN(rightNum) || isNaN(bottomNum) || isNaN(leftNum)) {
          alert('请输入有效的数字')
          return
        }

        if (topNum < 0 || rightNum < 0 || bottomNum < 0 || leftNum < 0) {
          alert('边距值不能为负数')
          return
        }

        // 设置自定义页边距
        this.instance.command.executeSetPaperMargin([
          topNum,
          rightNum,
          bottomNum,
          leftNum
        ])

        // 更新显示文字为"自定义"
        const selectSpan = this.dom.querySelector('.select') as HTMLSpanElement
        selectSpan.textContent = '自定义'
      }
    })
  }

  private showDropdown(): void {
    this.marginOptionDom.style.position = 'fixed'
    this.marginOptionDom.style.zIndex = '999999'
    this.marginOptionDom.classList.add('visible')
    
    requestAnimationFrame(() => {
      this.positionDropdown()
    })
  }

  private positionDropdown(): void {
    const rect = this.dom.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    let left = rect.left
    let top = rect.bottom + 4
    
    if (left + 120 > viewportWidth) {
      left = viewportWidth - 120 - 10
    }
    if (left < 10) {
      left = 10
    }
    
    if (top + 150 > viewportHeight) {
      top = rect.top - 150 - 4
    }
    if (top < 10) {
      top = 10
    }
    
    this.marginOptionDom.style.left = left + 'px'
    this.marginOptionDom.style.top = top + 'px'
  }

  private hideDropdown(): void {
    this.marginOptionDom.classList.remove('visible')
  }

  private hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options.visible')
    allDropdowns.forEach(dropdown => {
      dropdown.classList.remove('visible')
    })
  }

  public destroy(): void {
    document.removeEventListener('click', this.documentClickHandler)
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}
