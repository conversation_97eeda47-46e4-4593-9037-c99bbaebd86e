.highlight-button {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
}

.highlight-button:hover {
  background: rgba(25, 55, 88, .04);
}

.highlight-button.active {
  background: rgba(25, 55, 88, .08);
}

.highlight-button i {
  width: 16px;
  height: 16px;
  display: inline-block;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-image: url('../../assets/images/highlight.svg');
}

.highlight-button span {
  width: 16px;
  height: 3px;
  display: inline-block;
  border: 1px solid #e2e6ed;
  background-color: #ffff00;
}

.highlight-button #highlight {
  width: 1px;
  height: 1px;
  visibility: hidden;
  outline: none;
  appearance: none;
} 