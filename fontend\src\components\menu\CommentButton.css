/* 批注按钮样式 */
.menu-item__comment {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
}

.menu-item__comment:hover {
  background: rgba(25, 55, 88, .04);
}

.menu-item__comment.active {
  background: rgba(25, 55, 88, .08);
}

.menu-item__comment i {
  width: 16px;
  height: 16px;
  display: inline-block;
  /* 使用注释图标 */
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0iIzY2NiI+PHBhdGggZD0iTTE0IDFIMkMxLjQ1IDEgMSAxLjQ1IDEgMnYxMGMwIDAuNTUgMC40NSAxIDEgMWg0bDQgNHYtNGgzYzAuNTUgMCAxLTAuNDUgMS0xVjJjMC0wLjU1LTAuNDUtMS0xLTF6bS0xIDExSDh2MS41TDUuNSAxMkg0di0xaDl2MXptMC0ySDN2LTdoMTB2N3oiLz48L3N2Zz4=');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

/* 评论面板样式 */
.comment {
  position: fixed;
  top: 90px;
  right: 0;
  width: 240px;
  bottom: 30px;
  background: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, .1);
  overflow-y: auto;
  z-index: 2500; /* 提高层级，但低于菜单下拉框 */
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  margin-right: 310px;
}

/* 评论面板头部 */
.comment__header {
  height: 40px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e2e6ed;
  background: #f5f7fa;
}

.comment__header span {
  font-weight: bold;
  font-size: 16px;
}

/* 评论面板关闭按钮 */
.comment__header__close {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comment__header__close i {
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/close.svg');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.comment__header__close:hover {
  background: rgba(25, 55, 88, .08);
  border-radius: 4px;
}

/* 评论内容区域 */
.comment__main {
  padding: 0;
}

/* 评论项样式 */
.comment-item {
  background: #ffffff;
  border: 1px solid #e2e6ed;
  position: relative;
  border-radius: 8px;
  padding: 15px;
  font-size: 14px;
  margin: 15px 15px 15px 15px;
  cursor: pointer;
  transition: all .5s;
}

.comment-item:first-child {
  margin-top: 15px;
}

.comment-item:last-child {
  margin-bottom: 15px;
}

.comment-item:hover {
  border-color: #c0c6cf;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.comment-item.active {
  border-color: #E99D00;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.comment-item__title {
  height: 22px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #c1c6ce;
  margin-bottom: 8px;
}

.comment-item__title span:first-child {
  font-weight: 600;
  color: #3d4757;
  max-width: 45%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.comment-item__title span:nth-child(2) {
  color: #a0a5ad;
  font-size: 12px;
  text-align: right;
  max-width: 55%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.comment-item__title i {
  width: 16px;
  height: 16px;
  cursor: pointer;
  position: relative;
  margin-left: 5px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: url(../../assets/images/close.svg) no-repeat;
  background-size: 100%;
}

.comment-item__title i:hover {
  opacity: 0.6;
}

.comment-item__info {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.comment-item__info>span:first-child {
  font-weight: 600;
}

.comment-item__info>span:last-child {
  color: #c1c6ce;
}

.comment-item__content {
  line-height: 22px;
  word-break: break-word;
  margin-top: 5px;
} 