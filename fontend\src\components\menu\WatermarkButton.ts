import { CanvasEditor } from '../../editor'
import { Dialog } from '../dialog/Dialog'
import html from './WatermarkButton.html'
import './WatermarkButton.css'

export class WatermarkButton {
  private dom: HTMLDivElement
  private watermarkDom: HTMLDivElement
  private watermarkOptionDom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.watermarkDom = this.dom
    this.watermarkOptionDom = this.dom.querySelector<HTMLDivElement>('.options')!
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.watermarkDom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 切换显示状态
      const isVisible = this.watermarkOptionDom.classList.contains('visible')

      // 先隐藏所有其他的下拉框
      this.hideAllDropdowns()

      if (!isVisible) {
        // 显示当前下拉框并定位
        this.showDropdown()
      }
    }

    this.watermarkOptionDom.onmousedown = (evt) => {
      evt.stopPropagation() // 阻止事件冒泡
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        const menu = li.dataset.menu!
        this.hideDropdown()

        if (menu === 'add') {
          this.openAddWatermarkDialog()
        } else {
          this.instance.command.executeDeleteWatermark()
        }
      }
    }

    // 点击外部关闭下拉框
    document.addEventListener('click', (e) => {
      const target = e.target as Node
      if (!this.dom.contains(target) && !this.watermarkOptionDom.contains(target)) {
        this.hideDropdown()
      }
    })
  }

  // 显示下拉框并定位到按钮下方
  private showDropdown(): void {
    // 先设置基本样式
    this.watermarkOptionDom.style.position = 'fixed'
    this.watermarkOptionDom.style.zIndex = '999999'

    // 添加visible类，直接显示不要动画
    this.watermarkOptionDom.classList.add('visible')

    // 立即计算位置
    this.positionDropdown()
  }

  // 精确定位下拉框到按钮下方
  private positionDropdown(): void {
    const rect = this.watermarkDom.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 默认位置：按钮下方
    let left = rect.left
    let top = rect.bottom + 4

    // 水平边界检查（水印下拉框宽度约100px）
    if (left + 100 > viewportWidth) {
      left = viewportWidth - 100 - 10
    }
    if (left < 10) {
      left = 10
    }

    // 垂直边界检查
    if (top + 80 > viewportHeight) {
      top = rect.top - 80 - 4
    }
    if (top < 10) {
      top = 10
    }

    // 应用位置
    this.watermarkOptionDom.style.left = left + 'px'
    this.watermarkOptionDom.style.top = top + 'px'
  }

  // 隐藏下拉框
  private hideDropdown(): void {
    this.watermarkOptionDom.classList.remove('visible')
  }

  // 隐藏所有下拉框（避免多个下拉框同时显示）
  private hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options.visible, .menu-item__table__collapse[style*="block"]')
    allDropdowns.forEach(dropdown => {
      if (dropdown.classList.contains('visible')) {
        dropdown.classList.remove('visible')
      } else {
        (dropdown as HTMLElement).style.display = 'none'
      }
    })
  }

  private openAddWatermarkDialog(): void {
    new Dialog({
      title: '水印',
      data: [
        {
          type: 'text',
          label: '内容',
          name: 'data',
          required: true,
          placeholder: '请输入内容'
        },
        {
          type: 'color',
          label: '颜色',
          name: 'color',
          required: true,
          value: '#AEB5C0'
        },
        {
          type: 'number',
          label: '字体大小',
          name: 'size',
          required: true,
          value: '120'
        },
        {
          type: 'number',
          label: '透明度',
          name: 'opacity',
          required: true,
          value: '0.3'
        },
        {
          type: 'select',
          label: '重复',
          name: 'repeat',
          value: '0',
          required: false,
          options: [
            {
              label: '不重复',
              value: '0'
            },
            {
              label: '重复',
              value: '1'
            }
          ]
        },
        {
          type: 'number',
          label: '水平间隔',
          name: 'horizontalGap',
          required: false,
          value: '10'
        },
        {
          type: 'number',
          label: '垂直间隔',
          name: 'verticalGap',
          required: false,
          value: '10'
        }
      ],
      onConfirm: payload => {
        const nullableIndex = payload.findIndex(p => !p.value)
        if (~nullableIndex) return
        
        const watermark = payload.reduce((pre, cur) => {
          pre[cur.name] = cur.value
          return pre
        }, <any>{})
        
        const repeat = watermark.repeat === '1'
        this.instance.command.executeAddWatermark({
          data: watermark.data,
          color: watermark.color,
          size: Number(watermark.size),
          opacity: Number(watermark.opacity),
          repeat,
          gap:
            repeat && watermark.horizontalGap && watermark.verticalGap
              ? [
                  Number(watermark.horizontalGap),
                  Number(watermark.verticalGap)
                ]
              : undefined
        })
      }
    })
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 