import { CanvasEditor } from '../../editor'
import { ElementType } from '../../editor'
import html from './RadioButton.html'
import './RadioButton.css'

export class RadioButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executeInsertElementList([
        {
          type: ElementType.RADIO,
          checkbox: {
            value: false
          },
          value: ''
        }
      ])
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 