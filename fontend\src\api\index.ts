/**
 * API模块入口文件
 * 统一导出所有API相关的功能
 */

// 配置
export * from './config'

// HTTP客户端
export * from './http-client'

// 服务类
export * from './services'

// 类型定义
export * from './types'

// 工具函数
export * from './utils'

// 默认导出API服务
export { default as ApiService } from './services'

/**
 * API模块初始化函数
 * 在应用启动时调用，进行必要的初始化配置
 */
export async function initApiModule(config?: {
  authToken?: string
  baseHeaders?: Record<string, string>
  enableDebug?: boolean
}): Promise<void> {
  const { ApiService } = await import('./services')
  
  // 初始化API服务
  ApiService.init({
    authToken: config?.authToken,
    baseHeaders: config?.baseHeaders
  })
  
  // 测试API连接
  try {
    const isConnected = await ApiService.testConnection()
    if (isConnected) {
      console.log('✅ API连接测试成功')
    } else {
      console.warn('⚠️ API连接测试失败')
    }
  } catch (error) {
    console.error('❌ API连接测试出错:', error)
  }
}

/**
 * 快速API调用示例
 */
export const apiExamples = {
  /**
   * 健康检查示例
   */
  async healthCheck() {
    const { HealthService } = await import('./services')
    return await HealthService.check()
  },
  
  /**
   * 获取文档列表示例
   */
  async getDocuments() {
    const { DocumentService } = await import('./services')
    return await DocumentService.getDocuments({
      page: 1,
      page_size: 10
    })
  },
  
  /**
   * 保存文档示例
   */
  async saveDocument(title: string, content: any) {
    const { DocumentService } = await import('./services')
    return await DocumentService.saveDocument({
      title,
      content,
      is_public: false
    })
  },
  
  /**
   * 上传图片示例
   */
  async uploadImage(file: File) {
    const { UploadService } = await import('./services')
    return await UploadService.uploadImage(file)
  }
}
