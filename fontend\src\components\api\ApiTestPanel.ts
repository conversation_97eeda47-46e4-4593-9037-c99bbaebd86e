/**
 * API测试面板
 * 用于测试和调试API功能
 */

import { ApiService, HealthService, DocumentService } from '../../api'
import { formatApiError, isApiError } from '../../api/utils'

/**
 * API测试面板类
 */
export class ApiTestPanel {
  private container: HTMLElement
  private isVisible = false

  constructor() {
    this.container = this.createContainer()
    this.bindEvents()
    this.hide()
  }

  /**
   * 创建容器元素
   */
  private createContainer(): HTMLElement {
    const container = document.createElement('div')
    container.className = 'api-test-panel'
    container.innerHTML = `
      <div class="api-test-header">
        <h3>API测试面板</h3>
        <button class="api-test-close">×</button>
      </div>
      <div class="api-test-content">
        <div class="api-test-section">
          <h4>健康检查</h4>
          <button class="api-test-btn" data-action="health">测试健康检查</button>
          <div class="api-test-result" data-result="health"></div>
        </div>

        <div class="api-test-section">
          <h4>文档管理</h4>
          <button class="api-test-btn" data-action="documents">获取文档列表</button>
          <button class="api-test-btn" data-action="create-doc">创建测试文档</button>
          <div class="api-test-result" data-result="documents"></div>
        </div>

        <div class="api-test-section">
          <h4>连接测试</h4>
          <button class="api-test-btn" data-action="connection">测试API连接</button>
          <div class="api-test-result" data-result="connection"></div>
        </div>
      </div>
    `

    // 添加样式
    const style = document.createElement('style')
    style.textContent = `
      .api-test-panel {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 500px;
        max-height: 600px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .api-test-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #eee;
        background: #f8f9fa;
        border-radius: 8px 8px 0 0;
      }

      .api-test-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .api-test-close {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #666;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .api-test-close:hover {
        color: #333;
      }

      .api-test-content {
        padding: 20px;
        max-height: 500px;
        overflow-y: auto;
      }

      .api-test-section {
        margin-bottom: 24px;
      }

      .api-test-section h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .api-test-btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        margin-right: 8px;
        margin-bottom: 8px;
      }

      .api-test-btn:hover {
        background: #0056b3;
      }

      .api-test-btn:disabled {
        background: #6c757d;
        cursor: not-allowed;
      }

      .api-test-result {
        margin-top: 8px;
        padding: 8px;
        border-radius: 4px;
        font-size: 12px;
        font-family: monospace;
        white-space: pre-wrap;
        max-height: 200px;
        overflow-y: auto;
      }

      .api-test-result.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .api-test-result.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .api-test-result.info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
    `
    document.head.appendChild(style)

    document.body.appendChild(container)
    return container
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 关闭按钮
    const closeBtn = this.container.querySelector('.api-test-close')
    closeBtn?.addEventListener('click', () => this.hide())

    // 测试按钮
    const testBtns = this.container.querySelectorAll('.api-test-btn')
    testBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = (e.target as HTMLElement).getAttribute('data-action')
        if (action) {
          this.executeTest(action)
        }
      })
    })

    // ESC键关闭
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.hide()
      }
    })
  }

  /**
   * 执行测试
   */
  private async executeTest(action: string): Promise<void> {
    const btn = this.container.querySelector(`[data-action="${action}"]`) as HTMLButtonElement
    const resultContainer = this.container.querySelector(`[data-result="${action}"]`) as HTMLElement

    if (!btn || !resultContainer) return

    // 禁用按钮
    btn.disabled = true
    btn.textContent = '测试中...'

    // 清空结果
    resultContainer.textContent = ''
    resultContainer.className = 'api-test-result'

    try {
      let result: any

      switch (action) {
        case 'health':
          result = await this.testHealth()
          break
        case 'documents':
          result = await this.testDocuments()
          break
        case 'create-doc':
          result = await this.testCreateDocument()
          break
        case 'connection':
          result = await this.testConnection()
          break
        default:
          throw new Error(`未知的测试操作: ${action}`)
      }

      // 显示成功结果
      resultContainer.textContent = JSON.stringify(result, null, 2)
      resultContainer.classList.add('success')

    } catch (error) {
      // 显示错误结果
      const errorMessage = isApiError(error) ? formatApiError(error) : String(error)
      resultContainer.textContent = `错误: ${errorMessage}\n\n详细信息:\n${JSON.stringify(error, null, 2)}`
      resultContainer.classList.add('error')
    } finally {
      // 恢复按钮
      btn.disabled = false
      btn.textContent = this.getButtonText(action)
    }
  }

  /**
   * 获取按钮文本
   */
  private getButtonText(action: string): string {
    const textMap: Record<string, string> = {
      'health': '测试健康检查',
      'documents': '获取文档列表',
      'create-doc': '创建测试文档',
      'connection': '测试API连接'
    }
    return textMap[action] || '测试'
  }

  /**
   * 测试健康检查
   */
  private async testHealth(): Promise<any> {
    return await HealthService.check()
  }

  /**
   * 测试文档列表
   */
  private async testDocuments(): Promise<any> {
    return await DocumentService.getDocuments({
      page: 1,
      page_size: 5
    })
  }

  /**
   * 测试创建文档
   */
  private async testCreateDocument(): Promise<any> {
    return await DocumentService.createDocument({
      title: `测试文档 ${new Date().toLocaleString()}`,
      content: {
        main: [
          { value: '这是一个测试文档' },
          { value: '创建时间: ' + new Date().toLocaleString() }
        ]
      },
      is_public: false
    })
  }

  /**
   * 测试API连接
   */
  private async testConnection(): Promise<any> {
    const isConnected = await ApiService.testConnection()
    return {
      connected: isConnected,
      timestamp: new Date().toISOString(),
      message: isConnected ? 'API连接正常' : 'API连接失败'
    }
  }

  /**
   * 显示面板
   */
  show(): void {
    this.container.style.display = 'block'
    this.isVisible = true
  }

  /**
   * 隐藏面板
   */
  hide(): void {
    this.container.style.display = 'none'
    this.isVisible = false
  }

  /**
   * 切换显示状态
   */
  toggle(): void {
    if (this.isVisible) {
      this.hide()
    } else {
      this.show()
    }
  }

  /**
   * 销毁面板
   */
  destroy(): void {
    this.container.remove()
  }
}

// 创建全局实例
let apiTestPanelInstance: ApiTestPanel | null = null

/**
 * 获取API测试面板实例
 */
export function getApiTestPanel(): ApiTestPanel {
  if (!apiTestPanelInstance) {
    apiTestPanelInstance = new ApiTestPanel()
  }
  return apiTestPanelInstance
}

/**
 * 显示API测试面板
 */
export function showApiTestPanel(): void {
  getApiTestPanel().show()
}

// 将函数暴露到window对象，方便在控制台调用
if (typeof window !== 'undefined') {
  // 直接定义函数到window对象，避免引用问题
  (window as any).showApiTestPanel = () => {
    getApiTestPanel().show()
  }
  (window as any).getApiTestPanel = () => {
    return getApiTestPanel()
  }
}
