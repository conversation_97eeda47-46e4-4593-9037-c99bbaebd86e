import { CanvasEditor } from '../../editor'
import { ElementType } from '../../editor'
import html from './DateButton.html'
import './DateButton.css'

export class DateButton {
  private dom: HTMLDivElement
  private dateDom: HTMLDivElement
  private dateOptionDom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.dateDom = this.dom
    this.dateOptionDom = this.dom.querySelector<HTMLDivElement>('.options')!
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dateDom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 切换显示状态
      const isVisible = this.dateOptionDom.classList.contains('visible')

      // 先隐藏所有其他的下拉框
      this.hideAllDropdowns()

      if (!isVisible) {
        // 更新当前日期
        this.updateCurrentDate()

        // 显示当前下拉框并定位
        this.showDropdown()
      }
    }

    // 更新当前日期的方法
    this.updateCurrentDate()

    // 绑定事件处理器
    this.bindEventHandlers()
  }

  // 更新当前日期
  private updateCurrentDate(): void {
    const date = new Date()
    const year = date.getFullYear().toString()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    const dateString = `${year}-${month}-${day}`
    const dateTimeString = `${dateString} ${hour}:${minute}:${second}`

    this.dateOptionDom.querySelector<HTMLLIElement>('li:first-child')!.innerText = dateString
    this.dateOptionDom.querySelector<HTMLLIElement>('li:last-child')!.innerText = dateTimeString
  }

  // 绑定事件处理器
  private bindEventHandlers(): void {
    this.dateOptionDom.onmousedown = (evt) => {
      evt.stopPropagation() // 阻止事件冒泡
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        const dateFormat = li.dataset.format!
        this.hideDropdown()

        this.instance.command.executeInsertElementList([
          {
            type: ElementType.DATE,
            value: '',
            dateFormat,
            valueList: [
              {
                value: li.innerText.trim()
              }
            ]
          }
        ])
      }
    }

    // 添加外部点击关闭事件
    document.addEventListener('click', (e) => {
      const target = e.target as Node
      if (!this.dom.contains(target) && !this.dateOptionDom.contains(target)) {
        this.hideDropdown()
      }
    })
  }

  // 显示下拉框并定位到按钮下方
  private showDropdown(): void {
    // 先设置基本样式
    this.dateOptionDom.style.position = 'fixed'
    this.dateOptionDom.style.zIndex = '999999'

    // 添加visible类，直接显示不要动画
    this.dateOptionDom.classList.add('visible')

    // 立即计算位置
    this.positionDropdown()
  }

  // 精确定位下拉框到按钮下方
  private positionDropdown(): void {
    const rect = this.dateDom.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 默认位置：按钮下方
    let left = rect.left
    let top = rect.bottom + 4

    // 水平边界检查（日期下拉框宽度约180px）
    if (left + 180 > viewportWidth) {
      left = viewportWidth - 180 - 10
    }
    if (left < 10) {
      left = 10
    }

    // 垂直边界检查
    if (top + 100 > viewportHeight) {
      top = rect.top - 100 - 4
    }
    if (top < 10) {
      top = 10
    }

    // 应用位置
    this.dateOptionDom.style.left = left + 'px'
    this.dateOptionDom.style.top = top + 'px'
  }

  // 隐藏下拉框
  private hideDropdown(): void {
    this.dateOptionDom.classList.remove('visible')
  }

  // 隐藏所有下拉框（避免多个下拉框同时显示）
  private hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options.visible, .menu-item__table__collapse[style*="block"]')
    allDropdowns.forEach(dropdown => {
      if (dropdown.classList.contains('visible')) {
        dropdown.classList.remove('visible')
      } else {
        (dropdown as HTMLElement).style.display = 'none'
      }
    })
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 