# Selection API 错误修复说明

## 问题描述

在使用canvas-editor时，可能会遇到以下错误：

```
Uncaught DOMException: Failed to execute 'getRangeAt' on 'Selection': 0 is not a valid index.
```

这个错误通常发生在以下情况：

1. **浏览器扩展冲突**：某些浏览器扩展会干扰页面的Selection API
2. **选择状态异常**：当页面上没有任何文本被选中时调用`getRangeAt(0)`
3. **时序问题**：在DOM还未完全加载时调用Selection API
4. **多个编辑器实例**：多个编辑器实例之间的Selection状态冲突

## 解决方案

我们已经实现了一套完整的Selection API错误处理机制：

### 1. 安全的Selection工具函数

```typescript
import { getSafeRange, hasValidSelection, getSafeSelectionText } from './utils/selection'

// 安全地获取选择范围
const range = getSafeRange()
if (range) {
  // 处理选择范围
  console.log('选择范围:', range)
}

// 检查是否有有效选择
if (hasValidSelection()) {
  const text = getSafeSelectionText()
  console.log('选择的文本:', text)
}
```

### 2. 全局错误处理器

错误处理器会自动：
- 捕获Selection API相关错误
- 记录错误日志用于调试
- 尝试自动修复常见问题
- 提供错误统计信息

```typescript
import { SelectionErrorHandler } from './utils/errorHandler'

const errorHandler = SelectionErrorHandler.getInstance()

// 获取错误统计
const stats = errorHandler.getErrorStats()
console.log('错误统计:', stats)

// 获取错误日志
const errorLog = errorHandler.getErrorLog()
console.log('错误日志:', errorLog)
```

### 3. 浏览器扩展兼容性处理

自动检测和处理可能引起冲突的浏览器扩展：

```typescript
import { ExtensionConflictDetector } from './utils/extensionCompat'

const detector = ExtensionConflictDetector.getInstance()

// 检查是否存在扩展冲突
if (detector.hasConflicts()) {
  const conflicts = detector.getConflictingExtensions()
  console.warn('检测到扩展冲突:', conflicts)
}
```

### 4. 安全包装器

对于可能抛出Selection错误的代码，使用安全包装器：

```typescript
import { createExtensionSafeWrapper } from './utils/extensionCompat'

// 包装可能出错的函数
const safeGetRangeAt = createExtensionSafeWrapper(
  (selection: Selection, index: number) => selection.getRangeAt(index),
  null // fallback值
)

// 安全调用
const selection = window.getSelection()
if (selection) {
  const range = safeGetRangeAt(selection, 0)
  if (range) {
    // 处理范围
  }
}
```

## 使用方法

### 自动初始化

编辑器会在初始化时自动启用错误处理：

```typescript
import Editor from 'canvas-editor'

const editor = new Editor(container, data, options)
// 错误处理器已自动初始化
```

### 手动初始化

如果需要在编辑器外部使用这些工具：

```typescript
import { initializeErrorHandler, initializeExtensionCompat } from 'canvas-editor'

// 初始化错误处理器
initializeErrorHandler()

// 初始化扩展兼容性检测
initializeExtensionCompat()
```

## 常见问题

### Q: 为什么会出现Selection API错误？

A: 主要原因包括：
1. 浏览器扩展干扰（如翻译扩展、广告拦截器等）
2. 页面上同时存在多个富文本编辑器
3. 在不合适的时机调用Selection API
4. 浏览器兼容性问题

### Q: 如何调试Selection相关问题？

A: 可以通过以下方式：

```typescript
// 开启详细日志
localStorage.setItem('canvas-editor-debug', 'true')

// 查看错误统计
const errorHandler = SelectionErrorHandler.getInstance()
console.log(errorHandler.getErrorStats())

// 查看扩展冲突
const detector = ExtensionConflictDetector.getInstance()
console.log(detector.getConflictingExtensions())
```

### Q: 如何禁用某些扩展检测？

A: 目前暂不支持禁用特定检测，但可以通过以下方式减少影响：

```typescript
// 在编辑器初始化前设置
window.__CANVAS_EDITOR_DISABLE_EXTENSION_DETECTION__ = true
```

## 最佳实践

1. **避免直接调用Selection API**：使用提供的安全工具函数
2. **及时清理Selection状态**：在不需要时清除选择状态
3. **监听错误事件**：设置错误监听器以便及时发现问题
4. **测试扩展兼容性**：在常见浏览器扩展环境下测试应用

## 技术细节

### Selection API保护机制

我们通过以下方式保护Selection API：

1. **方法包装**：包装原生Selection方法，添加错误处理
2. **状态检查**：在调用前检查Selection状态
3. **自动恢复**：出错时尝试自动恢复到安全状态
4. **日志记录**：记录所有错误以便分析

### 扩展检测机制

检测可能冲突的扩展：

1. **DOM监听**：监听扩展注入的DOM变化
2. **特征识别**：识别扩展特有的标识和行为
3. **API拦截**：拦截可能被扩展修改的API调用
4. **冲突处理**：对检测到的冲突进行处理

## 更新日志

- v1.0.0: 初始版本，基本的Selection API错误处理
- v1.1.0: 添加扩展兼容性检测
- v1.2.0: 完善错误统计和日志功能
- v1.3.0: 添加自动修复机制
