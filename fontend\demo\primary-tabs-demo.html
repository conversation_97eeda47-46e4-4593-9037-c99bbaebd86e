<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一级标签演示 - 图书排版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: #4991f2;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .demo-area {
            padding: 20px;
        }
        
        /* 模拟右侧工具栏结构 */
        .right-tools-demo {
            border: 1px solid #e2e6ed;
            border-radius: 8px;
            overflow: hidden;
            background: #fff;
            width: 300px;
            height: 500px;
            margin: 20px auto;
            display: flex;
            flex-direction: column;
        }
        
        /* Header样式 */
        .right-tools__header {
            height: 38px;
            min-height: 38px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #e2e6ed;
            padding: 0 15px;
            background: #f5f7fa;
            box-sizing: border-box;
        }
        
        .right-tools__header span {
            color: #3d4757;
            font-size: 14px;
            font-weight: bold;
        }
        
        .right-tools__header__close {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            background: #ff5722;
            color: white;
            font-size: 12px;
        }
        
        /* 一级标签样式 */
        .right-tools__primary-tabs {
            display: flex;
            flex-direction: row;
            background: #e8f4ff;
            border-bottom: 1px solid #d0e7ff;
            height: 35px;
            min-height: 35px;
            width: 100%;
            padding: 0;
            margin: 0;
            flex-shrink: 0;
        }
        
        .right-tools__primary-tab {
            padding: 0 25px;
            height: 35px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all .2s;
            position: relative;
            background: #e8f4ff;
            border-right: 1px solid #d0e7ff;
            margin-bottom: -1px;
            user-select: none;
            flex: 1;
        }
        
        .right-tools__primary-tab span {
            font-size: 14px;
            color: #4991f2;
            display: inline-block;
            white-space: nowrap;
            font-weight: 600;
        }
        
        .right-tools__primary-tab.active {
            background: #fff;
            border-bottom: 2px solid #4991f2;
            z-index: 2;
        }
        
        .right-tools__primary-tab.active span {
            color: #4991f2;
            font-weight: bold;
            font-size: 15px;
        }
        
        .right-tools__primary-tab:hover {
            background: #d4edda;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .right-tools__primary-tab.active:hover {
            transform: none;
            box-shadow: none;
        }
        
        /* 二级标签样式 */
        .right-tools__tabs {
            display: flex;
            flex-direction: row;
            background: #f5f7fa;
            height: 30px;
            min-height: 30px;
            width: 100%;
            padding: 0;
            margin: 0;
            flex-shrink: 0;
        }
        
        .right-tools__tab {
            padding: 0 20px;
            height: 30px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all .2s;
            position: relative;
            background: #f0f0f0;
            border-right: 1px solid #e2e6ed;
            margin-bottom: -1px;
            user-select: none;
            flex: 1;
        }
        
        .right-tools__tab span {
            font-size: 13px;
            color: #606266;
            display: inline-block;
            white-space: nowrap;
            font-weight: 500;
        }
        
        .right-tools__tab.active {
            background: #fff;
            z-index: 1;
        }
        
        .right-tools__tab.active::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: #4991f2;
        }
        
        .right-tools__tab.active span {
            color: #4991f2;
            font-weight: bold;
            font-size: 14px;
        }
        
        .right-tools__tab:hover {
            background: #e8f4ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .right-tools__tab.active:hover {
            transform: none;
            box-shadow: none;
        }
        
        /* 内容区域 */
        .right-tools__contents {
            flex: 1;
            position: relative;
            overflow: hidden;
            background: #fff;
            border-top: none;
            width: 100%;
            height: calc(100% - 103px);
        }
        
        .right-tools__content {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 15px;
            box-sizing: border-box;
            overflow-y: auto;
            display: none;
            background: #fff;
        }
        
        .right-tools__content.active {
            display: block;
        }
        
        .content-placeholder {
            color: #909399;
            text-align: center;
            padding: 20px;
            font-size: 14px;
            border: 1px dashed #dcdfe6;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .hierarchy-demo {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .hierarchy-level {
            margin: 10px 0;
            padding: 8px 12px;
            border-left: 4px solid;
            background: white;
        }
        
        .level-1 {
            border-left-color: #4991f2;
            background: #e8f4ff;
        }
        
        .level-2 {
            border-left-color: #28a745;
            background: #e8f5e8;
            margin-left: 20px;
        }
        
        .level-3 {
            border-left-color: #ffc107;
            background: #fff8e1;
            margin-left: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>一级标签演示 - 图书排版</h1>
            <p>展示新的层级结构：一级标签 → 二级标签 → 内容</p>
        </div>
        
        <div class="demo-area">
            <div class="info-box">
                <h3>📋 层级结构说明</h3>
                <div class="hierarchy-demo">
                    <div class="hierarchy-level level-1">
                        <strong>一级标签：图书排版</strong> (35px高度)
                        <p>顶级分类，用于区分不同的功能模块</p>
                    </div>
                    <div class="hierarchy-level level-2">
                        <strong>二级标签：排版、公式、编写、咨询</strong> (30px高度)
                        <p>具体功能分类，在一级标签下的细分功能</p>
                    </div>
                    <div class="hierarchy-level level-3">
                        <strong>内容区域</strong> (剩余空间)
                        <p>实际的工具和操作界面</p>
                    </div>
                </div>
            </div>
            
            <div class="right-tools-demo">
                <!-- Header -->
                <div class="right-tools__header">
                    <span>工具栏</span>
                    <div class="right-tools__header__close">×</div>
                </div>
                
                <!-- 一级标签：图书排版 -->
                <div class="right-tools__primary-tabs">
                    <div class="right-tools__primary-tab active" data-primary-tab="book-layout">
                        <span>图书排版</span>
                    </div>
                </div>
                
                <!-- 二级标签：具体功能 -->
                <div class="right-tools__tabs">
                    <div class="right-tools__tab active" data-tab="typography">
                        <span>排版</span>
                    </div>
                    <div class="right-tools__tab" data-tab="formula">
                        <span>公式</span>
                    </div>
                    <div class="right-tools__tab" data-tab="writing">
                        <span>编写</span>
                    </div>
                    <div class="right-tools__tab" data-tab="consulting">
                        <span>咨询</span>
                    </div>
                </div>
                
                <!-- 内容区域 -->
                <div class="right-tools__contents">
                    <div class="right-tools__content active" data-tab="typography">
                        <div class="content-placeholder">
                            <h4>排版工具</h4>
                            <p>字体、段落、样式等排版相关工具</p>
                        </div>
                    </div>
                    <div class="right-tools__content" data-tab="formula">
                        <div class="content-placeholder">
                            <h4>公式工具</h4>
                            <p>数学公式编辑和插入工具</p>
                        </div>
                    </div>
                    <div class="right-tools__content" data-tab="writing">
                        <div class="content-placeholder">
                            <h4>编写工具</h4>
                            <p>文本编辑和写作辅助工具</p>
                        </div>
                    </div>
                    <div class="right-tools__content" data-tab="consulting">
                        <div class="content-placeholder">
                            <h4>咨询工具</h4>
                            <p>AI助手和咨询相关功能</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="info-box">
                <h3>✨ 新增特性</h3>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>层级清晰：</strong>一级标签"图书排版"明确功能定位</li>
                    <li><strong>视觉区分：</strong>一级标签使用不同的背景色和高度</li>
                    <li><strong>空间优化：</strong>二级标签高度降为30px，节省空间</li>
                    <li><strong>交互增强：</strong>悬停效果提升用户体验</li>
                    <li><strong>扩展性好：</strong>未来可以添加更多一级分类</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 二级标签切换
            const tabs = document.querySelectorAll('.right-tools__tab');
            const contents = document.querySelectorAll('.right-tools__content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    
                    // 切换标签状态
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 切换内容显示
                    contents.forEach(content => {
                        content.classList.remove('active');
                        if (content.getAttribute('data-tab') === tabId) {
                            content.classList.add('active');
                        }
                    });
                });
            });
            
            // 一级标签点击效果（演示用）
            const primaryTab = document.querySelector('.right-tools__primary-tab');
            if (primaryTab) {
                primaryTab.addEventListener('click', function() {
                    console.log('点击了一级标签：图书排版');
                });
            }
            
            console.log('📚 图书排版一级标签演示已加载');
        });
    </script>
</body>
</html>
