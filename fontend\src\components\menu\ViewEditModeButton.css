/* 视图编辑模式按钮样式 */
.view-editor-mode-btn {
  display: inline-block;
  padding: 6px 12px;
  margin: 2px;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #374151;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.2;
  text-align: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
  min-width: 60px;
}

/* 悬停效果 */
.view-editor-mode-btn:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
  color: #1f2937;
}

/* 激活状态 */
.view-editor-mode-btn.active {
  background-color: #004ecc !important;
  border-color: #2563eb;
  color: #ffffff;
  font-weight: 500;
}

/* 激活状态悬停效果 */
.view-editor-mode-btn.active:hover {
  background-color: #2563eb;
  border-color: #1d4ed8;
}

/* 禁用状态 */
.view-editor-mode-btn:disabled,
.view-editor-mode-btn.disabled {
  background-color: #f9fafb;
  border-color: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* 特定模式按钮样式 */
.view-edit-mode {
  /* 编辑模式 - 绿色主题 */
}

.view-clean-mode {
  /* 清洁模式 - 蓝色主题 */
}

.view-readonly-mode {
  /* 只读模式 - 灰色主题 */
}

.view-form-mode {
  /* 表单模式 - 橙色主题 */
}

.view-print-mode {
  /* 打印模式 - 紫色主题 */
}

.view-design-mode {
  /* 设计模式 - 红色主题 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .view-editor-mode-btn {
    padding: 4px 8px;
    font-size: 11px;
    min-width: 50px;
  }
}