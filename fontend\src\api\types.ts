/**
 * API类型定义
 * 定义所有API相关的TypeScript类型
 */

// 基础响应类型
export interface BaseResponse {
  success: boolean
  message?: string
  timestamp?: string
}

// 错误响应类型
export interface ErrorResponse extends BaseResponse {
  success: false
  error_code?: string
  details?: any
}

// 成功响应类型
export interface SuccessResponse<T = any> extends BaseResponse {
  success: true
  data: T
}

// 分页参数类型
export interface PaginationParams {
  page?: number
  page_size?: number
  limit?: number
  offset?: number
}

// 排序参数类型
export interface SortingParams {
  ordering?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// 搜索参数类型
export interface SearchParams {
  search?: string
  q?: string
  keyword?: string
}

// 过滤参数类型
export interface FilterParams {
  [key: string]: any
}

// 查询参数类型
export interface QueryParams extends PaginationParams, SortingParams, SearchParams, FilterParams {}

// 文档状态枚举
export enum DocumentStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived'
}

// 文档权限枚举
export enum DocumentPermission {
  PRIVATE = 'private',
  PUBLIC = 'public',
  SHARED = 'shared'
}

// 用户角色枚举
export enum UserRole {
  ADMIN = 'admin',
  EDITOR = 'editor',
  VIEWER = 'viewer'
}

// 文件类型枚举
export enum FileType {
  IMAGE = 'image',
  DOCUMENT = 'document',
  ATTACHMENT = 'attachment'
}

// 编辑器数据类型（基于canvas-editor的数据结构）
export interface EditorData {
  main: EditorElement[]
  header?: EditorElement[]
  footer?: EditorElement[]
}

// 编辑器元素类型
export interface EditorElement {
  value: string
  type?: string
  style?: ElementStyle
  [key: string]: any
}

// 元素样式类型
export interface ElementStyle {
  fontSize?: number
  fontFamily?: string
  color?: string
  bold?: boolean
  italic?: boolean
  underline?: boolean
  [key: string]: any
}

// 用户信息类型
export interface User {
  id: number
  username: string
  email: string
  first_name?: string
  last_name?: string
  avatar?: string
  role: UserRole
  is_active: boolean
  date_joined: string
  last_login?: string
}

// 文档类型（扩展版本）
export interface DocumentDetail {
  id: number
  title: string
  content: EditorData
  status: DocumentStatus
  permission: DocumentPermission
  author: User
  collaborators?: User[]
  created_at: string
  updated_at: string
  version: number
  word_count?: number
  is_favorite?: boolean
  tags?: string[]
  category?: string
  description?: string
}

// 文档版本类型（扩展版本）
export interface DocumentVersionDetail {
  id: number
  document: number
  version_number: number
  content: EditorData
  created_at: string
  created_by: User
  comment?: string
  word_count?: number
  changes_summary?: string
}

// 文件上传响应类型
export interface UploadResponse {
  id: number
  url: string
  filename: string
  original_name: string
  file_type: FileType
  file_size: number
  mime_type: string
  created_at: string
  created_by?: User
}

// 认证相关类型
export interface LoginRequest {
  username: string
  password: string
  remember_me?: boolean
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  user: User
  expires_in: number
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  first_name?: string
  last_name?: string
}

// 文档操作类型
export interface DocumentCreateRequest {
  title: string
  content: EditorData
  status?: DocumentStatus
  permission?: DocumentPermission
  tags?: string[]
  category?: string
  description?: string
}

export interface DocumentUpdateRequest {
  title?: string
  content?: EditorData
  status?: DocumentStatus
  permission?: DocumentPermission
  tags?: string[]
  category?: string
  description?: string
}

// 协作相关类型
export interface CollaborationInvite {
  document_id: number
  user_email: string
  permission: 'read' | 'write' | 'admin'
  message?: string
}

export interface CollaborationSession {
  id: string
  document_id: number
  user: User
  cursor_position?: number
  selection_range?: [number, number]
  last_activity: string
  is_active: boolean
}

// 评论相关类型
export interface Comment {
  id: number
  document_id: number
  author: User
  content: string
  position?: {
    start: number
    end: number
  }
  created_at: string
  updated_at: string
  replies?: Comment[]
  is_resolved?: boolean
}

// 搜索结果类型
export interface SearchResult {
  documents: DocumentDetail[]
  total_count: number
  search_time: number
  suggestions?: string[]
}

// 统计数据类型
export interface DocumentStats {
  total_documents: number
  total_words: number
  documents_by_status: Record<DocumentStatus, number>
  recent_activity: {
    date: string
    documents_created: number
    documents_updated: number
  }[]
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string
  data: any
  timestamp: string
  user_id?: number
}

// 导出所有类型
export type {
  BaseResponse,
  ErrorResponse,
  SuccessResponse,
  PaginationParams,
  SortingParams,
  SearchParams,
  FilterParams,
  QueryParams,
  EditorData,
  EditorElement,
  ElementStyle,
  User,
  DocumentDetail,
  DocumentVersionDetail,
  UploadResponse,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  DocumentCreateRequest,
  DocumentUpdateRequest,
  CollaborationInvite,
  CollaborationSession,
  Comment,
  SearchResult,
  DocumentStats,
  WebSocketMessage
}
