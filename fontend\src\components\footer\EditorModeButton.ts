import { CanvasEditor, EditorMode } from '../../editor'
import html from './EditorModeButton.html'
import './EditorModeButton.css'

export class EditorModeButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor
  private modeIndex = 0
  private modeList = [
    {
      mode: EditorMode.EDIT,
      name: '编辑模式'
    },
    {
      mode: EditorMode.CLEAN,
      name: '清洁模式'
    },
    {
      mode: EditorMode.READONLY,
      name: '只读模式'
    },
    {
      mode: EditorMode.FORM,
      name: '表单模式'
    },
    {
      mode: EditorMode.PRINT,
      name: '打印模式'
    },
    {
      mode: EditorMode.DESIGN,
      name: '设计模式'
    }
  ]

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.toggleMode()
    }
  }
  
  private toggleMode(): void {
    // 模式选择循环
    this.modeIndex = this.modeIndex === this.modeList.length - 1 ? 0 : this.modeIndex + 1
    
    // 设置模式
    const { name, mode } = this.modeList[this.modeIndex]
    this.dom.innerText = name
    this.instance.command.executeMode(mode)
    
    // 设置菜单栏权限视觉反馈
    const isReadonly = mode === EditorMode.READONLY
    const enableMenuList = ['search', 'print']
    document.querySelectorAll<HTMLDivElement>('.menu-item>div').forEach(dom => {
      const menu = dom.dataset.menu
      if (isReadonly && (!menu || !enableMenuList.includes(menu))) {
        dom.classList.add('disable')
      } else {
        dom.classList.remove('disable')
      }
    })
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 