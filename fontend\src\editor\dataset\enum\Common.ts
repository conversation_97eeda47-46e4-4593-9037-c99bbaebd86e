export enum MaxHeightRatio {
  HALF = 'half',
  ONE_THIRD = 'one-third',
  QUARTER = 'quarter'
}

export enum NumberType {
  ARABIC = 'arabic',
  CHINESE = 'chinese'
}

export enum ImageDisplay {
  INLINE = 'inline',
  BLOCK = 'block',
  SURROUND = 'surround',
  FLOAT_TOP = 'float-top',
  FLOAT_BOTTOM = 'float-bottom'
}

export enum LocationPosition {
  BEFORE = 'before',
  AFTER = 'after',
  OUTER_BEFORE = 'outer-before',
  OUTER_AFTER = 'outer-after'
}

export enum FlexDirection {
  ROW = 'row',
  COLUMN = 'column'
}
