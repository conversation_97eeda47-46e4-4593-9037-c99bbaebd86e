#!/usr/bin/env python
"""
虚拟环境检查脚本
验证虚拟环境是否正确设置
"""

import sys
import os
from pathlib import Path

def check_venv_exists():
    """检查虚拟环境是否存在"""
    venv_path = Path(".venv")
    if venv_path.exists():
        print("✅ 虚拟环境目录存在: .venv/")
        return True
    else:
        print("❌ 虚拟环境目录不存在")
        return False

def check_python_in_venv():
    """检查是否在虚拟环境中运行"""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 当前在虚拟环境中运行")
        print(f"   Python 路径: {sys.executable}")
        return True
    else:
        print("⚠️  当前不在虚拟环境中运行")
        print(f"   Python 路径: {sys.executable}")
        return False

def check_django_installed():
    """检查 Django 是否安装"""
    try:
        import django
        print(f"✅ Django 已安装: {django.get_version()}")
        return True
    except ImportError:
        print("❌ Django 未安装")
        return False

def check_requirements():
    """检查主要依赖是否安装"""
    requirements = [
        ('rest_framework', 'Django REST Framework'),
        ('corsheaders', 'Django CORS Headers'),
        ('decouple', 'Python Decouple'),
    ]
    
    all_installed = True
    for module, name in requirements:
        try:
            __import__(module)
            print(f"✅ {name} 已安装")
        except ImportError:
            print(f"❌ {name} 未安装")
            all_installed = False
    
    return all_installed

def show_recommendations():
    """显示建议"""
    print("\n" + "="*50)
    print("💡 建议:")
    print("="*50)
    
    if not check_venv_exists():
        print("1. 创建虚拟环境:")
        print("   python -m venv .venv")
    
    if not check_python_in_venv():
        print("2. 激活虚拟环境:")
        print("   Windows: .venv\\Scripts\\activate")
        print("   Linux/Mac: source .venv/bin/activate")
    
    if not check_django_installed() or not check_requirements():
        print("3. 安装依赖:")
        print("   pip install -r requirements.txt")
    
    print("4. 启动项目:")
    print("   python start.py 8000")

def main():
    """主函数"""
    print("🔍 虚拟环境检查工具")
    print("="*30)
    
    # 检查当前目录
    if not Path("manage.py").exists():
        print("❌ 请在 backend 目录下运行此脚本")
        sys.exit(1)
    
    print(f"📁 当前目录: {os.getcwd()}")
    print(f"🐍 Python 版本: {sys.version}")
    print()
    
    # 执行检查
    venv_exists = check_venv_exists()
    in_venv = check_python_in_venv()
    django_installed = check_django_installed()
    deps_installed = check_requirements()
    
    print("\n" + "="*30)
    if venv_exists and in_venv and django_installed and deps_installed:
        print("🎉 虚拟环境配置完美！")
        print("可以开始开发了！")
    else:
        print("⚠️  虚拟环境需要进一步配置")
        show_recommendations()

if __name__ == "__main__":
    main()
