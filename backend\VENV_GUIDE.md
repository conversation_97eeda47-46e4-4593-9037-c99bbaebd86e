# Python 虚拟环境使用指南

## 🐍 虚拟环境说明

虚拟环境 `.venv` 已在 backend 目录下创建完成，用于隔离项目依赖，避免与系统 Python 环境冲突。

## 🚀 快速开始

### 方法一：使用便捷脚本

#### Windows 用户
```bash
# 双击运行或在命令行执行
activate_venv.bat
```

#### Linux/Mac 用户
```bash
# 在终端执行
source activate_venv.sh
```

### 方法二：手动激活

#### Windows
```bash
# 激活虚拟环境
.venv\Scripts\activate

# 验证激活成功
python --version
pip --version
```

#### Linux/Mac
```bash
# 激活虚拟环境
source .venv/bin/activate

# 验证激活成功
python --version
pip --version
```

## 📦 安装依赖

激活虚拟环境后，安装项目依赖：

```bash
# 升级 pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
```

## 🔧 自动化设置

使用自动化设置脚本：

```bash
# 自动创建虚拟环境并安装依赖
python setup_venv.py
```

这个脚本会：
1. 检查 Python 版本
2. 创建虚拟环境
3. 升级 pip
4. 安装所有依赖
5. 显示使用说明

## 🎯 使用虚拟环境运行项目

```bash
# 1. 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows

# 2. 启动项目
python start.py 8000

# 或使用 Django 命令
python manage.py runserver 8000
```

## 📋 常用命令

### 虚拟环境管理
```bash
# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows

# 退出虚拟环境
deactivate

# 查看虚拟环境路径
which python  # Linux/Mac
where python  # Windows
```

### 包管理
```bash
# 查看已安装包
pip list

# 查看包详情
pip show django

# 导出依赖列表
pip freeze > requirements.txt

# 安装特定包
pip install package_name

# 卸载包
pip uninstall package_name
```

### Django 命令
```bash
# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 启动开发服务器
python manage.py runserver

# 进入 Django shell
python manage.py shell
```

## 🔍 验证虚拟环境

激活虚拟环境后，验证是否正确：

```bash
# 检查 Python 路径（应该指向 .venv 目录）
which python    # Linux/Mac
where python    # Windows

# 检查已安装的包
pip list

# 应该看到 Django、djangorestframework 等包
```

## 🚨 常见问题

### 1. 激活脚本无法执行
**Windows PowerShell 执行策略问题**
```powershell
# 临时允许脚本执行
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 2. 虚拟环境创建失败
```bash
# 确保 Python 版本 >= 3.8
python --version

# 重新创建虚拟环境
rm -rf .venv  # Linux/Mac
rmdir /s .venv  # Windows
python -m venv .venv
```

### 3. 依赖安装失败
```bash
# 升级 pip
pip install --upgrade pip

# 清除缓存重新安装
pip cache purge
pip install -r requirements.txt
```

### 4. 找不到 Django 命令
```bash
# 确保虚拟环境已激活
# 检查 Django 是否安装
pip show django

# 重新安装 Django
pip install Django==5.0.7
```

## 📁 目录结构

```
backend/
├── .venv/                 # 虚拟环境目录
│   ├── Scripts/          # Windows 可执行文件
│   ├── bin/              # Linux/Mac 可执行文件
│   ├── Lib/              # Python 库
│   └── pyvenv.cfg        # 虚拟环境配置
├── activate_venv.bat     # Windows 激活脚本
├── activate_venv.sh      # Linux/Mac 激活脚本
├── setup_venv.py         # 自动化设置脚本
├── requirements.txt      # 依赖列表
└── manage.py            # Django 管理脚本
```

## 💡 最佳实践

1. **始终在虚拟环境中工作**
2. **定期更新 requirements.txt**
3. **不要将 .venv 目录提交到版本控制**
4. **使用 pip freeze 导出精确的依赖版本**
5. **在不同环境中测试项目**

## 🔄 重建虚拟环境

如果需要重建虚拟环境：

```bash
# 1. 退出当前虚拟环境
deactivate

# 2. 删除虚拟环境目录
rm -rf .venv  # Linux/Mac
rmdir /s .venv  # Windows

# 3. 重新创建
python -m venv .venv

# 4. 激活并安装依赖
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows
pip install -r requirements.txt
```
