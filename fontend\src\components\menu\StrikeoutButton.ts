import './StrikeoutButton.css'

export class StrikeoutButton {
  private element: HTMLDivElement;
  private command: any;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.strikeout-button') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="strikeout-button" title="删除线(Ctrl+Shift+X)">
      <i></i>
    </div>`
  }

  private bindEvents(): void {
    this.element.onclick = () => {
      console.log('strikeout')
      this.command.executeStrikeout()
    }
  }

  // 更新按钮状态
  public updateState(isStrikeout: boolean): void {
    if (isStrikeout) {
      this.element.classList.add('active')
    } else {
      this.element.classList.remove('active')
    }
  }
} 