import './FontSizeAddButton.css'

export class FontSizeAddButton {
  private element: HTMLDivElement;
  private command: any;
  private isApple: boolean;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    this.isApple = typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent)
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.font-size-add-button') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="font-size-add-button" title="增大字号(${this.isApple ? '⌘' : 'Ctrl'}+[)">
      <i></i>
    </div>`
  }

  private bindEvents(): void {
    this.element.onclick = () => {
      console.log('size-add')
      this.command.executeSizeAdd()
    }
  }
} 