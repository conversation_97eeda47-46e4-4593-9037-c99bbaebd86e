import { commentList, editorData, options } from '../mock'
import prism from 'prismjs'
import Editor, {
  BlockType,
  ControlType,
  EditorMode,
  ElementType,
  IElement,
  IRangeStyle
} from '../editor'
import { IBlock } from '../editor/interface/Block'
import { Dialog } from '../components/dialog/Dialog'
import { formatPrismToken } from '../utils/prism'
import { Signature } from '../components/signature/Signature'
import { debounce } from '../utils'
// 导入顶部菜单组件
import {
  UndoButton,
  RedoButton,
  PainterButton,
  FormatButton,
  NewFontButton,
  NewFontSizeButton,
  FontSizeAddButton,
  FontSizeMinusButton,
  BoldButton,
  ItalicButton,
  UnderlineButton,
  StrikeoutButton,
  SuperscriptButton,
  SubscriptButton,
  ColorButton,
  HighlightButton,
  TitleButton,
  NewTitleButton,
  NormalTextButton,
  Title1Button,
  Title2Button,
  Title3Button,
  Title4Button,
  Title5Button,
  Title6Button,
  HomeFontButton,
  HomeFontSizeButton,
  HomeTableButton,
  HomeCatalogButton,
  HomeMarginButton,
  HomeOrientationButton,
  HomeLineBreakButton,
  HomePrintButton,
  AlignLeftButton,
  AlignCenterButton,
  AlignRightButton,
  AlignmentButton,
  JustifyButton,
  RowMarginButton,
  ListButton,
  LineBreakButton,
  TableButton,
  ImageButton,
  HyperlinkButton,
  SeparatorButton,
  WatermarkButton,
  CodeBlockButton,
  PageBreakButton,
  ControlButton,
  CheckboxButton,
  RadioButton,
  LatexButton,
  DateButton,
  BlockButton,
  SearchButton,
  PrintButton,
  CommentButton,
  HomeNormalTextButton,
  HomeTitle1Button,
  HomeTitle2Button,
  HomeTitle3Button,
  HomeTitle4Button,
  HomeTitle5Button,
  HomeTitle6Button,
  LayoutPaperSizeButton,
  LayoutPaperDirectionButton,
  LayoutPageModeButton,
  LayoutPaperMarginButton,
  LayoutFullscreenButton,
  LayoutEditorOptionButton,
  ViewEditModeButton,
  ViewCleanModeButton,
  ViewReadonlyModeButton,
  ViewFormModeButton,
  ViewPrintModeButton,
  ViewDesignModeButton
} from '../components/menu'

// 导入底部菜单组件
import { Footer } from '../components/footer'

// 导入工具组件
import { Catalog, RightTools } from '../components/rightTools'

// 导入Ribbon菜单控制器
import { RibbonMenu } from '../components/menu/RibbonMenu'

// 导入下拉框定位器
import { DropdownPositioner } from '../components/menu/DropdownPositioner'

// 导入API模块
import { initApiModule } from '../api'
import { createEditorApiIntegration } from '../components/api/EditorApiIntegration'
import { getApiTestPanel } from '../components/api/ApiTestPanel'

// 导入样式配置模块
import { initStylesConfigModule } from '../stylesConfig'
import '../stylesConfig/test-config'

// 定义评论接口，符合项目中使用的属性
interface IComment {
  id: string;
  userName: string;
  createdDate: string;
  content: string;
  rangeText?: string;
  startIndex?: number;
  endIndex?: number;
  pageNo?: number;
  isEdit?: boolean;
  active?: boolean;
}

// 全局变量，用于存储组件实例
let catalogInstance: Catalog | null = null
let rightToolsInstance: RightTools | null = null
let editorInstance: Editor | null = null
let ribbonMenuInstance: RibbonMenu | null = null
let editorApiIntegration: any = null

/**
 * 初始化编辑器
 * @returns 返回编辑器实例
 */
export function initEditor(): Editor {
  // 检查是否已经初始化
  if (editorInstance) {
    return editorInstance
  }

  const isApple =
    typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent)

  // 1. 初始化编辑器
  const container = document.querySelector<HTMLDivElement>('.editor')!
  const instance = new Editor(
    container,
    editorData,
    options
  )

  // cypress使用
  Reflect.set(window, 'editor', instance)

  // 保存实例以供后续使用
  editorInstance = instance

  // 初始化API集成
  initApiIntegration(instance)

  // 初始化样式配置模块
  initStylesConfigModule().then(() => {
    console.log('✅ 字体样式配置模块初始化完成')
  }).catch(error => {
    console.error('❌ 字体样式配置模块初始化失败:', error)
  })

  return instance
}

/**
 * 初始化菜单和工具组件
 * @param instance 编辑器实例
 */
export function initMenuAndTools(instance: Editor): void {
  // 添加标记属性，防止重复初始化
  if (document.querySelector('.menu')?.getAttribute('data-initialized') === 'true') {
    console.log('菜单已初始化，跳过重复初始化')
    // 仍然初始化事件监听器等非UI部分
    initEvents(instance)
    return
  }

  document.querySelector('.menu')?.setAttribute('data-initialized', 'true')

  // 加载菜单和底部菜单HTML模板
  const menuContainer = document.querySelector('.menu')
  const footerContainer = document.querySelector('.footer-container')
  const catalogContainer = document.querySelector('.catalog-container')

  // 只加载菜单HTML模板，底部菜单使用Footer组件，目录使用Catalog组件
  fetch('./src/components/menu/menu-index.html')
    .then(response => response.text())
    .then(menuHtml => {
      // 设置菜单HTML
      if (menuContainer) {
        menuContainer.innerHTML = menuHtml

        // 初始化顶部菜单组件
        // 2. | 撤销 | 重做 | 格式刷 | 清除格式 |
        new UndoButton(document.querySelector('.menu-item__undo')!, instance.command)
        new RedoButton(document.querySelector('.menu-item__redo')!, instance.command)
        new PainterButton(document.querySelector('.menu-item__painter')!, instance.command)
        new FormatButton(document.querySelector('.menu-item__format')!, instance.command)

        // 3. | 字体选择 | 字号选择 | 字体变大 | 字体变小 | 加粗 | 斜体 | 下划线 | 删除线 | 上标 | 下标 | 字体颜色 | 背景色 |
        new NewFontButton(document.querySelector('.menu-item__new-font')!, instance.command)
        new NewFontSizeButton(document.querySelector('.menu-item__new-font-size')!, instance.command)
        new FontSizeAddButton(document.querySelector('.menu-item__size-add')!, instance.command)
        new FontSizeMinusButton(document.querySelector('.menu-item__size-minus')!, instance.command)
        new BoldButton(document.querySelector('.menu-item__bold')!, instance.command)
        new ItalicButton(document.querySelector('.menu-item__italic')!, instance.command)
        new UnderlineButton(document.querySelector('.menu-item__underline')!, instance.command)
        new StrikeoutButton(document.querySelector('.menu-item__strikeout')!, instance.command)
        new SuperscriptButton(document.querySelector('.menu-item__superscript')!, instance.command)
        new SubscriptButton(document.querySelector('.menu-item__subscript')!, instance.command)
        new ColorButton(document.querySelector('.menu-item__color')!, instance.command)
        new HighlightButton(document.querySelector('.menu-item__highlight')!, instance.command)

        // 4. | 标题按钮组 | 左对齐 | 居中对齐 | 右对齐 | 两端对齐 | 分散对齐 | 行间距 | 列表 |
        // 初始化段落菜单中的独立标题按钮
        const normalTextBtn = new NormalTextButton(instance)
        document.querySelector('.menu-item__normal-text')!.replaceWith(normalTextBtn.getElement())

        const title1Btn = new Title1Button(instance)
        document.querySelector('.menu-item__title1')!.replaceWith(title1Btn.getElement())

        const title2Btn = new Title2Button(instance)
        document.querySelector('.menu-item__title2')!.replaceWith(title2Btn.getElement())

        const title3Btn = new Title3Button(instance)
        document.querySelector('.menu-item__title3')!.replaceWith(title3Btn.getElement())

        const title4Btn = new Title4Button(instance)
        document.querySelector('.menu-item__title4')!.replaceWith(title4Btn.getElement())

        const title5Btn = new Title5Button(instance)
        document.querySelector('.menu-item__title5')!.replaceWith(title5Btn.getElement())

        const title6Btn = new Title6Button(instance)
        document.querySelector('.menu-item__title6')!.replaceWith(title6Btn.getElement())

        const alignLeftBtn = new AlignLeftButton(instance)
        document.querySelector('.menu-item__left')!.replaceWith(alignLeftBtn.getElement())

        const alignCenterBtn = new AlignCenterButton(instance)
        document.querySelector('.menu-item__center')!.replaceWith(alignCenterBtn.getElement())

        const alignRightBtn = new AlignRightButton(instance)
        document.querySelector('.menu-item__right')!.replaceWith(alignRightBtn.getElement())

        const alignmentBtn = new AlignmentButton(instance)
        document.querySelector('.menu-item__alignment')!.replaceWith(alignmentBtn.getElement())

        const justifyBtn = new JustifyButton(instance)
        document.querySelector('.menu-item__justify')!.replaceWith(justifyBtn.getElement())

        const rowMarginBtn = new RowMarginButton(instance)
        document.querySelector('.menu-item__row-margin')!.replaceWith(rowMarginBtn.getElement())

        const listBtn = new ListButton(instance)
        document.querySelector('.menu-item__list')!.replaceWith(listBtn.getElement())

        // 添加段落结束标记按钮
        const lineBreakBtn = new LineBreakButton(instance)
        document.querySelector('.menu-item__line-break')!.replaceWith(lineBreakBtn.getElement())

        // 5. | 表格 | 图片 | 超链接 | 分割线 | 水印 | 代码块 | 分页符 | 控件 | 复选框 | 单选框 | LaTeX | 日期 | 内容块 |
        const tableBtn = new TableButton(instance)
        document.querySelector('.menu-item__table')!.replaceWith(tableBtn.getElement())

        const imageBtn = new ImageButton(instance)
        document.querySelector('.menu-item__image')!.replaceWith(imageBtn.getElement())

        const hyperlinkBtn = new HyperlinkButton(instance)
        document.querySelector('.menu-item__hyperlink')!.replaceWith(hyperlinkBtn.getElement())

        const separatorBtn = new SeparatorButton(instance)
        document.querySelector('.menu-item__separator')!.replaceWith(separatorBtn.getElement())

        // 初始化所有水印按钮（插入选项卡和布局选项卡中的）
        const watermarkButtons = document.querySelectorAll('.menu-item__watermark')
        watermarkButtons.forEach(watermarkElement => {
          const watermarkBtn = new WatermarkButton(instance)
          watermarkElement.replaceWith(watermarkBtn.getElement())
        })

        const codeBlockBtn = new CodeBlockButton(instance)
        document.querySelector('.menu-item__codeblock')!.replaceWith(codeBlockBtn.getElement())

        const pageBreakBtn = new PageBreakButton(instance)
        document.querySelector('.menu-item__page-break')!.replaceWith(pageBreakBtn.getElement())

        const controlBtn = new ControlButton(instance)
        document.querySelector('.menu-item__control')!.replaceWith(controlBtn.getElement())

        const checkboxBtn = new CheckboxButton(instance)
        document.querySelector('.menu-item__checkbox')!.replaceWith(checkboxBtn.getElement())

        const radioBtn = new RadioButton(instance)
        document.querySelector('.menu-item__radio')!.replaceWith(radioBtn.getElement())

        const latexBtn = new LatexButton(instance)
        document.querySelector('.menu-item__latex')!.replaceWith(latexBtn.getElement())

        const dateBtn = new DateButton(instance)
        document.querySelector('.menu-item__date')!.replaceWith(dateBtn.getElement())

        const blockBtn = new BlockButton(instance)
        document.querySelector('.menu-item__block')!.replaceWith(blockBtn.getElement())

        // 6. | 搜索 | 打印 |
        const searchBtn = new SearchButton(instance)
        document.querySelector('.menu-item__search')!.replaceWith(searchBtn.getElement())

        const printBtn = new PrintButton(instance)
        document.querySelector('.menu-item__print')!.replaceWith(printBtn.getElement())

        // 添加批注按钮
        const commentBtn = new CommentButton(instance)
        document.querySelector('.menu-item__comment')!.replaceWith(commentBtn.getElement())

        // 添加开始菜单标题按钮组
        const homeNormalTextBtn = new HomeNormalTextButton(instance)
        document.querySelector('.menu-item__home-normal-text')!.replaceWith(homeNormalTextBtn.getElement())

        const homeTitle1Btn = new HomeTitle1Button(instance)
        document.querySelector('.menu-item__home-title1')!.replaceWith(homeTitle1Btn.getElement())

        const homeTitle2Btn = new HomeTitle2Button(instance)
        document.querySelector('.menu-item__home-title2')!.replaceWith(homeTitle2Btn.getElement())

        const homeTitle3Btn = new HomeTitle3Button(instance)
        document.querySelector('.menu-item__home-title3')!.replaceWith(homeTitle3Btn.getElement())

        const homeTitle4Btn = new HomeTitle4Button(instance)
        document.querySelector('.menu-item__home-title4')!.replaceWith(homeTitle4Btn.getElement())

        const homeTitle5Btn = new HomeTitle5Button(instance)
        document.querySelector('.menu-item__home-title5')!.replaceWith(homeTitle5Btn.getElement())

        const homeTitle6Btn = new HomeTitle6Button(instance)
        document.querySelector('.menu-item__home-title6')!.replaceWith(homeTitle6Btn.getElement())

        // 添加字体按钮到开始菜单
        const homeFontBtn = new HomeFontButton(instance)
        document.querySelector('.menu-item__home-font')!.replaceWith(homeFontBtn.getElement())

        // 添加字号按钮到开始菜单
        const homeFontSizeBtn = new HomeFontSizeButton(instance)
        document.querySelector('.menu-item__home-font-size')!.replaceWith(homeFontSizeBtn.getElement())

        // 添加表格按钮到开始菜单
        const homeTableBtn = new HomeTableButton(instance)
        document.querySelector('.menu-item__home-table')!.replaceWith(homeTableBtn.getElement())

        // 添加目录按钮到开始菜单
        const homeCatalogBtn = new HomeCatalogButton(instance)
        document.querySelector('.menu-item__home-catalog')!.replaceWith(homeCatalogBtn.getElement())

        // 添加页边距按钮到开始菜单
        const homeMarginBtn = new HomeMarginButton(instance)
        document.querySelector('.menu-item__home-margin')!.replaceWith(homeMarginBtn.getElement())

        // 添加纸张方向按钮到开始菜单
        const homeOrientationBtn = new HomeOrientationButton(instance)
        document.querySelector('.menu-item__home-orientation')!.replaceWith(homeOrientationBtn.getElement())

        // 添加段落结束标记按钮到开始菜单
        const homeLineBreakBtn = new HomeLineBreakButton(instance)
        document.querySelector('.menu-item__home-line-break')!.replaceWith(homeLineBreakBtn.getElement())

        // 添加打印按钮到开始菜单
        const homePrintBtn = new HomePrintButton(instance)
        document.querySelector('.menu-item__home-print')!.replaceWith(homePrintBtn.getElement())

        // 初始化布局菜单组件
        const layoutPaperSizeBtn = new LayoutPaperSizeButton(instance)
        const paperSizeElement = document.querySelector('.paper-size')
        if (paperSizeElement) {
          paperSizeElement.replaceWith(layoutPaperSizeBtn.getElement())

        }

        const layoutPaperDirectionBtn = new LayoutPaperDirectionButton(instance)
        const paperDirectionElement = document.querySelector('.paper-direction')
        if (paperDirectionElement) {
          paperDirectionElement.replaceWith(layoutPaperDirectionBtn.getElement())

        }

        const layoutPageModeBtn = new LayoutPageModeButton(instance)
        const pageModeElement = document.querySelector('.page-mode')
        if (pageModeElement) {
          pageModeElement.replaceWith(layoutPageModeBtn.getElement())

        }

        const layoutPaperMarginBtn = new LayoutPaperMarginButton(instance)
        const paperMarginElement = document.querySelector('.paper-margin')
        if (paperMarginElement) {
          paperMarginElement.replaceWith(layoutPaperMarginBtn.getElement())

        }

        const layoutFullscreenBtn = new LayoutFullscreenButton(instance)
        const fullscreenElement = document.querySelector('.fullscreen')
        if (fullscreenElement) {
          fullscreenElement.replaceWith(layoutFullscreenBtn.getElement())

        }

        const layoutEditorOptionBtn = new LayoutEditorOptionButton(instance)
        const editorOptionElement = document.querySelector('.editor-option')
        if (editorOptionElement) {
          editorOptionElement.replaceWith(layoutEditorOptionBtn.getElement())

        }

        // 初始化视图编辑模式按钮组件
        const viewEditModeBtn = new ViewEditModeButton(instance)
        const viewEditModeElement = document.querySelector('.menu-item__view-edit-mode')
        if (viewEditModeElement) {
          viewEditModeElement.replaceWith(viewEditModeBtn.getElement())
        }

        const viewCleanModeBtn = new ViewCleanModeButton(instance)
        const viewCleanModeElement = document.querySelector('.menu-item__view-clean-mode')
        if (viewCleanModeElement) {
          viewCleanModeElement.replaceWith(viewCleanModeBtn.getElement())
        }

        const viewReadonlyModeBtn = new ViewReadonlyModeButton(instance)
        const viewReadonlyModeElement = document.querySelector('.menu-item__view-readonly-mode')
        if (viewReadonlyModeElement) {
          viewReadonlyModeElement.replaceWith(viewReadonlyModeBtn.getElement())
        }

        const viewFormModeBtn = new ViewFormModeButton(instance)
        const viewFormModeElement = document.querySelector('.menu-item__view-form-mode')
        if (viewFormModeElement) {
          viewFormModeElement.replaceWith(viewFormModeBtn.getElement())
        }

        const viewPrintModeBtn = new ViewPrintModeButton(instance)
        const viewPrintModeElement = document.querySelector('.menu-item__view-print-mode')
        if (viewPrintModeElement) {
          viewPrintModeElement.replaceWith(viewPrintModeBtn.getElement())
        }

        const viewDesignModeBtn = new ViewDesignModeButton(instance)
        const viewDesignModeElement = document.querySelector('.menu-item__view-design-mode')
        if (viewDesignModeElement) {
          viewDesignModeElement.replaceWith(viewDesignModeBtn.getElement())
        }

        // 初始化Ribbon菜单控制器
        const ribbonMenuContainer = document.querySelector('.ribbon-menu')
        if (ribbonMenuContainer) {
          ribbonMenuInstance = new RibbonMenu(ribbonMenuContainer as HTMLElement)


          // 将ribbonMenuInstance暴露到window对象，方便调试
          Reflect.set(window, 'ribbonMenuInstance', ribbonMenuInstance)
        } else {
          console.error('未找到Ribbon菜单容器')
        }

        // 初始化下拉框定位器
        const dropdownPositioner = DropdownPositioner.getInstance()
        dropdownPositioner.init()


        // 将dropdownPositioner暴露到window对象，方便调试
        Reflect.set(window, 'dropdownPositioner', dropdownPositioner)
      }

      // 初始化底部菜单组件（使用Footer组件）
      if (footerContainer) {
        const footer = new Footer(instance)
        footerContainer.appendChild(footer.getElement())
      }

      // 初始化目录组件（使用Catalog组件）
      if (catalogContainer) {
        catalogInstance = new Catalog(instance)
        catalogContainer.appendChild(catalogInstance.getElement())

        // 将catalogInstance暴露到window对象，方便其他组件访问
        Reflect.set(window, 'catalogInstance', catalogInstance)
      }

      // 初始化右侧工具栏组件（使用RightTools组件）
      try {
        const rightToolsContainer = document.querySelector('.right-tools-container')


        if (rightToolsContainer) {
          // 确保容器有可见的样式
          rightToolsContainer.setAttribute('style', 'display:block !important; width:300px !important; background:#fff; padding:3px; position:fixed; right:0; top:60px; bottom:30px; z-index:1500; overflow:hidden !important;')

          rightToolsInstance = new RightTools(instance)
          const toolElement = rightToolsInstance.getElement()

          // 确保工具栏元素可见和样式正确
          setupRightToolsUI(toolElement)

          rightToolsContainer.appendChild(toolElement)
          // 显示右侧工具栏
          rightToolsInstance.show()

          // 将rightToolsInstance暴露到window对象，方便其他组件访问
          Reflect.set(window, 'rightToolsInstance', rightToolsInstance)

          // 延迟激活标签页
          setTimeout(() => {
            if (rightToolsInstance) {
              rightToolsInstance.activateTab('typography')
            }
          }, 500)
        } else {
          console.error('右侧工具栏容器未找到')
        }
      } catch (error) {
        console.error('初始化右侧工具栏组件失败:', error)
      }

      // 初始化事件监听和其他功能
      initEvents(instance)
    })
    .catch(error => {
      console.error('加载HTML模板失败:', error)
      // 如果加载失败，仍然初始化事件
      initEvents(instance)
    })

  // 在页面加载完成后确保右侧工具栏显示（防止可能的隐藏问题）
  window.addEventListener('load', () => {
    // 强制设置容器样式
    const container = document.querySelector('.right-tools-container')
    if (container) {
      container.setAttribute('style', 'display:block !important; width:300px !important; background:#fff; padding:3px; position:fixed; right:0; top:50px; bottom:30px; z-index:1500; overflow:hidden !important;')
    } else {
      console.error('未找到右侧工具栏容器')
    }

    // 使用全局实例显示工具栏
    setTimeout(() => {
      const rightToolsInstance = (window as any).rightToolsInstance
      if (rightToolsInstance) {
        // 获取工具栏DOM元素
        const toolElement = rightToolsInstance.getElement()
        if (toolElement) {
          setupRightToolsUI(toolElement)
        }

        // 最后显示工具栏
        rightToolsInstance.show()

        // 延迟一段时间后再次激活标签页
        setTimeout(() => {
          if (rightToolsInstance) {
            rightToolsInstance.activateTab('typography')
          }
        }, 500)
      } else {
        console.error('未找到右侧工具栏实例')
      }
    }, 1000) // 延迟1秒执行，确保其他初始化已完成
  })
}

/**
 * 设置右侧工具栏的UI样式
 * @param toolElement 工具栏DOM元素
 */
function setupRightToolsUI(toolElement: HTMLElement): void {
  // 强制显示所有元素
  toolElement.querySelectorAll('*').forEach((el: Element) => {
    if ((el as HTMLElement).style) {
      (el as HTMLElement).style.display = el.tagName === 'DIV' ? 'flex' : 'inline-block'
    }
  })

  // 强制设置样式 - TabControl风格
  toolElement.style.cssText = 'display:flex !important; flex-direction:column !important; width:100% !important; height:100% !important; overflow:hidden !important;'

  // 设置header样式
  const header = toolElement.querySelector('.right-tools__header')
  if (header) {
    (header as HTMLElement).style.cssText = 'height:38px; min-height:38px; display:flex !important; align-items:center; justify-content:space-between; border-bottom:1px solid #e2e6ed; padding:0 20px; background:#f5f7fa; box-sizing:border-box;'

    const headerTitle = header.querySelector('span')
    if (headerTitle) {
      (headerTitle as HTMLElement).style.cssText = 'color:#3d4757; font-size:14px; font-weight:bold; display:inline-block;'
    }

    const closeBtn = header.querySelector('.right-tools__header__close')
    if (closeBtn) {
      (closeBtn as HTMLElement).style.cssText = 'width:24px; height:24px; display:flex; align-items:center; justify-content:center; cursor:pointer;'
    }
  } else {
    console.error('未找到header元素')
  }

  // 设置tab样式 - 水平排列
  const tabsContainer = toolElement.querySelector('.right-tools__tabs')
  if (tabsContainer) {
    (tabsContainer as HTMLElement).style.cssText = 'display:flex !important; flex-direction:row !important; background:#f5f7fa; border-bottom:1px solid #e2e6ed; height:40px; min-height:40px; width:100%; flex-shrink:0;'

    // 设置所有标签
    const tabs = tabsContainer.querySelectorAll('.right-tools__tab')

    tabs.forEach((tab: Element) => {
      (tab as HTMLElement).style.cssText = 'display:flex !important; flex-direction:row !important; align-items:center; justify-content:center; padding:0 15px; height:40px; background:#f0f0f0; border-right:1px solid #e2e6ed; border-bottom:1px solid #e2e6ed; margin-bottom:-1px; flex:1;'

      // 调整图标样式
      const img = tab.querySelector('img')
      if (img) {
        (img as HTMLElement).style.cssText = 'width:18px; height:18px; margin-right:5px; display:inline-block !important;'
      }

      // 调整文字样式
      const span = tab.querySelector('span')
      if (span) {
        (span as HTMLElement).style.cssText = 'font-size:12px; color:#606266; display:inline-block !important; white-space:nowrap;'
      }
    })

    // 设置活动标签样式
    const activeTab = tabsContainer.querySelector('.right-tools__tab.active')
    if (activeTab) {
      (activeTab as HTMLElement).style.cssText = 'display:flex !important; flex-direction:row !important; align-items:center; justify-content:center; padding:0 5px; height:40px; background:#fff !important; border-right:1px solid #e2e6ed; border-bottom:1px solid #fff !important; margin-bottom:-1px; z-index:1;'

      // 设置活动标签文字样式
      const activeSpan = activeTab.querySelector('span')
      if (activeSpan) {
        (activeSpan as HTMLElement).style.cssText = 'font-size:12px; color:#4991f2 !important; font-weight:bold !important; display:inline-block !important;'
      }
    }
  } else {
    console.error('未找到tabs容器')
  }

  // 设置内容区样式
  const contentsContainer = toolElement.querySelector('.right-tools__contents')
  if (contentsContainer) {
    (contentsContainer as HTMLElement).style.cssText = 'flex:1; position:relative; overflow:hidden; background:#fff; width:100%; height:calc(100% - 78px);'

    // 设置所有内容区域
    const contents = contentsContainer.querySelectorAll('.right-tools__content')

    contents.forEach((content: Element) => {
      (content as HTMLElement).style.cssText = 'position:absolute; top:0; left:0; width:100%; height:100%; padding:5px; box-sizing:border-box; overflow-y:auto; background:#fff;'

      // 设置占位区域
      const placeholder = content.querySelector('.right-tools__placeholder')
      if (placeholder) {
        (placeholder as HTMLElement).style.cssText = 'color:#909399; text-align:center; padding:5px; font-size:14px; border:1px dashed #dcdfe6; margin:20px 0; border-radius:4px; background:#fafafa; display:block !important;'
      }
    })

    // 显示活动内容区域
    const activeContent = contentsContainer.querySelector('.right-tools__content.active')
    if (activeContent) {
      (activeContent as HTMLElement).style.display = 'block'
    }
  } else {
    console.error('未找到contents容器')
  }


}

/**
 * 初始化事件监听和功能注册
 * @param instance 编辑器实例
 */
export function initEvents(instance: Editor): void {
  // 更新目录的函数，现在使用Catalog组件
  async function updateCatalog() {
    if (catalogInstance) {
      await catalogInstance.updateCatalog()
    }
  }

  function toggleFullscreen() {
    const container = document.querySelector<HTMLDivElement>('.editor')!
    if (!document.fullscreenElement) {
      container.requestFullscreen().catch(err => {
        console.log(`Error attempting to enable full-screen mode: ${err.message}`)
      })
    } else {
      document.exitFullscreen()
    }
  }

  // 显示评论面板的函数
  function showCommentPanel() {
    const comment = document.querySelector<HTMLDivElement>('.comment')!
    comment.style.display = 'block'
    comment.style.opacity = '1'

  }

  // 隐藏评论面板的函数
  function hideCommentPanel() {
    const comment = document.querySelector<HTMLDivElement>('.comment')!
    comment.style.display = 'none'

  }

  // 将显示函数暴露到全局，方便其他组件调用
  Reflect.set(window, 'showCommentPanel', showCommentPanel)
  Reflect.set(window, 'hideCommentPanel', hideCommentPanel)

  async function updateComment() {
    // 评论
    const comment = document.querySelector<HTMLDivElement>('.comment')!
    comment.innerHTML = ''

    // 创建标题栏
    const headerDom = document.createElement('div')
    headerDom.classList.add('comment__header')

    const titleSpan = document.createElement('span')
    titleSpan.innerText = '批注'

    const closeDom = document.createElement('div')
    closeDom.classList.add('comment__header__close')
    closeDom.title = '关闭'

    const closeIcon = document.createElement('i')
    closeDom.appendChild(closeIcon)

    // 绑定关闭事件
    closeDom.addEventListener('click', () => {
      hideCommentPanel()
    })

    headerDom.appendChild(titleSpan)
    headerDom.appendChild(closeDom)
    comment.appendChild(headerDom)

    // 创建主体区域
    const mainDom = document.createElement('div')
    mainDom.classList.add('comment__main')

    if (commentList.length) {
      // 修改：批注面板默认不显示，即使有批注内容
      // comment.style.opacity = '1'
      // comment.style.display = 'block'

      // 创建批注项
      for (let c = 0; c < commentList.length; c++) {
        const item = commentList[c] as IComment
        const commentItemDom = document.createElement('div')
        commentItemDom.classList.add('comment-item')
        commentItemDom.dataset.id = `${item.id}`
        if (item.active) {
          commentItemDom.classList.add('active')
        }
        const titleDom = document.createElement('div')
        titleDom.classList.add('comment-item__title')
        const titleNameDom = document.createElement('span')
        titleNameDom.innerText = item.userName
        const titleTimeDom = document.createElement('span')
        titleTimeDom.innerText = item.createdDate
        titleDom.append(titleNameDom)
        titleDom.append(titleTimeDom)
        if (item.isEdit) {
          const editDom = document.createElement('i')
          editDom.innerHTML = '✎'
          editDom.title = '编辑'
          editDom.addEventListener('click', () => {
            // 修改
            new Dialog({
              title: '编辑批注',
              data: [
                {
                  type: 'textarea',
                  name: 'content',
                  required: true,
                  value: item.content,
                  placeholder: '请输入批注内容'
                }
              ],
              onConfirm: payload => {
                const newContent = payload.find(p => p.name === 'content')?.value
                if (!newContent) return
                item.content = newContent
                titleContentDom.innerText = newContent
              }
            })
          })
          titleDom.append(editDom)
          // 删除
          const deleteDom = document.createElement('i')
          deleteDom.innerHTML = '×'
          deleteDom.title = '删除'
          deleteDom.addEventListener('click', () => {
            console.log('删除')
            const index = commentList.findIndex(c => c.id === item.id)
            if (~index) {
              commentList.splice(index, 1)
              updateComment()
            }
          })
          titleDom.append(deleteDom)
        }
        commentItemDom.append(titleDom)
        const infoDom = document.createElement('div')
        infoDom.classList.add('comment-item__info')
        const infoUserDom = document.createElement('span')
        infoUserDom.innerText = item.rangeText || '评论已删除'
        const infoModifyDom = document.createElement('span')
        infoModifyDom.innerText = `P${item.pageNo}`
        infoDom.append(infoUserDom)
        infoDom.append(infoModifyDom)
        commentItemDom.append(infoDom)
        const titleContentDom = document.createElement('div')
        titleContentDom.classList.add('comment-item__content')
        titleContentDom.innerText = item.content
        commentItemDom.append(titleContentDom)
        commentItemDom.addEventListener('click', evt => {
          evt.stopPropagation()
          commentList.forEach(c => {
            (c as IComment).active = c.id === item.id
          })
          document.querySelectorAll('.comment-item').forEach(dom => {
            if ((dom as HTMLElement).dataset.id === `${item.id}`) {
              dom.classList.add('active')
            } else {
              dom.classList.remove('active')
            }
          })
          // 定位评论位置
          if (item.rangeText) {
            instance.command.executeLocationGroup(item.id)
          }
        })
        mainDom.append(commentItemDom)
      }
    } else {
      comment.style.opacity = '0'
      comment.style.display = 'none'
    }

    comment.appendChild(mainDom)
  }

  // 内容变化回调
  const handleContentChange = async function () {
    // 更新页面信息
    // 页数
    const pageNo = document.querySelector<HTMLSpanElement>('.page-no')!
    // 总页数
    const pageCount = document.querySelector<HTMLSpanElement>('.page-size')!
    // 字符数
    const wordCount = await instance.command.getWordCount()
    document.querySelector<HTMLSpanElement>('.word-count')!.innerText = `${wordCount}`
    // 可见页码
    const pageNoList = document.querySelector<HTMLSpanElement>('.page-no-list')!
    // 页面缩放
    const pageScale = document.querySelector<HTMLSpanElement>('.page-scale-percentage')!

    // 目录
    updateCatalog()

    // 更新评论
    updateComment()
  }

  // 注册内容变化事件
  instance.listener.contentChange = debounce(handleContentChange, 300)

  // 注册选择变化事件
  instance.listener.rangeStyleChange = function (payload: IRangeStyle) {
    // 更新订阅组件的状态即可，不需要手动操作DOM
  }

  // 注册可见页码变化事件
  instance.listener.visiblePageNoListChange = function (payload: number[]) {
    document.querySelector<HTMLSpanElement>('.page-no-list')!.innerText = payload.join('、')
  }

  // 注册页面大小变化事件
  instance.listener.pageSizeChange = function (payload: number) {
    document.querySelector<HTMLSpanElement>('.page-size')!.innerText = `${payload}`
  }

  // 注册页码变化事件
  instance.listener.intersectionPageNoChange = function (payload: number) {
    document.querySelector<HTMLSpanElement>('.page-no')!.innerText = `${payload + 1}`
  }

  // 注册页面缩放变化事件
  instance.listener.pageScaleChange = function (payload: number) {
    // 更新底部菜单的缩放百分比显示
    const footerPercentage = document.querySelector<HTMLSpanElement>('.page-scale-percentage')
    if (footerPercentage) {
      footerPercentage.innerText = `${Math.floor(payload * 100)}%`
    }

    // 更新布局菜单的缩放百分比显示
    const layoutPercentages = document.querySelectorAll<HTMLSpanElement>('.ribbon-group .page-scale-percentage')
    layoutPercentages.forEach(element => {
      element.innerText = `${Math.floor(payload * 100)}%`
    })

    // 更新布局菜单缩放按钮的状态
    const layoutMinusBtns = document.querySelectorAll('.ribbon-group .page-scale-minus')
    const layoutAddBtns = document.querySelectorAll('.ribbon-group .page-scale-add')

    layoutMinusBtns.forEach(btn => {
      if (payload <= 0.1) {
        btn.classList.add('disabled')
      } else {
        btn.classList.remove('disabled')
      }
    })

    layoutAddBtns.forEach(btn => {
      if (payload >= 5.0) {
        btn.classList.add('disabled')
      } else {
        btn.classList.remove('disabled')
      }
    })
  }

  // 初始化布局菜单页面缩放组件事件
  initLayoutPageScaleEvents(instance)

  // 默认执行一次
  handleContentChange()

  // 初始化评论
  updateComment()

  // 快捷键
  window.addEventListener('keydown', evt => {
    // 打印
    if (evt.ctrlKey && evt.key === 'p') {
      evt.preventDefault()
      instance.command.executePrint()
    }

    // 页面缩放快捷键
    // Ctrl+- 缩小
    if (evt.ctrlKey && evt.key === '-') {
      evt.preventDefault()
      instance.command.executePageScaleMinus()
    }

    // Ctrl+= 或 Ctrl++ 放大
    if (evt.ctrlKey && (evt.key === '=' || evt.key === '+')) {
      evt.preventDefault()
      instance.command.executePageScaleAdd()
    }

    // Ctrl+0 重置为100%
    if (evt.ctrlKey && evt.key === '0') {
      evt.preventDefault()
      instance.command.executePageScaleRecovery()
    }

    // LaTeX公式快捷键 Ctrl+L
    if (evt.ctrlKey && evt.key === 'l') {
      evt.preventDefault()
      console.log('触发LaTeX公式快捷键')
      // 打开LaTeX对话框
      new Dialog({
        title: 'LaTeX公式',
        data: [
          {
            type: 'textarea',
            height: 100,
            name: 'value',
            placeholder: '请输入LaTeX公式，例如：E = mc^2'
          }
        ],
        onConfirm: payload => {
          const value = payload.find(p => p.name === 'value')?.value
          if (!value) return
          console.log('插入LaTeX元素:', value)

          instance.command.executeInsertElementList([
            {
              type: ElementType.LATEX,
              value
            }
          ])
        }
      })
    }

    // 内容块快捷键 Ctrl+B
    if (evt.ctrlKey && evt.key === 'b') {
      evt.preventDefault()
      console.log('触发内容块快捷键')
      // 打开内容块对话框
      new Dialog({
        title: '内容块',
        data: [
          {
            type: 'select',
            label: '类型',
            name: 'type',
            value: 'iframe',
            required: true,
            options: [
              {
                label: '网址',
                value: 'iframe'
              },
              {
                label: '视频',
                value: 'video'
              }
            ]
          },
          {
            type: 'number',
            label: '宽度',
            name: 'width',
            placeholder: '请输入宽度（默认页面内宽度）'
          },
          {
            type: 'number',
            label: '高度',
            name: 'height',
            required: true,
            value: '300',
            placeholder: '请输入高度'
          },
          {
            type: 'input',
            label: '地址',
            name: 'src',
            required: false,
            placeholder: '请输入地址'
          },
          {
            type: 'textarea',
            label: 'HTML',
            height: 100,
            name: 'srcdoc',
            required: false,
            placeholder: '请输入HTML代码（仅网址类型有效）'
          }
        ],
        onConfirm: payload => {
          const type = payload.find(p => p.name === 'type')?.value
          if (!type) return

          const width = payload.find(p => p.name === 'width')?.value
          const height = payload.find(p => p.name === 'height')?.value
          if (!height) return

          // 地址或HTML代码至少存在一项
          const src = payload.find(p => p.name === 'src')?.value
          const srcdoc = payload.find(p => p.name === 'srcdoc')?.value

          const block: IBlock = {
            type: <BlockType>type
          }

          if (block.type === BlockType.IFRAME) {
            if (!src && !srcdoc) return
            block.iframeBlock = {
              src,
              srcdoc
            }
          } else if (block.type === BlockType.VIDEO) {
            if (!src) return
            block.videoBlock = {
              src
            }
          }

          const blockElement: IElement = {
            type: ElementType.BLOCK,
            value: '',
            height: Number(height),
            block
          }

          if (width) {
            blockElement.width = Number(width)
          }

          console.log('插入内容块元素:', blockElement)
          instance.command.executeInsertElementList([blockElement])
        }
      })
    }
  })

  // 注册LaTeX渲染引擎
  instance.register.renderEngine((element, previewElement) => {
    if (element.type === ElementType.LATEX) {
      console.log('LaTeX渲染尝试')
      try {
        // 改为使用预加载方式处理
        import('katex').then(katex => {
          console.log('KaTeX库加载成功')
          katex.default.render(element.value, previewElement, {
            throwOnError: false
          })
        }).catch(error => {
          console.error('KaTeX库加载失败:', error)
          // 在预览元素中显示错误信息
          previewElement.textContent = `LaTeX渲染失败: ${error.message || '未知错误'}`
        })
        return true
      } catch (error) {
        console.error('LaTeX渲染异常:', error)
        // 在预览元素中显示错误信息
        previewElement.textContent = `LaTeX渲染失败: ${error.message || '未知错误'}`
        return true
      }
    }
    if (
      element.type === ElementType.BLOCK &&
      element.block?.type === BlockType.IFRAME
    ) {
      console.log('IFRAME', element)
      if (!element.block.iframeBlock) return
      // iframe示例
      if (element.block.iframeBlock.src?.includes('哔哩哔哩')) {
        const iframeDom = document.createElement('iframe')
        iframeDom.src = element.block.iframeBlock.src
        iframeDom.allowFullscreen = true
        iframeDom.style.border = '0'
        iframeDom.style.width = `100%`
        iframeDom.style.height = `${element.height}px`
        previewElement.append(iframeDom)
        return true
      }
    }
    return false
  })

  // 注册控件回调
  instance.register.controlChange((payload, control) => {
    // 监听控件变化
    console.log('---controlChange---', control.type, payload)
    if (control.type === ControlType.SELECT) {
      const item = control.select?.options.find(o => o.value === payload)
      if (item) {
        console.log('---SELECT---', item)
      }
    }
  })

  // 注册控件渲染器
  instance.register.controlComponentRender(
    (
      controlComponent,
      control,
      controlElement,
      elementList,
      editor,
      pageContainer
    ) => {
      console.log(
        '---controlComponentRender---',
        controlComponent,
        control,
        controlElement
      )
      return false
    }
  )

  // 编辑模式
  instance.register.editorModeChange(mode => {
    console.log('---editorModeChange---', mode)
    document.querySelector<HTMLDivElement>('.editor-mode')!.innerText =
      mode === EditorMode.EDIT
        ? '编辑模式'
        : mode === EditorMode.CLEAN
        ? '清洁模式'
        : mode === EditorMode.READONLY
        ? '只读模式'
        : mode === EditorMode.FORM
        ? '表单模式'
        : '设计模式'
  })

  instance.listener.pasteEvent = function (event) {
    console.log('---pasteEvent---', event)
    return Promise.resolve()
  }

  instance.listener.afterLoadWatermark = function () {
    console.log('---afterLoadWatermark---')
  }

  instance.register.editorCallback({
    afterSetValue() {
      console.log('---afterSetValue---')
    },
    afterSetHTML() {
      console.log('---afterSetHTML---')
    }
  })

  // 画布选择区域变化回调
  instance.listener.selectEnd = async function (payload) {
    console.log('---selectEnd---', payload)
    if (payload && payload.pageNo) {
      const { pageNo, startIndex, endIndex } = payload
      // 查找纸张元素
      const activePage = await instance.command.getPageElementList(pageNo)
      if (!activePage?.length) return
      // 找到需要搜索的范围
      const rangeElementList = activePage.slice(startIndex, endIndex + 1)
      if (!rangeElementList.length) return
      // 设置评论
      new Dialog({
        title: '插入批注',
        data: [
          {
            type: 'textarea',
            name: 'content',
            required: true,
            placeholder: '请输入批注内容'
          }
        ],
        onConfirm(payload) {
          const content = payload.find(p => p.name === 'content')?.value
          if (!content) return
          // 插入评论
          const id = `${new Date().getTime()}`
          const rangeText = rangeElementList
            .filter(r => r.value)
            .map(r => r.value)
            .join('')
          commentList.push({
            id,
            isEdit: true,
            pageNo,
            userName: '张三',
            rangeText,
            content,
            startIndex,
            endIndex,
            createdDate: '2023-04-28'
          } as IComment)
          updateComment()
        }
      })
    }
  }

  // 按页面大小生成代码
  instance.register.codeTransform((prismCode, language) => {
    return formatPrismToken(prism.tokenize(prismCode, prism.languages[language]))
  })

  // 批注签名
  instance.register.contextMenuProvider((point, element, elementList, event) => {
    if (element) {
      return [
        {
          key: 'signature',
          text: '签名',
          onclick() {
            const contentElement = document.createElement('div')
            contentElement.style.width = '420px'
            contentElement.style.height = '200px'
            const signatureInstance = new Signature(contentElement)
            new Dialog({
              title: '签名',
              contentElement,
              onConfirm: () => {
                // 这里需要根据elementList指针位置追加图片
                if (signatureInstance.isEmpty()) return
                const imageElement: IElement = {
                  type: ElementType.IMAGE,
                  value: '',
                  height: 100,
                  id: `${Date.now()}`,
                  imgValue: signatureInstance.toDataURL()
                }
                // 签名图片无需添加边框，不能调整大小
                instance.command.executeInsertElementList([imageElement])
              }
            })
          }
        }
      ]
    }
    return []
  })

  // 注册工具
  instance.register.wordTool(editor => {
    return {
      callback: (word: string) => {
        console.log('---word-tool---', word)
        // 如果是电话号码或邮箱、网址等，可以在这里处理
        return Promise.resolve([
          {
            text: `复制 ${word}`,
            onClick: () => {
              navigator.clipboard.writeText(word)
            }
          },
          {
            text: '百度搜索',
            onClick: () => {
              window.open(`https://www.baidu.com/s?wd=${word}`)
            }
          }
        ])
      }
    }
  })
}

/**
 * 暴露获取编辑器实例的方法
 * @returns 返回当前编辑器实例
 */
export function getEditorInstance(): Editor | null {
  return editorInstance
}

/**
 * 暴露获取目录实例的方法
 * @returns 返回当前目录实例
 */
export function getCatalogInstance(): Catalog | null {
  return catalogInstance
}

/**
 * 初始化布局菜单页面缩放组件事件
 * @param instance 编辑器实例
 */
function initLayoutPageScaleEvents(instance: Editor): void {


  // 缩小按钮事件
  const minusButtons = document.querySelectorAll('.ribbon-group .page-scale-minus')
  minusButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      e.preventDefault()
      e.stopPropagation()



      // 执行页面缩小命令
      instance.command.executePageScaleMinus()
    })
  })

  // 放大按钮事件
  const addButtons = document.querySelectorAll('.ribbon-group .page-scale-add')
  addButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      e.preventDefault()
      e.stopPropagation()



      // 执行页面放大命令
      instance.command.executePageScaleAdd()
    })
  })

  // 百分比显示区域点击重置事件
  const percentageElements = document.querySelectorAll('.ribbon-group .page-scale-percentage')
  percentageElements.forEach(element => {
    element.addEventListener('click', (e) => {
      e.preventDefault()
      e.stopPropagation()



      // 执行页面缩放恢复命令（恢复到100%）
      instance.command.executePageScaleRecovery()
    })
  })

  // 注册页面缩放变化事件
  instance.listener.pageScaleChange = function (payload: number) {
    document.querySelector<HTMLSpanElement>('.page-scale-percentage')!.innerText = `${Math.floor(payload * 100)}%`
  }
}

/**
 * 初始化API集成
 * @param instance 编辑器实例
 */
async function initApiIntegration(instance: Editor): Promise<void> {
  try {
    console.log('🚀 开始初始化API集成...')

    // 初始化API模块
    await initApiModule({
      enableDebug: true
    })

    // 创建编辑器API集成实例
    editorApiIntegration = createEditorApiIntegration(instance)

    // 将API集成实例暴露到window对象，方便调试和其他组件访问
    Reflect.set(window, 'editorApiIntegration', editorApiIntegration)

    // 初始化API测试面板（仅在开发环境）
    if (process.env.NODE_ENV === 'development') {
      const apiTestPanel = getApiTestPanel()
      Reflect.set(window, 'apiTestPanel', apiTestPanel)
      console.log('💡 API测试面板已初始化，可在控制台调用 showApiTestPanel() 打开')
    }

    console.log('✅ API集成初始化完成')

    // 可选：加载默认文档或执行其他初始化操作
    // await loadDefaultDocument()

  } catch (error) {
    console.error('❌ API集成初始化失败:', error)
  }
}

/**
 * 加载默认文档（可选）
 */
async function loadDefaultDocument(): Promise<void> {
  try {
    // 这里可以加载用户上次编辑的文档或默认文档
    // const lastDocumentId = localStorage.getItem('lastDocumentId')
    // if (lastDocumentId && editorApiIntegration) {
    //   await editorApiIntegration.loadDocument(lastDocumentId)
    // }
  } catch (error) {
    console.error('加载默认文档失败:', error)
  }
}