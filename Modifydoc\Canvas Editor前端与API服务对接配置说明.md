# Canvas Editor 前端与API服务对接配置说明

## 🎯 配置概述

本文档详细说明了如何配置Canvas Editor前端与Django API服务的对接，实现前后端数据交互功能。

## 📁 项目结构

### 新增的API模块结构
```
fontend/src/api/
├── config.ts              # API配置文件
├── http-client.ts          # HTTP客户端封装
├── services.ts             # API服务类
├── types.ts                # TypeScript类型定义
├── utils.ts                # API工具函数
└── index.ts                # API模块入口

fontend/src/components/api/
├── EditorApiIntegration.ts # 编辑器API集成组件
└── ApiTestPanel.ts         # API测试面板
```

## 🔧 配置详情

### 1. API基础配置

**文件**: `fontend/src/api/config.ts`

```typescript
// 环境配置
export const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production'
} as const

// API基础配置
export const API_CONFIG = {
  // 开发环境API地址（使用Vite代理）
  DEVELOPMENT: {
    BASE_URL: '', // 空字符串表示使用当前域名，通过Vite代理转发
    API_PREFIX: '/api'
  },
  // 生产环境API地址
  PRODUCTION: {
    BASE_URL: 'https://your-production-domain.com',
    API_PREFIX: '/api'
  }
} as const
```

**主要功能**：
- ✅ 环境区分配置
- ✅ API端点定义
- ✅ HTTP请求配置
- ✅ 错误码映射

### 2. Vite开发服务器代理配置

**文件**: `fontend/vite.config.ts`

```typescript
server: {
  host: '0.0.0.0',
  port: 3000,
  // 配置API代理，将前端API请求代理到Django后端
  proxy: {
    '/api': {
      target: 'http://127.0.0.1:8000',
      changeOrigin: true,
      secure: false,
      configure: (proxy, _options) => {
        proxy.on('error', (err, _req, _res) => {
          console.log('proxy error', err)
        })
        proxy.on('proxyReq', (proxyReq, req, _res) => {
          console.log('Sending Request to the Target:', req.method, req.url)
        })
        proxy.on('proxyRes', (proxyRes, req, _res) => {
          console.log('Received Response from the Target:', proxyRes.statusCode, req.url)
        })
      }
    }
  }
}
```

**代理功能**：
- ✅ 将前端 `/api` 请求代理到 Django 后端
- ✅ 解决跨域问题
- ✅ 请求日志记录
- ✅ 错误处理

### 3. HTTP客户端封装

**文件**: `fontend/src/api/http-client.ts`

**主要功能**：
- ✅ 统一的HTTP请求接口
- ✅ 请求/响应拦截器
- ✅ 错误处理和重试机制
- ✅ 超时控制
- ✅ 认证令牌管理

**使用示例**：
```typescript
import { httpClient } from './http-client'

// GET请求
const response = await httpClient.get('/health/')

// POST请求
const document = await httpClient.post('/documents/', {
  title: '新文档',
  content: { main: [] }
})

// 文件上传
const uploadResult = await httpClient.upload('/upload/image/', file)
```

### 4. API服务类

**文件**: `fontend/src/api/services.ts`

**提供的服务**：
- ✅ **HealthService**: 健康检查
- ✅ **DocumentService**: 文档管理
- ✅ **UploadService**: 文件上传

**使用示例**：
```typescript
import { ApiService, DocumentService } from '../api'

// 健康检查
const health = await ApiService.health.check()

// 获取文档列表
const documents = await DocumentService.getDocuments({
  page: 1,
  page_size: 10
})

// 保存文档
const savedDoc = await DocumentService.saveDocument({
  title: '我的文档',
  content: editorData
})
```

### 5. 编辑器API集成

**文件**: `fontend/src/components/api/EditorApiIntegration.ts`

**集成功能**：
- ✅ 自动保存文档
- ✅ 加载文档内容
- ✅ 图片上传集成
- ✅ 内容变化监听
- ✅ 错误处理和用户提示

**使用方式**：
```typescript
// 在编辑器初始化时自动创建
const apiIntegration = createEditorApiIntegration(editorInstance)

// 加载文档
await apiIntegration.loadDocument(documentId)

// 保存当前文档
await apiIntegration.saveCurrentDocument()

// 创建新文档
await apiIntegration.createNewDocument('新文档标题')
```

## 🚀 启动和使用

### 1. 启动后端服务

```bash
cd backend
.venv\Scripts\activate  # Windows
# 或 source .venv/bin/activate  # Linux/Mac
python manage.py runserver 8000
```

### 2. 启动前端服务

```bash
cd fontend
npm run dev
```

前端服务将在 `http://localhost:3000` 启动，API请求会自动代理到后端。

### 3. API测试

在浏览器控制台中可以使用以下命令测试API：

```javascript
// 显示API测试面板
showApiTestPanel()

// 直接调用API
const health = await window.editorApiIntegration.testApiConnection()
console.log(health)

// 保存当前文档
const doc = await window.editorApiIntegration.saveCurrentDocument()
console.log(doc)
```

## 🔍 调试和监控

### 1. 开发环境日志

API模块在开发环境下会输出详细的日志：
- ✅ 请求日志：显示所有API请求
- ✅ 响应日志：显示API响应数据
- ✅ 错误日志：显示详细的错误信息

### 2. API测试面板

开发环境下提供了可视化的API测试面板：
- ✅ 健康检查测试
- ✅ 文档管理测试
- ✅ 连接状态测试
- ✅ 实时结果显示

### 3. 浏览器控制台

所有API相关的实例都暴露到了 `window` 对象：
```javascript
window.editorApiIntegration  // 编辑器API集成实例
window.apiTestPanel         // API测试面板实例
window.showApiTestPanel     // 显示测试面板函数
```

## 📋 API端点说明

### 后端提供的API端点

| 端点 | 方法 | 说明 |
|------|------|------|
| `/api/health/` | GET | 健康检查 |
| `/api/documents/` | GET | 获取文档列表 |
| `/api/documents/` | POST | 创建新文档 |
| `/api/documents/{id}/` | GET | 获取单个文档 |
| `/api/documents/{id}/` | PATCH | 更新文档 |
| `/api/documents/{id}/` | DELETE | 删除文档 |
| `/api/documents/{id}/versions/` | GET | 获取文档版本 |
| `/api/documents/{id}/versions/` | POST | 创建文档版本 |

### 前端API调用示例

```typescript
// 健康检查
GET /api/health/
Response: {
  "status": "healthy",
  "timestamp": "2025-06-15T16:40:00Z",
  "version": "1.0.0",
  "database": "sqlite",
  "debug": true
}

// 获取文档列表
GET /api/documents/?page=1&page_size=10
Response: {
  "count": 5,
  "next": null,
  "previous": null,
  "results": [...]
}

// 创建文档
POST /api/documents/
Body: {
  "title": "新文档",
  "content": { "main": [...] },
  "is_public": false
}
```

## ⚙️ 配置选项

### 1. 自动保存配置

```typescript
// 设置自动保存间隔（毫秒）
editorApiIntegration.setAutoSaveInterval(30000) // 30秒

// 启用/禁用自动保存
editorApiIntegration.startAutoSave()
editorApiIntegration.stopAutoSave()
```

### 2. 认证配置

```typescript
// 设置认证令牌
ApiService.setAuthToken('your-jwt-token')

// 移除认证令牌
ApiService.removeAuthToken()
```

### 3. 请求超时配置

```typescript
// 在 config.ts 中修改
export const HTTP_CONFIG = {
  TIMEOUT: 30000, // 30秒超时
  // ...
}
```

## 🔒 安全考虑

### 1. CORS配置

后端已配置CORS允许前端域名访问：
```python
# backend/book_editor_backend/settings.py
CORS_ALLOWED_ORIGINS = [
    'http://localhost:3000',
    'http://127.0.0.1:3000'
]
```

### 2. 认证和授权

- ✅ 支持JWT令牌认证
- ✅ 会话认证支持
- ✅ 权限控制集成

### 3. 数据验证

- ✅ 前端数据类型检查
- ✅ 后端数据验证
- ✅ 错误处理机制

## 🚨 常见问题

### 1. API连接失败

**问题**: 前端无法连接到后端API
**解决方案**:
1. 确保后端服务在 `http://127.0.0.1:8000` 运行
2. 检查Vite代理配置
3. 查看浏览器控制台错误信息

### 2. 跨域问题

**问题**: CORS错误
**解决方案**:
1. 确保后端CORS配置正确
2. 检查前端请求域名
3. 使用Vite代理避免跨域

### 3. 自动保存失败

**问题**: 文档自动保存不工作
**解决方案**:
1. 检查API连接状态
2. 查看控制台错误日志
3. 确保文档ID正确设置

## ✅ 配置完成验证

配置完成后，可以通过以下方式验证：

1. **后端服务检查**:
   - 访问 `http://127.0.0.1:8000/api/health/` 应返回健康状态

2. **前端代理检查**:
   - 在浏览器控制台运行 `showApiTestPanel()`
   - 点击"测试API连接"按钮

3. **集成功能检查**:
   - 编辑器内容变化时应触发自动保存
   - 控制台应显示API相关日志

现在Canvas Editor前端已成功与Django API服务对接，支持文档的创建、保存、加载等完整功能！🎉
