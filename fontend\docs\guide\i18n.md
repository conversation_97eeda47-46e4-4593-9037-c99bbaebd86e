# 国际化

## 使用方式

```javascript
import Editor from "@hufe921/canvas-editor"

const instance = new Editor(container, <IElement[]>data, options)

// 注册
instance.register.langMap(locale: string, lang: ILang)

// 设置
instance.command.executeSetLocale(locale)
```

## ILang

```typescript
interface ILang {
  contextmenu: {
    global: {
      cut: string
      copy: string
      paste: string
      selectAll: string
      print: string
    }
    control: {
      delete: string
    }
    hyperlink: {
      delete: string
      cancel: string
      edit: string
    }
    image: {
      change: string
      saveAs: string
      textWrap: string
      textWrapType: {
        embed: string
        upDown: string
        surround: string
        floatTop: string
        floatBottom: string
    }
    table: {
      insertRowCol: string
      insertTopRow: string
      insertBottomRow: string
      insertLeftCol: string
      insertRightCol: string
      deleteRowCol: string
      deleteRow: string
      deleteCol: string
      deleteTable: string
      mergeCell: string
      mergeCancelCell: string
      verticalAlign: string
      verticalAlignTop: string
      verticalAlignMiddle: string
      verticalAlignBottom: string
      border: string
      borderAll: string
      borderEmpty: string
      borderDash: string
      borderExternal: string
      borderInternal: string
      borderTd: string
      borderTdTop: string
      borderTdRight: string
      borderTdBottom: string
      borderTdLeft: string
      borderTdForward: string
      borderTdBack: string
    }
  }
  datePicker: {
    now: string
    confirm: string
    return: string
    timeSelect: string
    weeks: {
      sun: string
      mon: string
      tue: string
      wed: string
      thu: string
      fri: string
      sat: string
    }
    year: string
    month: string
    hour: string
    minute: string
    second: string
  }
  frame: {
    header: string
    footer: string
  }
  pageBreak: {
    displayName: string
  }
  zone: {
    headerTip: string
    footerTip: string
  }
}
```
