import './UndoButton.css'

export class UndoButton {
  private element: HTMLDivElement;
  private command: any;
  private isApple: boolean;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    this.isApple = typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent)
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.undo-button') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="undo-button" title="撤销(${this.isApple ? '⌘' : 'Ctrl'}+Z)">
      <i></i>
    </div>`
  }

  private bindEvents(): void {
    this.element.onclick = () => {
      console.log('undo')
      this.command.executeUndo()
    }
  }

  // 更新按钮状态
  public updateState(canUndo: boolean): void {
    if (canUndo) {
      this.element.classList.remove('no-allow')
    } else {
      this.element.classList.add('no-allow')
    }
  }
} 