import { ITitleOption, ITitleSizeOption } from '../../interface/Title'
import { TitleLevel } from '../enum/Title'
import { fontStyleConfigManager } from '../../../stylesConfig/FontStyleConfigManager'

/**
 * 获取动态标题配置
 * 从配置文件中读取标题大小设置
 */
function getDynamicTitleOption(): Readonly<Required<ITitleOption>> {
  const sizeMapping = fontStyleConfigManager.getTitleSizeMapping()

  return {
    defaultFirstSize: sizeMapping[TitleLevel.FIRST],
    defaultSecondSize: sizeMapping[TitleLevel.SECOND],
    defaultThirdSize: sizeMapping[TitleLevel.THIRD],
    defaultFourthSize: sizeMapping[TitleLevel.FOURTH],
    defaultFifthSize: sizeMapping[TitleLevel.FIFTH],
    defaultSixthSize: sizeMapping[TitleLevel.SIXTH]
  }
}

/**
 * 默认标题配置选项
 * 现在从配置文件动态获取，保持向后兼容性
 */
export const defaultTitleOption: Readonly<Required<ITitleOption>> = getDynamicTitleOption()

/**
 * 获取最新的标题配置选项
 * 用于在配置更新后重新获取最新值
 */
export function getUpdatedTitleOption(): Readonly<Required<ITitleOption>> {
  return getDynamicTitleOption()
}

export const titleSizeMapping: Record<TitleLevel, keyof ITitleSizeOption> = {
  [TitleLevel.FIRST]: 'defaultFirstSize',
  [TitleLevel.SECOND]: 'defaultSecondSize',
  [TitleLevel.THIRD]: 'defaultThirdSize',
  [TitleLevel.FOURTH]: 'defaultFourthSize',
  [TitleLevel.FIFTH]: 'defaultFifthSize',
  [TitleLevel.SIXTH]: 'defaultSixthSize'
}

export const titleOrderNumberMapping: Record<TitleLevel, number> = {
  [TitleLevel.FIRST]: 1,
  [TitleLevel.SECOND]: 2,
  [TitleLevel.THIRD]: 3,
  [TitleLevel.FOURTH]: 4,
  [TitleLevel.FIFTH]: 5,
  [TitleLevel.SIXTH]: 6
}

export const titleNodeNameMapping: Record<string, TitleLevel> = {
  H1: TitleLevel.FIRST,
  H2: TitleLevel.SECOND,
  H3: TitleLevel.THIRD,
  H4: TitleLevel.FOURTH,
  H5: TitleLevel.FIFTH,
  H6: TitleLevel.SIXTH
}
