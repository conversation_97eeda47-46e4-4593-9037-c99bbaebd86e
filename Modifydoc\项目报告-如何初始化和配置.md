# Canvas Editor 初始化和配置完整指南

## 📋 项目概述

Canvas Editor 是一个基于 Canvas 的富文本编辑器，提供了完整的文档编辑功能。本报告详细介绍如何初始化和配置编辑器，以及各个组件的功能和接口。

## 🚀 快速开始

### 1. 安装依赖

```bash
# 使用 npm
npm install @hufe921/canvas-editor --save

# 使用 yarn
yarn add @hufe921/canvas-editor

# 使用 pnpm
pnpm add @hufe921/canvas-editor
```

### 2. 基础初始化

#### 最简单的初始化

```typescript
import Editor from '@hufe921/canvas-editor'

// 准备容器
const container = document.querySelector('.canvas-editor') as HTMLDivElement

// 基础数据
const data = [
  {
    value: 'Hello World'
  }
]

// 创建编辑器实例
const instance = new Editor(container, data, {})
```

#### 完整结构初始化

```typescript
import Editor, { IEditorData, IEditorOption } from '@hufe921/canvas-editor'

// 完整的文档数据结构
const data: IEditorData = {
  header: [
    {
      value: '文档标题',
      size: 18,
      bold: true,
      rowFlex: RowFlex.CENTER
    }
  ],
  main: [
    {
      value: '这是正文内容',
      size: 14
    },
    {
      value: '\n'
    },
    {
      value: '第二段内容',
      size: 14,
      color: '#333333'
    }
  ],
  footer: [
    {
      value: 'canvas-editor',
      size: 12,
      color: '#666666'
    }
  ]
}

// 编辑器配置选项
const options: IEditorOption = {
  mode: EditorMode.EDIT,
  defaultFont: 'Microsoft YaHei',
  defaultSize: 16,
  margins: [100, 120, 100, 120], // [上, 右, 下, 左]
  watermark: {
    data: 'Canvas Editor',
    size: 20,
    color: '#CCCCCC',
    opacity: 0.3
  }
}

// 创建编辑器实例
const instance = new Editor(container, data, options)
```

## ⚙️ 编辑器配置选项

### 1. 核心配置接口

```typescript
interface IEditorOption {
  // 编辑器模式
  mode?: EditorMode                    // 编辑模式：edit/readonly/print等
  
  // 默认样式
  defaultType?: string                 // 默认元素类型
  defaultColor?: string                // 默认文字颜色
  defaultFont?: string                 // 默认字体
  defaultSize?: number                 // 默认字号
  minSize?: number                     // 最小字号
  maxSize?: number                     // 最大字号
  
  // 页面设置
  width?: number                       // 页面宽度
  height?: number                      // 页面高度
  scale?: number                       // 缩放比例
  pageGap?: number                     // 页面间距
  margins?: IMargin                    // 页边距
  paperDirection?: PaperDirection      // 纸张方向
  pageMode?: PageMode                  // 页面模式
  
  // 视觉效果
  rangeColor?: string                  // 选区颜色
  rangeAlpha?: number                  // 选区透明度
  searchMatchColor?: string            // 搜索匹配颜色
  highlightAlpha?: number              // 高亮透明度
  
  // 功能配置
  historyMaxRecordCount?: number       // 历史记录最大数量
  contextMenuDisableKeys?: string[]    // 禁用的右键菜单项
  wordBreak?: WordBreak               // 换行规则
  
  // 组件配置
  header?: IHeader                     // 页眉配置
  footer?: IFooter                     // 页脚配置
  pageNumber?: IPageNumber             // 页码配置
  watermark?: IWatermark               // 水印配置
  control?: IControlOption             // 控件配置
  table?: ITableOption                 // 表格配置
}
```

### 2. 详细配置示例

```typescript
const advancedOptions: IEditorOption = {
  // 基础设置
  mode: EditorMode.EDIT,
  defaultFont: 'Microsoft YaHei',
  defaultSize: 16,
  defaultColor: '#000000',
  minSize: 8,
  maxSize: 72,
  
  // 页面设置
  width: 794,                          // A4纸宽度
  height: 1123,                        // A4纸高度
  scale: 1,
  pageGap: 20,
  margins: [100, 120, 100, 120],       // 页边距
  paperDirection: PaperDirection.VERTICAL,
  pageMode: PageMode.PAGING,
  
  // 选区和搜索
  rangeColor: '#AECBFA',
  rangeAlpha: 0.6,
  rangeMinWidth: 5,
  searchMatchColor: '#FFFF00',
  searchMatchAlpha: 0.6,
  searchNavigateMatchColor: '#AAD280',
  highlightAlpha: 0.6,
  
  // 下划线和删除线
  underlineColor: '#000000',
  strikeoutColor: '#FF0000',
  
  // 调整器
  resizerColor: '#4182D9',
  resizerSize: 5,
  
  // 历史记录
  historyMaxRecordCount: 100,
  
  // 换行规则
  wordBreak: WordBreak.BREAK_WORD,
  
  // 页眉配置
  header: {
    top: 30,                           // 页眉距离页面顶部距离
    maxHeightRadio: 0.2               // 页眉最大高度比例
  },
  
  // 页脚配置
  footer: {
    bottom: 30,                        // 页脚距离页面底部距离
    maxHeightRadio: 0.2               // 页脚最大高度比例
  },
  
  // 页码配置
  pageNumber: {
    format: '第{pageNo}页/共{pageCount}页',
    font: 'Microsoft YaHei',
    size: 12,
    color: '#666666',
    rowFlex: RowFlex.CENTER,
    numberType: NumberType.ARABIC
  },
  
  // 水印配置
  watermark: {
    data: 'Canvas Editor',
    font: 'Microsoft YaHei',
    size: 20,
    color: '#CCCCCC',
    opacity: 0.3,
    repeat: true,
    gap: [100, 100]                    // 水印间距
  },
  
  // 控件配置
  control: {
    placeholderColor: '#DCDFE6',
    bracketColor: '#AECBFA',
    prefix: '{',
    postfix: '}',
    backgroundColor: '#F5F7FA'
  },
  
  // 表格配置
  table: {
    borderColor: '#000000',
    borderWidth: 1,
    tdPadding: [5, 5, 5, 5]           // 单元格内边距
  },
  
  // 占位符
  placeholder: {
    data: '请输入内容...',
    font: 'Microsoft YaHei',
    size: 16,
    color: '#CCCCCC'
  },
  
  // 禁用的右键菜单项
  contextMenuDisableKeys: ['cut', 'copy'],
  
  // 遮罩边距（用于菜单栏等）
  maskMargin: [60, 0, 30, 0]          // [上, 右, 下, 左]
}

const instance = new Editor(container, data, advancedOptions)
```

## 🏗️ 编辑器架构和组件

### 1. 核心架构

```typescript
export class Editor {
  public command: Command              // 命令系统
  public listener: Listener            // 事件监听器
  public eventBus: EventBus           // 事件总线
  public register: Register           // 注册系统
  public destroy: Function            // 销毁方法
  public use: Function                // 插件使用方法
  public override: Override           // 方法重写

  constructor(
    container: HTMLDivElement,         // 容器元素
    data: IEditorData | IElement[],    // 文档数据
    options: IEditorOption = {}        // 配置选项
  ) {
    // 初始化各个系统...
  }
}
```

### 2. 主要组件系统

#### 命令系统 (Command)

```typescript
// 命令系统提供所有编辑操作的统一接口
const command = instance.command

// 基础操作
command.executeUndo()                  // 撤销
command.executeRedo()                  // 重做
command.executeCut()                   // 剪切
command.executeCopy()                  // 复制
command.executePaste()                 // 粘贴
command.executeSelectAll()             // 全选

// 格式化操作
command.executeBold()                  // 加粗
command.executeItalic()                // 斜体
command.executeUnderline()             // 下划线
command.executeStrikeout()             // 删除线
command.executeFont('Arial')           // 设置字体
command.executeSize(18)                // 设置字号
command.executeColor('#FF0000')        // 设置颜色

// 段落操作
command.executeRowFlex(RowFlex.CENTER) // 段落对齐
command.executeRowMargin(10)           // 行间距
command.executeTitle(TitleLevel.FIRST) // 设置标题

// 插入操作
command.executeInsertTable(3, 4)       // 插入表格
command.executeImage(imageData)        // 插入图片
command.executeHyperlink(url, text)    // 插入链接
command.executePageBreak()             // 插入分页符

// 模式切换
command.executeMode(EditorMode.READONLY) // 切换模式
```

#### 事件系统 (Listener & EventBus)

```typescript
// 事件监听器 - 监听编辑器状态变化
const listener = instance.listener

// 内容变化监听
listener.contentChange = () => {
  console.log('文档内容发生变化')
}

// 选区样式变化监听
listener.rangeStyleChange = (payload) => {
  console.log('选区样式变化:', payload)
  // payload 包含当前选区的样式信息
  // { bold: true, italic: false, size: 16, font: 'Arial' }
}

// 可见页面变化监听
listener.visiblePageNoListChange = (pageList) => {
  console.log('可见页面:', pageList)
}

// 页面大小变化监听
listener.pageSizeChange = (width, height) => {
  console.log('页面大小变化:', width, height)
}

// 事件总线 - 自定义事件通信
const eventBus = instance.eventBus

// 监听自定义事件
eventBus.on('customEvent', (data) => {
  console.log('收到自定义事件:', data)
})

// 触发自定义事件
eventBus.emit('customEvent', { message: 'Hello' })
```

#### 注册系统 (Register)

```typescript
// 注册系统 - 注册自定义功能
const register = instance.register

// 注册右键菜单
register.contextMenuList([
  {
    key: 'customMenu',
    name: '自定义菜单项',
    shortCut: 'Ctrl + M',
    when: (payload) => {
      return payload.editorHasSelection // 只在有选中内容时显示
    },
    callback: (command, context) => {
      console.log('执行自定义菜单操作')
      // 执行自定义逻辑
    }
  }
])

// 注册快捷键
register.shortcutList([
  {
    key: 'S',
    ctrl: true,
    shift: true,
    callback: (command) => {
      console.log('执行自定义保存')
      // 执行自定义保存逻辑
    }
  }
])

// 注册控件变化回调
register.controlChange((payload, control) => {
  console.log('控件值变化:', control.type, payload)
})

// 注册语言包
register.langMap('zh-CN', {
  contextmenu: {
    custom: {
      menu: '自定义菜单'
    }
  }
})
```

### 3. 编辑器模式

```typescript
// 编辑器支持多种模式
enum EditorMode {
  EDIT = 'edit',           // 编辑模式（默认）
  CLEAN = 'clean',         // 清洁模式（隐藏辅助元素）
  READONLY = 'readonly',   // 只读模式
  FORM = 'form',          // 表单模式（仅控件可编辑）
  PRINT = 'print',        // 打印模式
  DESIGN = 'design'       // 设计模式
}

// 切换模式
instance.command.executeMode(EditorMode.READONLY)

// 获取当前模式
const currentMode = instance.command.getOptions().mode
```

### 4. 页面模式

```typescript
// 页面显示模式
enum PageMode {
  PAGING = 'paging',       // 分页模式（默认）
  CONTINUITY = 'continuity' // 连续页模式
}

// 纸张方向
enum PaperDirection {
  VERTICAL = 'vertical',    // 纵向（默认）
  HORIZONTAL = 'horizontal' // 横向
}

// 设置页面模式
instance.command.executePageMode(PageMode.CONTINUITY)

// 设置纸张方向
instance.command.executePaperDirection(PaperDirection.HORIZONTAL)
```

## 🎨 UI组件系统

### 1. 菜单组件

```typescript
// 菜单按钮基础类
export class MenuButton {
  protected element: HTMLDivElement
  protected command: Command
  protected isApple: boolean

  constructor(container: HTMLElement, command: Command) {
    this.command = command
    this.isApple = /Mac OS X/.test(navigator.userAgent)
    this.render(container)
    this.bindEvents()
  }

  protected render(container: HTMLElement): void {
    // 渲染按钮HTML
  }

  protected bindEvents(): void {
    // 绑定事件处理
  }
}

// 加粗按钮示例
export class BoldButton extends MenuButton {
  protected render(container: HTMLElement): void {
    container.innerHTML = `
      <div class="bold-button" title="加粗(${this.isApple ? '⌘' : 'Ctrl'}+B)">
        <i class="bold-icon"></i>
      </div>
    `
    this.element = container.querySelector('.bold-button')!
  }

  protected bindEvents(): void {
    this.element.onclick = () => {
      this.command.executeBold()
    }
  }
}

// 使用菜单按钮
const boldButton = new BoldButton(menuContainer, instance.command)
```

### 2. 工具栏组件

```typescript
// 工具栏管理器
export class ToolbarManager {
  private container: HTMLElement
  private command: Command
  private buttons: Map<string, MenuButton>

  constructor(container: HTMLElement, command: Command) {
    this.container = container
    this.command = command
    this.buttons = new Map()
    this.initializeButtons()
  }

  private initializeButtons(): void {
    // 初始化所有工具栏按钮
    this.addButton('bold', new BoldButton(this.createButtonContainer(), this.command))
    this.addButton('italic', new ItalicButton(this.createButtonContainer(), this.command))
    this.addButton('underline', new UnderlineButton(this.createButtonContainer(), this.command))
    // ... 更多按钮
  }

  private addButton(key: string, button: MenuButton): void {
    this.buttons.set(key, button)
  }

  private createButtonContainer(): HTMLElement {
    const container = document.createElement('div')
    container.className = 'toolbar-button-container'
    this.container.appendChild(container)
    return container
  }

  public getButton(key: string): MenuButton | undefined {
    return this.buttons.get(key)
  }

  public updateButtonStates(styleInfo: any): void {
    // 根据当前样式更新按钮状态
    const boldButton = this.getButton('bold')
    if (boldButton && styleInfo.bold) {
      boldButton.element.classList.add('active')
    } else {
      boldButton?.element.classList.remove('active')
    }
  }
}
```

### 3. 状态栏组件

```typescript
// 状态栏组件
export class StatusBar {
  private container: HTMLElement
  private pageInfo: HTMLElement
  private wordCount: HTMLElement
  private scaleInfo: HTMLElement

  constructor(container: HTMLElement) {
    this.container = container
    this.render()
  }

  private render(): void {
    this.container.innerHTML = `
      <div class="status-bar">
        <div class="status-item page-info">页面: 1/1</div>
        <div class="status-item word-count">字数: 0</div>
        <div class="status-item scale-info">缩放: 100%</div>
      </div>
    `

    this.pageInfo = this.container.querySelector('.page-info')!
    this.wordCount = this.container.querySelector('.word-count')!
    this.scaleInfo = this.container.querySelector('.scale-info')!
  }

  public updatePageInfo(current: number, total: number): void {
    this.pageInfo.textContent = `页面: ${current}/${total}`
  }

  public updateWordCount(count: number): void {
    this.wordCount.textContent = `字数: ${count}`
  }

  public updateScale(scale: number): void {
    this.scaleInfo.textContent = `缩放: ${Math.round(scale * 100)}%`
  }
}

// 使用状态栏
const statusBar = new StatusBar(statusContainer)

// 监听编辑器变化并更新状态栏
instance.listener.contentChange = async () => {
  const wordCount = await instance.command.getWordCount()
  statusBar.updateWordCount(wordCount)
}

instance.listener.visiblePageNoListChange = (pageList) => {
  const currentPage = pageList[0] || 1
  const totalPages = instance.command.getPageCount()
  statusBar.updatePageInfo(currentPage, totalPages)
}
```

## 🔧 完整初始化示例

### 1. 基础初始化流程

```typescript
// 完整的编辑器初始化示例
export class EditorInitializer {
  private container: HTMLElement
  private instance: Editor | null = null
  private toolbarManager: ToolbarManager | null = null
  private statusBar: StatusBar | null = null

  constructor(containerId: string) {
    this.container = document.getElementById(containerId)!
    if (!this.container) {
      throw new Error(`容器元素 ${containerId} 不存在`)
    }
  }

  public async initialize(): Promise<Editor> {
    try {
      // 1. 准备数据和配置
      const data = this.prepareData()
      const options = this.prepareOptions()

      // 2. 创建编辑器实例
      this.instance = new Editor(
        this.container.querySelector('.editor-main')!,
        data,
        options
      )

      // 3. 初始化UI组件
      await this.initializeUI()

      // 4. 设置事件监听
      this.setupEventListeners()

      // 5. 注册自定义功能
      this.registerCustomFeatures()

      console.log('编辑器初始化完成')
      return this.instance

    } catch (error) {
      console.error('编辑器初始化失败:', error)
      throw error
    }
  }

  private prepareData(): IEditorData {
    return {
      header: [
        {
          value: '文档标题',
          size: 18,
          bold: true,
          rowFlex: RowFlex.CENTER
        }
      ],
      main: [
        {
          value: '请输入正文内容...',
          size: 14
        }
      ],
      footer: [
        {
          value: 'Canvas Editor',
          size: 12,
          color: '#666666'
        }
      ]
    }
  }

  private prepareOptions(): IEditorOption {
    return {
      mode: EditorMode.EDIT,
      defaultFont: 'Microsoft YaHei',
      defaultSize: 14,
      margins: [100, 120, 100, 120],
      watermark: {
        data: 'Canvas Editor',
        size: 20,
        color: '#CCCCCC',
        opacity: 0.3
      },
      pageNumber: {
        format: '第{pageNo}页/共{pageCount}页'
      }
    }
  }

  private async initializeUI(): Promise<void> {
    // 初始化工具栏
    const toolbarContainer = this.container.querySelector('.editor-toolbar')!
    this.toolbarManager = new ToolbarManager(toolbarContainer, this.instance!.command)

    // 初始化状态栏
    const statusContainer = this.container.querySelector('.editor-status')!
    this.statusBar = new StatusBar(statusContainer)
  }

  private setupEventListeners(): void {
    if (!this.instance) return

    // 内容变化监听
    this.instance.listener.contentChange = async () => {
      const wordCount = await this.instance!.command.getWordCount()
      this.statusBar?.updateWordCount(wordCount)
    }

    // 样式变化监听
    this.instance.listener.rangeStyleChange = (styleInfo) => {
      this.toolbarManager?.updateButtonStates(styleInfo)
    }

    // 页面变化监听
    this.instance.listener.visiblePageNoListChange = (pageList) => {
      const currentPage = pageList[0] || 1
      const totalPages = this.instance!.command.getPageCount()
      this.statusBar?.updatePageInfo(currentPage, totalPages)
    }
  }

  private registerCustomFeatures(): void {
    if (!this.instance) return

    // 注册自定义右键菜单
    this.instance.register.contextMenuList([
      {
        key: 'customSave',
        name: '保存文档',
        shortCut: 'Ctrl + S',
        callback: (command) => {
          this.saveDocument()
        }
      }
    ])

    // 注册自定义快捷键
    this.instance.register.shortcutList([
      {
        key: 'S',
        ctrl: true,
        callback: (command) => {
          this.saveDocument()
        }
      }
    ])
  }

  private async saveDocument(): Promise<void> {
    if (!this.instance) return

    try {
      const data = this.instance.command.getValue()
      // 执行保存逻辑
      console.log('保存文档:', data)

      // 可以发送到服务器
      // await fetch('/api/documents/save', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(data)
      // })

    } catch (error) {
      console.error('保存失败:', error)
    }
  }

  public destroy(): void {
    if (this.instance) {
      this.instance.destroy()
      this.instance = null
    }
  }
}

// 使用初始化器
const initializer = new EditorInitializer('editor-container')
const editorInstance = await initializer.initialize()
```

## 📚 接口和类型定义

### 1. 核心接口

```typescript
// 编辑器数据接口
interface IEditorData {
  header?: IElement[]    // 页眉元素列表
  main: IElement[]       // 正文元素列表（必需）
  footer?: IElement[]    // 页脚元素列表
}

// 元素接口
interface IElement {
  id?: string           // 元素唯一标识
  type?: ElementType    // 元素类型
  value: string         // 元素值（必需）

  // 字体样式
  font?: string         // 字体名称
  size?: number         // 字号
  bold?: boolean        // 加粗
  italic?: boolean      // 斜体
  color?: string        // 文字颜色
  highlight?: string    // 高亮颜色

  // 文本装饰
  underline?: boolean   // 下划线
  strikeout?: boolean   // 删除线

  // 段落样式
  rowFlex?: RowFlex     // 行对齐方式
  rowMargin?: number    // 行间距

  // 标题
  title?: TitleLevel    // 标题级别

  // 列表
  listType?: ListType   // 列表类型
  listStyle?: ListStyle // 列表样式
  listWrap?: boolean    // 列表换行

  // 表格相关
  colgroup?: IColgroup  // 列组
  trList?: ITr[]        // 行列表

  // 图片相关
  width?: number        // 宽度
  height?: number       // 高度
  imgValue?: string     // 图片数据
  imgDisplay?: ImageDisplay // 图片显示方式

  // 控件相关
  control?: IControl    // 控件配置
  controlId?: string    // 控件ID

  // 其他
  url?: string          // 链接地址
  hyperlinkId?: string  // 超链接ID
  dateFormat?: string   // 日期格式
}

// 编辑器结果接口
interface IEditorResult {
  version: string       // 编辑器版本
  data: IEditorData     // 文档数据
  options: IEditorOption // 编辑器配置
}
```

### 2. 枚举类型

```typescript
// 编辑器模式
enum EditorMode {
  EDIT = 'edit',        // 编辑模式
  CLEAN = 'clean',      // 清洁模式
  READONLY = 'readonly', // 只读模式
  FORM = 'form',        // 表单模式
  PRINT = 'print',      // 打印模式
  DESIGN = 'design'     // 设计模式
}

// 元素类型
enum ElementType {
  TEXT = 'text',        // 文本
  IMAGE = 'image',      // 图片
  TABLE = 'table',      // 表格
  HYPERLINK = 'hyperlink', // 超链接
  SUPERSCRIPT = 'superscript', // 上标
  SUBSCRIPT = 'subscript',     // 下标
  SEPARATOR = 'separator',     // 分隔符
  PAGE_BREAK = 'pageBreak',    // 分页符
  CONTROL = 'control',         // 控件
  CHECKBOX = 'checkbox',       // 复选框
  RADIO = 'radio',            // 单选框
  LATEX = 'latex',            // LaTeX公式
  DATE = 'date',              // 日期
  BLOCK = 'block'             // 块元素
}

// 行对齐方式
enum RowFlex {
  LEFT = 'left',        // 左对齐
  CENTER = 'center',    // 居中对齐
  RIGHT = 'right',      // 右对齐
  ALIGNMENT = 'alignment' // 两端对齐
}

// 控件类型
enum ControlType {
  TEXT = 'text',        // 文本控件
  SELECT = 'select',    // 选择控件
  CHECKBOX = 'checkbox', // 复选框控件
  RADIO = 'radio',      // 单选框控件
  DATE = 'date'         // 日期控件
}

// 标题级别
enum TitleLevel {
  FIRST = 'first',      // 一级标题
  SECOND = 'second',    // 二级标题
  THIRD = 'third',      // 三级标题
  FOURTH = 'fourth',    // 四级标题
  FIFTH = 'fifth',      // 五级标题
  SIXTH = 'sixth'       // 六级标题
}
```

### 3. 命令接口

```typescript
// 命令系统接口
interface ICommand {
  // 基础操作
  executeUndo(): void
  executeRedo(): void
  executeCut(): void
  executeCopy(): void
  executePaste(): void
  executeSelectAll(): void
  executeBackspace(): void

  // 格式化
  executeBold(): void
  executeItalic(): void
  executeUnderline(): void
  executeStrikeout(): void
  executeFont(font: string): void
  executeSize(size: number): void
  executeColor(color: string): void
  executeHighlight(color: string): void

  // 段落
  executeRowFlex(rowFlex: RowFlex): void
  executeRowMargin(margin: number): void
  executeTitle(level: TitleLevel): void
  executeList(listType: ListType, listStyle?: ListStyle): void

  // 插入
  executeInsertTable(row: number, col: number): void
  executeImage(imageData: IImageData): void
  executeHyperlink(url: string, text?: string): void
  executePageBreak(): void
  executeSeparator(): void
  executeInsertElementList(elementList: IElement[]): void

  // 数据操作
  getValue(): IEditorResult
  setValue(data: IEditorData, options?: ISetValueOption): void
  getHTML(): IEditorHTML
  setHTML(html: Partial<IEditorHTML>): void
  getText(): IEditorText

  // 模式和设置
  executeMode(mode: EditorMode): void
  executePageMode(pageMode: PageMode): void
  executePaperDirection(direction: PaperDirection): void
  executePageScale(scale: number): void

  // 搜索和替换
  executeSearch(keyword: string): void
  executeReplace(newWord: string, option?: IReplaceOption): void

  // 打印
  executePrint(): void

  // 获取信息
  getWordCount(): Promise<number>
  getPageCount(): number
  getOptions(): DeepRequired<IEditorOption>
  getRange(): IRange
  getCursorPosition(): IPositionContext | null
}
```

## 🎯 最佳实践

### 1. 性能优化

```typescript
// 性能优化配置
const performanceOptions: IEditorOption = {
  // 限制历史记录数量
  historyMaxRecordCount: 50,

  // 合理设置页面大小
  width: 794,
  height: 1123,

  // 适当的缩放比例
  scale: 1,

  // 合理的页面间距
  pageGap: 20,

  // 优化渲染模式
  renderMode: RenderMode.LAZY
}

// 大文档处理
class LargeDocumentHandler {
  private editor: Editor

  constructor(editor: Editor) {
    this.editor = editor
  }

  // 分批加载大文档
  async loadLargeDocument(data: IElement[], batchSize: number = 1000): Promise<void> {
    const batches = this.chunkArray(data, batchSize)

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i]

      if (i === 0) {
        // 第一批直接设置
        this.editor.command.setValue({ main: batch })
      } else {
        // 后续批次追加
        this.editor.command.executeInsertElementList(batch)
      }

      // 让出控制权，避免阻塞UI
      await new Promise(resolve => setTimeout(resolve, 0))
    }
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }
}
```

### 2. 错误处理

```typescript
// 错误处理包装器
class EditorErrorHandler {
  private editor: Editor

  constructor(editor: Editor) {
    this.editor = editor
  }

  // 安全执行命令
  async safeExecute<T>(
    operation: () => T,
    fallback?: () => T,
    errorMessage?: string
  ): Promise<T | null> {
    try {
      return operation()
    } catch (error) {
      console.error(errorMessage || '编辑器操作失败:', error)

      if (fallback) {
        try {
          return fallback()
        } catch (fallbackError) {
          console.error('回退操作也失败:', fallbackError)
        }
      }

      return null
    }
  }

  // 数据验证
  validateData(data: IEditorData): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!data.main || !Array.isArray(data.main)) {
      errors.push('main字段必须是数组')
    }

    if (data.header && !Array.isArray(data.header)) {
      errors.push('header字段必须是数组')
    }

    if (data.footer && !Array.isArray(data.footer)) {
      errors.push('footer字段必须是数组')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}

// 使用错误处理
const errorHandler = new EditorErrorHandler(instance)

// 安全设置数据
const result = await errorHandler.safeExecute(
  () => {
    const validation = errorHandler.validateData(newData)
    if (!validation.valid) {
      throw new Error('数据验证失败: ' + validation.errors.join(', '))
    }
    instance.command.setValue(newData)
    return true
  },
  () => {
    console.log('使用默认数据')
    instance.command.setValue({ main: [{ value: '默认内容' }] })
    return false
  },
  '设置编辑器数据失败'
)
```

### 3. 内存管理

```typescript
// 内存管理工具
class MemoryManager {
  private editor: Editor
  private autoSaveTimer: NodeJS.Timeout | null = null

  constructor(editor: Editor) {
    this.editor = editor
    this.setupAutoCleanup()
  }

  private setupAutoCleanup(): void {
    // 定期清理
    setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000) // 每5分钟清理一次

    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
      this.destroy()
    })
  }

  private cleanup(): void {
    // 清理历史记录
    const historyCount = this.editor.command.getOptions().historyMaxRecordCount
    if (historyCount && historyCount > 50) {
      // 如果历史记录过多，建议减少
      console.warn('历史记录过多，建议减少 historyMaxRecordCount')
    }

    // 强制垃圾回收（仅在开发环境）
    if (typeof window.gc === 'function') {
      window.gc()
    }
  }

  public destroy(): void {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer)
      this.autoSaveTimer = null
    }

    // 销毁编辑器
    this.editor.destroy()
  }
}
```

## 📋 总结

Canvas Editor 提供了完整的富文本编辑解决方案，具有以下特点：

### 🎯 核心优势
- **🎨 基于Canvas**: 完全自主的渲染控制
- **📱 跨平台**: 支持桌面和移动端
- **🔧 高度可配置**: 丰富的配置选项
- **🔌 插件化**: 支持功能扩展
- **⚡ 高性能**: 优化的渲染机制

### 🛠️ 主要功能
- **📝 富文本编辑**: 完整的文本格式化功能
- **📊 表格支持**: 复杂表格编辑
- **🖼️ 图片处理**: 图片插入和编辑
- **🎮 控件系统**: 表单控件支持
- **📄 多页面**: 分页和连续页模式
- **🖨️ 打印支持**: 完整的打印功能

### 💡 使用建议
- **合理配置**: 根据需求选择合适的配置选项
- **性能优化**: 注意大文档的处理方式
- **错误处理**: 实现完善的错误处理机制
- **内存管理**: 及时清理资源避免内存泄漏
- **用户体验**: 提供友好的用户界面和交互

通过本指南，开发者可以快速上手Canvas Editor，并根据具体需求进行定制和扩展。
