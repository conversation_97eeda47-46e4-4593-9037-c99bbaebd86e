import { CanvasEditor } from '../../editor'
import html from './HomeTableButton.html'
import './HomeTableButton.css'

export class HomeTableButton {
  private dom: HTMLDivElement
  private tablePanelContainer: HTMLDivElement
  private instance: CanvasEditor
  private colIndex = 0
  private rowIndex = 0

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.tablePanelContainer = this.dom.querySelector<HTMLDivElement>('.table-panel')!
    
    this.bindEvents()
  }

  private bindEvents(): void {
    // 点击表格按钮显示面板
    this.dom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      
      // 先隐藏所有其他的下拉框
      this.hideAllDropdowns()
      
      // 显示当前面板并定位
      this.showPanel()
    }

    // 表格面板事件处理
    const tableContainer = this.tablePanelContainer.querySelector<HTMLDivElement>('.table-container')!
    
    // 创建表格选择网格
    this.createTableGrid(tableContainer)

    // 点击外部关闭面板
    document.addEventListener('click', (e) => {
      const target = e.target as Node
      if (!this.dom.contains(target) && !this.tablePanelContainer.contains(target)) {
        this.hidePanel()
      }
    })
  }

  private createTableGrid(container: HTMLDivElement): void {
    // 创建10x8的表格选择网格
    for (let row = 0; row < 8; row++) {
      for (let col = 0; col < 10; col++) {
        const cell = document.createElement('div')
        cell.className = 'table-cell'
        cell.dataset.row = row.toString()
        cell.dataset.col = col.toString()
        
        cell.onmouseenter = () => {
          this.highlightCells(col, row)
        }
        
        cell.onclick = (e) => {
          e.stopPropagation()
          this.insertTable(col + 1, row + 1)
          this.hidePanel()
        }
        
        container.appendChild(cell)
      }
    }
  }

  private highlightCells(col: number, row: number): void {
    const cells = this.tablePanelContainer.querySelectorAll('.table-cell')
    cells.forEach((cell, index) => {
      const cellRow = Math.floor(index / 10)
      const cellCol = index % 10
      
      if (cellCol <= col && cellRow <= row) {
        cell.classList.add('highlighted')
      } else {
        cell.classList.remove('highlighted')
      }
    })
    
    // 更新显示的行列数
    const info = this.tablePanelContainer.querySelector('.table-info')!
    info.textContent = `${col + 1} x ${row + 1} 表格`
  }

  private insertTable(cols: number, rows: number): void {
    this.instance.command.executeInsertTable(rows, cols)
  }

  // 显示面板并定位到按钮下方
  private showPanel(): void {
    // 先设置基本样式
    this.tablePanelContainer.style.position = 'fixed'
    this.tablePanelContainer.style.zIndex = '999999'
    this.tablePanelContainer.style.display = 'block'
    
    // 立即计算位置
    this.positionPanel()
  }

  // 精确定位面板到按钮下方
  private positionPanel(): void {
    const rect = this.dom.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    // 默认位置：按钮下方
    let left = rect.left
    let top = rect.bottom + 4
    
    // 水平边界检查（表格面板宽度约250px）
    if (left + 250 > viewportWidth) {
      left = viewportWidth - 250 - 10
    }
    if (left < 10) {
      left = 10
    }
    
    // 垂直边界检查（表格面板高度约200px）
    if (top + 200 > viewportHeight) {
      top = rect.top - 200 - 4
    }
    if (top < 10) {
      top = 10
    }
    
    // 应用位置
    this.tablePanelContainer.style.left = left + 'px'
    this.tablePanelContainer.style.top = top + 'px'
  }

  // 隐藏面板
  private hidePanel(): void {
    this.tablePanelContainer.style.display = 'none'
  }

  // 隐藏所有下拉框（避免多个下拉框同时显示）
  private hideAllDropdowns(): void {
    const allDropdowns = document.querySelectorAll('.options.visible, .table-panel[style*="block"]')
    allDropdowns.forEach(dropdown => {
      if (dropdown.classList.contains('visible')) {
        dropdown.classList.remove('visible')
      } else {
        (dropdown as HTMLElement).style.display = 'none'
      }
    })
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}
