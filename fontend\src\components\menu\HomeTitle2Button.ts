import { TitleLevel } from '../../editor/dataset/enum/Title'
import { CanvasEditor } from '../../editor'
import html from './HomeTitle2Button.html'

/**
 * 开始菜单标题2按钮组件
 * 用于将选中文本设置为标题2样式
 */
export class HomeTitle2Button {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.bindEvents()
  }

  /**
   * 绑定事件处理器
   */
  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      
      // 执行设置标题2命令
      this.instance.command.executeTitle(TitleLevel.SECOND)
      
      // 更新按钮状态
      this.updateActiveState()
    }
  }

  /**
   * 更新按钮的激活状态
   */
  private updateActiveState(): void {
    // 移除所有标题按钮的激活状态
    const allTitleButtons = document.querySelectorAll('[class*="menu-item__title"], [class*="menu-item__home-title"], .menu-item__normal-text, .menu-item__home-normal-text')
    allTitleButtons.forEach(btn => btn.classList.remove('active'))
    
    // 激活当前按钮
    this.dom.classList.add('active')
  }

  /**
   * 获取DOM元素
   */
  public getElement(): HTMLDivElement {
    return this.dom
  }
}
