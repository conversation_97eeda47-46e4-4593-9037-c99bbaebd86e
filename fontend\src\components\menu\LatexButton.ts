import { CanvasEditor } from '../../editor'
import { ElementType } from '../../editor'
import { Dialog } from '../dialog/Dialog'
import html from './LatexButton.html'
import './LatexButton.css'

export class LatexButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement

    // 添加样式确保按钮可见
    this.dom.style.cursor = 'pointer'
    this.dom.setAttribute('title', 'LaTeX公式')

    // 确保绑定事件
    this.bindEvents()


    // 为现有按钮添加增强点击事件支持
    this.enhanceExistingButton()
  }

  private bindEvents(): void {
    // 使用箭头函数绑定上下文
    this.dom.addEventListener('click', (e) => {
      console.log('LaTeX按钮被点击')
      e.preventDefault()
      e.stopPropagation()
      this.openLatexDialog()
    })
  }

  /**
   * 增强现有DOM中的LaTeX按钮，确保可点击性
   * 从debug-helper.ts移植而来的功能
   */
  private enhanceExistingButton(): void {
    // 延迟执行，确保DOM已完全加载
    setTimeout(() => {
      // 查找LaTeX按钮
      const latexBtn = document.querySelector('.menu-item__latex')


      if (latexBtn) {
        // 确保按钮可交互，设置高z-index
        latexBtn.setAttribute('style', 'z-index: 9999 !important;')

        // 直接绑定点击事件
        latexBtn.addEventListener('click', (e) => {
          console.log('LaTeX按钮被直接点击')
          e.preventDefault()
          e.stopPropagation()

          // 尝试通过API调用打开LaTeX编辑器
          try {
            const editorInstance = (window as any).editor
            if (editorInstance && editorInstance.command) {
              // 触发原生的LaTeX编辑功能
              if (editorInstance.latexEditor) {
                // 如果有专门的LaTeX编辑器方法，调用它
                editorInstance.latexEditor.open()
              } else {
                // 否则按原有逻辑插入，但不提供默认值
                editorInstance.command.executeInsertElementList([
                  {
                    type: 16, // ElementType.LATEX
                    value: ''
                  }
                ])
              }
              console.log('成功打开LaTeX编辑器')
            } else {
              console.error('找不到编辑器实例或command对象')
            }
          } catch (error) {
            console.error('打开LaTeX编辑器失败:', error)
          }
        })


      }
    }, 2000)
  }

  private openLatexDialog(): void {
    console.log('打开LaTeX对话框')
    new Dialog({
      title: 'LaTeX公式',
      data: [
        {
          type: 'textarea',
          height: 100,
          name: 'value',
          placeholder: '请输入LaTeX公式，例如：E = mc^2'
        }
      ],
      onConfirm: payload => {
        const value = payload.find(p => p.name === 'value')?.value
        if (!value) return
        console.log('插入LaTeX元素:', value)

        this.instance.command.executeInsertElementList([
          {
            type: ElementType.LATEX,
            value
          }
        ])
      }
    })
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
}