import { CanvasEditor } from '../../editor'
import { PaperDirection } from '../../editor'
import html from './PaperDirectionButton.html'
import './PaperDirectionButton.css'

export class PaperDirectionButton {
  private dom: HTMLDivElement
  private paperDirectionOptionsDom: HTMLDivElement
  private instance: CanvasEditor
  private documentClickHandler: (e: MouseEvent) => void

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.paperDirectionOptionsDom = this.dom.querySelector<HTMLDivElement>('.options')!
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      this.paperDirectionOptionsDom.classList.toggle('visible')
    }
    
    this.paperDirectionOptionsDom.onclick = (evt) => {
      evt.stopPropagation() // 阻止事件冒泡
      const li = evt.target as HTMLLIElement
      if (li.tagName === 'LI') {
        const paperDirection = li.dataset.paperDirection!
        this.instance.command.executePaperDirection(<PaperDirection>paperDirection)
        
        // 更新活动状态
        this.paperDirectionOptionsDom.querySelectorAll('li').forEach(item => {
          item.classList.remove('active')
        })
        li.classList.add('active')
        
        // 选择后关闭下拉框
        this.paperDirectionOptionsDom.classList.remove('visible')
      }
    }
    
    // 点击外部关闭下拉框
    this.documentClickHandler = (e) => {
      if (!this.dom.contains(e.target as Node)) {
        this.paperDirectionOptionsDom.classList.remove('visible')
      }
    }
    document.addEventListener('click', this.documentClickHandler)
  }
  
  // 销毁组件时移除全局事件监听
  public destroy(): void {
    document.removeEventListener('click', this.documentClickHandler)
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 