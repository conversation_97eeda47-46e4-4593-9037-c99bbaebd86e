import { CanvasEditor } from '../../editor'
import html from './PageBreakButton.html'
import './PageBreakButton.css'

export class PageBreakButton {
  private dom: HTMLDivElement
  private instance: CanvasEditor

  constructor(instance: CanvasEditor) {
    this.instance = instance
    this.dom = document.createElement('div')
    this.dom.innerHTML = html
    this.dom = this.dom.firstElementChild as HTMLDivElement
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.dom.onclick = () => {
      this.instance.command.executePageBreak()
    }
  }

  public getElement(): HTMLDivElement {
    return this.dom
  }
} 