/**
 * API 配置文件
 * 定义API基础URL、端点和相关配置
 */

// 环境配置
export const ENV = {
  // 开发环境
  DEVELOPMENT: 'development',
  // 生产环境
  PRODUCTION: 'production'
} as const

// 当前环境
export const CURRENT_ENV = process.env.NODE_ENV || ENV.DEVELOPMENT

// API基础配置
export const API_CONFIG = {
  // 开发环境API地址（使用Vite代理）
  DEVELOPMENT: {
    BASE_URL: '', // 空字符串表示使用当前域名，通过Vite代理转发
    API_PREFIX: '/api'
  },
  // 生产环境API地址
  PRODUCTION: {
    BASE_URL: 'https://your-production-domain.com',
    API_PREFIX: '/api'
  }
} as const

// 获取当前环境的API配置
export const getCurrentApiConfig = () => {
  return CURRENT_ENV === ENV.PRODUCTION
    ? API_CONFIG.PRODUCTION
    : API_CONFIG.DEVELOPMENT
}

// API端点定义
export const API_ENDPOINTS = {
  // 健康检查
  HEALTH: '/health/',

  // 文档管理
  DOCUMENTS: '/documents/',
  DOCUMENT_DETAIL: (id: string | number) => `/documents/${id}/`,
  DOCUMENT_VERSIONS: (id: string | number) => `/documents/${id}/versions/`,

  // 用户认证（如果需要）
  AUTH: {
    LOGIN: '/auth/login/',
    LOGOUT: '/auth/logout/',
    REGISTER: '/auth/register/',
    PROFILE: '/auth/profile/'
  },

  // 文件上传
  UPLOAD: {
    IMAGE: '/upload/image/',
    DOCUMENT: '/upload/document/',
    ATTACHMENT: '/upload/attachment/'
  }
} as const

// HTTP请求配置
export const HTTP_CONFIG = {
  // 请求超时时间（毫秒）
  TIMEOUT: 30000,

  // 默认请求头
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },

  // 文件上传请求头
  UPLOAD_HEADERS: {
    'Accept': 'application/json'
    // Content-Type 会自动设置为 multipart/form-data
  }
} as const

// 错误码定义
export const ERROR_CODES = {
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  // 请求超时
  TIMEOUT: 'TIMEOUT',
  // 服务器错误
  SERVER_ERROR: 'SERVER_ERROR',
  // 认证失败
  UNAUTHORIZED: 'UNAUTHORIZED',
  // 权限不足
  FORBIDDEN: 'FORBIDDEN',
  // 资源不存在
  NOT_FOUND: 'NOT_FOUND',
  // 请求参数错误
  BAD_REQUEST: 'BAD_REQUEST',
  // 未知错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const

// HTTP状态码映射
export const HTTP_STATUS_CODES = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
} as const

// 获取完整的API URL
export const getApiUrl = (endpoint: string): string => {
  const config = getCurrentApiConfig()
  const baseUrl = config.BASE_URL + config.API_PREFIX

  // 确保endpoint以/开头
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`

  return baseUrl + normalizedEndpoint
}

// 调试模式
export const DEBUG_MODE = CURRENT_ENV === ENV.DEVELOPMENT

// API调用日志配置
export const LOG_CONFIG = {
  // 是否启用请求日志
  ENABLE_REQUEST_LOG: DEBUG_MODE,
  // 是否启用响应日志
  ENABLE_RESPONSE_LOG: DEBUG_MODE,
  // 是否启用错误日志
  ENABLE_ERROR_LOG: true
} as const
