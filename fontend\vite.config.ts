import { defineConfig } from 'vite'
import typescript from '@rollup/plugin-typescript'
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js'
import * as path from 'path'

export default defineConfig(({ mode }) => {
  const name = 'Book-Editor'
  if (mode === 'lib') {
    return {
      plugins: [
        cssInjectedByJsPlugin({
          styleId: `${name}-style`,
          topExecutionPriority: true
        }),

        {
          ...typescript({
            tsconfig: './tsconfig.json',
            include: ['./src/editor/**']
          }),
          apply: 'build',
          declaration: true,
          declarationDir: 'types/',
          rootDir: '/'
        }
      ],
      build: {
        lib: {
          name,
          fileName: name,
          entry: path.resolve(__dirname, 'src/editor/index.ts')
        },
        rollupOptions: {
          output: {
            sourcemap: true
          }
        }
      }
    }
  }
  return {
    base: `/${name}/`,
    server: {
      host: '127.0.0.1',
      port: 5173,
      // 配置API代理，将前端API请求代理到Django后端
      proxy: {
        '/api': {
          target: 'http://127.0.0.1:8000',
          changeOrigin: true,
          secure: false,
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.log('proxy error', err)
            })
            proxy.on('proxyReq', (proxyReq, req, _res) => {
              console.log('Sending Request to the Target:', req.method, req.url)
            })
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log('Received Response from the Target:', proxyRes.statusCode, req.url)
            })
          }
        }
      },
      // 配置路由重写，将 /Book-Editor/API 重定向到 /Book-Editor/API.html
      middlewareMode: false,
      fs: {
        strict: false
      }
    },
    plugins: [
      {
        name: 'vite-plugin-html-import',
        transform(code, id) {
          // 处理HTML导入
          if (id.endsWith('.html')) {
            const html = code.trim()
            return {
              code: `export default ${JSON.stringify(html)};`,
              map: null
            }
          }
        }
      },
      {
        name: 'vite-plugin-custom-routes',
        configureServer(server) {
          server.middlewares.use((req, res, next) => {
            // 处理API测试页面路由
            if (req.url === '/Book-Editor/API') {
              req.url = '/Book-Editor/API.html'
            }
            // 处理登录页面路由
            else if (req.url === '/login/' || req.url === '/login') {
              req.url = '/login.html'
            }
            next()
          })
        }
      }
    ]
  }
})
