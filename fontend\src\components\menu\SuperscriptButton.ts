import './SuperscriptButton.css'

export class SuperscriptButton {
  private element: HTMLDivElement;
  private command: any;
  private isApple: boolean;

  constructor(container: HTMLElement, command: any) {
    this.command = command
    this.isApple = typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent)
    
    // 创建按钮元素
    container.innerHTML = this.render()
    this.element = container.querySelector('.superscript-button') as HTMLDivElement
    
    // 绑定事件
    this.bindEvents()
  }

  private render(): string {
    return `<div class="superscript-button" title="上标(${this.isApple ? '⌘' : 'Ctrl'}+Shift+,)">
      <i></i>
    </div>`
  }

  private bindEvents(): void {
    this.element.onclick = () => {
      console.log('superscript')
      this.command.executeSuperscript()
    }
  }

  // 更新按钮状态
  public updateState(isSuperscript: boolean): void {
    if (isSuperscript) {
      this.element.classList.add('active')
    } else {
      this.element.classList.remove('active')
    }
  }
} 